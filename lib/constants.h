#ifndef CONSTANTS_H
#define CONSTANTS_H

#include <stddef.h>
#include <stdint.h>

// SysEx protocol constants
#define SYSEX_START_BYTE        0xF0
#define SYSEX_END_BYTE          0xF7
#define SYSEX_PADDING_BYTE      0x20
#define SYSEX_CHUNK_SIZE        255

// DROID-specific SysEx header (including start byte, like original)
static const uint8_t DROID_SYSEX_HEADER[] = {0xF0, 0x00, 0x66, 0x66, 0x50};
#define DROID_SYSEX_HEADER_SIZE (sizeof(DROID_SYSEX_HEADER))

// MIDI client and port names
#define MIDI_CLIENT_NAME        "DroidSysExClient"
#define MIDI_OUTPUT_PORT_NAME   "DroidSysExOut"

// Default MIDI device name to search for
#define DEFAULT_MIDI_DEVICE     "DROID X7 MIDI"

// Buffer and file size limits
#define MAX_FILE_SIZE           (10 * 1024 * 1024)  // 10MB max file size
#define MAX_DEVICE_NAME_LENGTH  256
#define MAX_PATH_LENGTH         1024

// Application metadata
#define APP_NAME                "droid-upload"
#define APP_VERSION             "1.0.0"

#endif // CONSTANTS_H
