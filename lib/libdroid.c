#include "libdroid.h"
#include "types.h"
#include "constants.h"
#include "file_utils.h"
#include "sysex.h"
#include "midi.h"
#include <stdlib.h>
#include <string.h>
#include <stdio.h>

#define DROID_LIB_VERSION "1.0.0"

// Helper function to create error result
static DroidLibResult* create_error_result(DroidLibErrorCode error_code, const char* message) {
    DroidLibResult* result = malloc(sizeof(DroidLibResult));
    if (!result) return NULL;

    result->error_code = error_code;
    result->error_message = message ? strdup(message) : NULL;
    result->data = NULL;
    result->data_size = 0;

    return result;
}

// Helper function to create success result
static DroidLibResult* create_success_result(void* data, size_t size) {
    DroidLibResult* result = malloc(sizeof(DroidLibResult));
    if (!result) return NULL;

    result->error_code = DROID_LIB_SUCCESS;
    result->error_message = NULL;
    result->data = data;
    result->data_size = size;

    return result;
}

// Helper function to validate common input parameters
static DroidLibResult* validate_filename(const char* filename) {
    if (!filename || strlen(filename) == 0) {
        return create_error_result(DROID_LIB_ERROR_INVALID_ARGS, "Filename cannot be NULL or empty");
    }

    if (strlen(filename) >= MAX_PATH_LENGTH) {
        return create_error_result(DROID_LIB_ERROR_INVALID_ARGS, "Filename path too long");
    }

    return NULL; // Success - no error
}

// Helper function to validate buffer parameters
static DroidLibResult* validate_buffer_params(const uint8_t* data, size_t size) {
    if (!data && size > 0) {
        return create_error_result(DROID_LIB_ERROR_INVALID_ARGS, "Data buffer cannot be NULL when size > 0");
    }

    if (size > MAX_FILE_SIZE) {
        return create_error_result(DROID_LIB_ERROR_INVALID_ARGS, "Buffer size exceeds maximum allowed");
    }

    return NULL; // Success - no error
}

// Convert internal error codes to library error codes
static DroidLibErrorCode convert_error_code(DroidError error) {
    switch (error) {
        case DROID_SUCCESS:
            return DROID_LIB_SUCCESS;
        case DROID_ERROR_FILE_NOT_FOUND:
            return DROID_LIB_ERROR_FILE_NOT_FOUND;
        case DROID_ERROR_FILE_READ_FAILED:
            return DROID_LIB_ERROR_FILE_READ_FAILED;
        case DROID_ERROR_MIDI_DEVICE_NOT_FOUND:
            return DROID_LIB_ERROR_DEVICE_NOT_FOUND;
        case DROID_ERROR_MIDI_SEND_FAILED:
            return DROID_LIB_ERROR_MIDI_FAILED;
        case DROID_ERROR_MEMORY_ALLOCATION:
            return DROID_LIB_ERROR_MEMORY_ALLOCATION;
        case DROID_ERROR_INVALID_ARGS:
            return DROID_LIB_ERROR_INVALID_ARGS;
        default:
            return DROID_LIB_ERROR_MIDI_FAILED;
    }
}

DroidLibResult* droid_upload_file(const char* filename, const char* device_name,
                                  int verbose, int dry_run) {
    DroidLibResult* validation_error = validate_filename(filename);
    if (validation_error) {
        return validation_error;
    }

    // Read the file
    Result file_result = file_read_all(filename);
    if (DROID_IS_ERROR(file_result)) {
        DroidLibErrorCode error_code = convert_error_code(file_result.error);
        const char* message = (error_code == DROID_LIB_ERROR_FILE_NOT_FOUND)
                             ? "Configuration file not found"
                             : "Failed to read configuration file";
        return create_error_result(error_code, message);
    }

    Buffer* file_buffer = (Buffer*)file_result.data;

    // Upload the buffer
    DroidLibResult* result = droid_upload_buffer(file_buffer->data, file_buffer->size,
                                                 device_name, verbose, dry_run);

    // Clean up file buffer
    buffer_free(file_buffer);
    free(file_buffer);

    return result;
}

DroidLibResult* droid_upload_buffer(const uint8_t* data, size_t size,
                                    const char* device_name, int verbose, int dry_run) {
    DroidLibResult* validation_error = validate_buffer_params(data, size);
    if (validation_error) {
        return validation_error;
    }

    if (verbose) {
        printf("File size: %zu bytes\n", size);
        printf("Preparing SysEx message...\n");
    }

    // Prepare SysEx message
    Result sysex_result = sysex_prepare_message(data, size);
    if (DROID_IS_ERROR(sysex_result)) {
        return create_error_result(DROID_LIB_ERROR_SYSEX_ENCODING, "Failed to encode SysEx message");
    }

    Buffer* sysex_buffer = (Buffer*)sysex_result.data;

    if (verbose) {
        printf("SysEx message size: %zu bytes\n", sysex_buffer->size);
    }

    if (dry_run) {
        if (verbose) {
            printf("Dry run: SysEx message prepared successfully (not sending)\n");
        }
        buffer_free(sysex_buffer);
        free(sysex_buffer);
        return create_success_result(NULL, 0);
    }

    // Send via MIDI - we need to find the device first, then send
    if (verbose) {
        printf("Looking for MIDI device: %s\n", device_name ? device_name : "DROID X7 MIDI");
    }

    MIDIEndpointRef endpoint = midi_find_destination(device_name ? device_name : "DROID X7 MIDI");
    if (endpoint == 0) {
        buffer_free(sysex_buffer);
        free(sysex_buffer);
        return create_error_result(DROID_LIB_ERROR_DEVICE_NOT_FOUND, "MIDI device not found");
    }

    if (verbose) {
        printf("Found device: %s\n", device_name ? device_name : "DROID X7 MIDI");
        printf("Sending SysEx message...\n");
    }
    DroidError send_error = midi_send_sysex(endpoint, sysex_buffer->data, sysex_buffer->size, 5.0);

    // Clean up SysEx buffer
    buffer_free(sysex_buffer);
    free(sysex_buffer);

    if (send_error != DROID_SUCCESS) {
        DroidLibErrorCode error_code = convert_error_code(send_error);
        const char* message = (error_code == DROID_LIB_ERROR_DEVICE_NOT_FOUND)
                             ? "MIDI device not found"
                             : "Failed to send MIDI message";
        return create_error_result(error_code, message);
    }

    if (verbose) {
        printf("SysEx message sent and completed successfully.\n");
    }

    return create_success_result(NULL, 0);
}

// Helper function to check if device name already exists in list
static bool device_name_exists(DroidDevice *devices, int count, const char *name) {
    for (int i = 0; i < count; i++) {
        if (devices[i].name && strcmp(devices[i].name, name) == 0) {
            return true;
        }
    }
    return false;
}

// Helper function to check if device is online using kMIDIPropertyOffline
static bool is_device_online(MIDIDeviceRef device, ItemCount destination_count, ItemCount source_count) {
    (void)destination_count; // Suppress unused parameter warning
    (void)source_count;      // Suppress unused parameter warning

    // Check device offline property directly
    SInt32 offline = 0;
    OSStatus status = MIDIObjectGetIntegerProperty(device, kMIDIPropertyOffline, &offline);

    if (status != noErr || offline != 0) {
        return false;
    }

    return true;
}

// Helper function to add device to list if not duplicate
static bool add_device_if_unique(DroidDeviceList *device_list, int *device_index,
                                const char *name, bool is_active) {
    if (device_name_exists(device_list->devices, *device_index, name)) {
        return false; // Duplicate found
    }

    device_list->devices[*device_index].name = strdup(name);
    device_list->devices[*device_index].id = *device_index;
    device_list->devices[*device_index].is_active = is_active ? 1 : 0;
    (*device_index)++;
    return true;
}

// Core enumeration function that can be used by both public functions
static DroidDeviceList* enumerate_devices_internal(bool active_only) {
    DroidDeviceList* device_list = malloc(sizeof(DroidDeviceList));
    if (!device_list) {
        return NULL;
    }

    // Initialize the device list
    device_list->error_code = DROID_LIB_SUCCESS;
    device_list->error_message = NULL;
    device_list->devices = NULL;
    device_list->device_count = 0;

    // Get counts
    ItemCount device_count = MIDIGetNumberOfDevices();
    ItemCount destination_count = MIDIGetNumberOfDestinations();
    ItemCount source_count = MIDIGetNumberOfSources();
    ItemCount total_count = device_count + destination_count + source_count;

    if (total_count == 0) {
        return device_list; // Return empty list
    }

    // Allocate memory for devices (estimate max needed)
    device_list->devices = malloc(sizeof(DroidDevice) * total_count);
    if (!device_list->devices) {
        device_list->error_code = DROID_LIB_ERROR_MEMORY_ALLOCATION;
        device_list->error_message = strdup("Memory allocation failed");
        return device_list;
    }

    int device_index = 0;

    // Add known MIDI devices (includes offline devices if not active_only)
    if (!active_only) {
        for (ItemCount i = 0; i < device_count; i++) {
            MIDIDeviceRef device = MIDIGetDevice(i);
            if (device == 0) continue;

            char device_name[MAX_DEVICE_NAME_LENGTH];
            CFStringRef cf_name = NULL;
            OSStatus status = MIDIObjectGetStringProperty(device, kMIDIPropertyName, &cf_name);

            if (status == noErr && cf_name) {
                Boolean success = CFStringGetCString(cf_name, device_name, sizeof(device_name), kCFStringEncodingUTF8);
                CFRelease(cf_name);

                if (success) {
                    bool is_online = is_device_online(device, destination_count, source_count);
                    add_device_if_unique(device_list, &device_index, device_name, is_online);
                }
            }
        }
    }

    // Add active endpoints (destinations)
    for (ItemCount i = 0; i < destination_count; i++) {
        MIDIEndpointRef endpoint = MIDIGetDestination(i);
        if (endpoint == 0) continue;

        char endpoint_name[MAX_DEVICE_NAME_LENGTH];
        if (midi_get_endpoint_name(endpoint, endpoint_name, sizeof(endpoint_name)) == DROID_SUCCESS) {
            add_device_if_unique(device_list, &device_index, endpoint_name, true);
        }
    }

    // Add active endpoints (sources)
    for (ItemCount i = 0; i < source_count; i++) {
        MIDIEndpointRef endpoint = MIDIGetSource(i);
        if (endpoint == 0) continue;

        char endpoint_name[MAX_DEVICE_NAME_LENGTH];
        if (midi_get_endpoint_name(endpoint, endpoint_name, sizeof(endpoint_name)) == DROID_SUCCESS) {
            add_device_if_unique(device_list, &device_index, endpoint_name, true);
        }
    }

    device_list->device_count = device_index;
    return device_list;
}

DroidDeviceList* droid_list_devices(void) {
    return enumerate_devices_internal(false);
}

DroidDeviceList* droid_list_devices_with_options(int active_only) {
    return enumerate_devices_internal(active_only != 0);
}

DroidLibResult* droid_validate_file(const char* filename) {
    DroidLibResult* validation_error = validate_filename(filename);
    if (validation_error) {
        return validation_error;
    }

    // Read the file
    Result file_result = file_read_all(filename);
    if (DROID_IS_ERROR(file_result)) {
        DroidLibErrorCode error_code = convert_error_code(file_result.error);
        const char* message = (error_code == DROID_LIB_ERROR_FILE_NOT_FOUND)
                             ? "Configuration file not found"
                             : "Failed to read configuration file";
        return create_error_result(error_code, message);
    }

    Buffer* file_buffer = (Buffer*)file_result.data;

    // Validate the buffer
    DroidLibResult* result = droid_validate_buffer(file_buffer->data, file_buffer->size);

    // Clean up file buffer
    buffer_free(file_buffer);
    free(file_buffer);

    return result;
}

DroidLibResult* droid_validate_buffer(const uint8_t* data, size_t size) {
    DroidLibResult* validation_error = validate_buffer_params(data, size);
    if (validation_error) {
        return validation_error;
    }

    // Basic validation - check if we can create a SysEx message
    Result sysex_result = sysex_prepare_message(data, size);
    if (DROID_IS_ERROR(sysex_result)) {
        return create_error_result(DROID_LIB_ERROR_INVALID_CONFIG, "Invalid configuration format");
    }

    Buffer* sysex_buffer = (Buffer*)sysex_result.data;

    // Clean up
    buffer_free(sysex_buffer);
    free(sysex_buffer);

    return create_success_result(NULL, 0);
}

int droid_is_device_connected(const char* device_name) {
    const char* target_device = device_name ? device_name : "DROID X7 MIDI";

    ItemCount destination_count = MIDIGetNumberOfDestinations();

    for (ItemCount i = 0; i < destination_count; i++) {
        MIDIEndpointRef endpoint = MIDIGetDestination(i);
        if (endpoint == 0) continue;

        char endpoint_name[MAX_DEVICE_NAME_LENGTH];
        if (midi_get_endpoint_name(endpoint, endpoint_name, sizeof(endpoint_name)) == DROID_SUCCESS) {
            if (strstr(endpoint_name, target_device) != NULL) {
                if (midi_validate_endpoint(endpoint) == DROID_SUCCESS) {
                    return 1; // Device found and accessible
                }
            }
        }
    }

    return 0; // Device not found or not accessible
}

const char* droid_get_version(void) {
    return DROID_LIB_VERSION;
}

void droid_free_result(DroidLibResult* result) {
    if (!result) return;

    if (result->error_message) {
        free(result->error_message);
    }
    if (result->data) {
        free(result->data);
    }
    free(result);
}

void droid_free_device_list(DroidDeviceList* device_list) {
    if (!device_list) return;

    if (device_list->error_message) {
        free(device_list->error_message);
    }

    if (device_list->devices) {
        for (int i = 0; i < device_list->device_count; i++) {
            if (device_list->devices[i].name) {
                free(device_list->devices[i].name);
            }
        }
        free(device_list->devices);
    }

    free(device_list);
}
