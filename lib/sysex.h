#ifndef SYSEX_H
#define SYSEX_H

#include "types.h"

/**
 * Encodes data with padding bytes inserted at regular intervals.
 * This is required for the DROID SysEx protocol which needs padding
 * every SYSEX_CHUNK_SIZE bytes.
 * 
 * @param input Input data to encode
 * @param input_size Size of input data
 * @param chunk_size Number of bytes before inserting padding (default: SYSEX_CHUNK_SIZE)
 * @param padding_byte Byte value to use for padding (default: SYSEX_PADDING_BYTE)
 * @return Result containing encoded buffer on success, error on failure
 */
Result sysex_encode_with_padding(const uint8_t *input, size_t input_size, 
                                size_t chunk_size, uint8_t padding_byte);

/**
 * Prepares a complete SysEx message with header, encoded data, and footer.
 * This function:
 * 1. Combines the DROID SysEx header with the input data
 * 2. Encodes the combined data with padding
 * 3. Wraps the result with SysEx start (0xF0) and end (0xF7) bytes
 * 
 * @param data Input data (typically INI file contents)
 * @param data_size Size of input data
 * @return Result containing complete SysEx message on success, error on failure
 */
Result sysex_prepare_message(const uint8_t *data, size_t data_size);

/**
 * Validates that a buffer contains a valid SysEx message.
 * Checks for proper start/end bytes and reasonable size limits.
 * 
 * @param sysex_data SysEx message data to validate
 * @param sysex_size Size of SysEx message
 * @return DROID_SUCCESS if valid, appropriate error code otherwise
 */
DroidError sysex_validate_message(const uint8_t *sysex_data, size_t sysex_size);

/**
 * Calculates the expected size of a SysEx message after encoding.
 * Useful for pre-allocating buffers.
 * 
 * @param input_size Size of input data before encoding
 * @param chunk_size Chunk size for padding calculation
 * @return Expected size after encoding with header, padding, and footer
 */
size_t sysex_calculate_encoded_size(size_t input_size, size_t chunk_size);

/**
 * Creates a SysEx message with custom header bytes.
 * More flexible version of sysex_prepare_message for different protocols.
 * 
 * @param header Custom header bytes (excluding 0xF0 start byte)
 * @param header_size Size of custom header
 * @param data Input data to encode
 * @param data_size Size of input data
 * @param chunk_size Chunk size for padding
 * @param padding_byte Padding byte value
 * @return Result containing complete SysEx message on success, error on failure
 */
Result sysex_prepare_custom_message(const uint8_t *header, size_t header_size,
                                   const uint8_t *data, size_t data_size,
                                   size_t chunk_size, uint8_t padding_byte);

#endif // SYSEX_H
