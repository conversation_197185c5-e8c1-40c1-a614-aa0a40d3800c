#ifndef MIDI_H
#define MIDI_H

#include "types.h"
#include <CoreMIDI/CoreMIDI.h>

/**
 * Initializes a MIDI context with client and output port.
 * This must be called before any other MIDI operations.
 * 
 * @param context Pointer to MIDI context to initialize
 * @param client_name Name for the MIDI client (or NULL for default)
 * @param port_name Name for the output port (or NULL for default)
 * @return DROID_SUCCESS on success, appropriate error code on failure
 */
DroidError midi_context_init(MidiContext *context, const char *client_name, const char *port_name);

/**
 * Cleans up a MIDI context and releases all resources.
 * Safe to call multiple times or on uninitialized contexts.
 * 
 * @param context Pointer to MIDI context to cleanup
 */
void midi_context_cleanup(MidiContext *context);

/**
 * Finds a MIDI destination endpoint by name substring match.
 * 
 * @param device_name Name or partial name of the MIDI device to find (must not be NULL)
 * @return MIDIEndpointRef on success, 0 if not found
 */
MIDIEndpointRef midi_find_destination(const char *device_name);

/**
 * Lists all available MIDI destinations.
 * Useful for debugging when the target device is not found.
 * 
 * @param verbose If true, prints detailed information about each device
 */
void midi_list_destinations(bool verbose);

/**
 * Sends a SysEx message to a MIDI endpoint asynchronously.
 * This function blocks until the message is completely sent.
 * 
 * @param endpoint MIDI endpoint to send to
 * @param sysex_data SysEx message data
 * @param sysex_size Size of SysEx message
 * @param timeout_seconds Maximum time to wait for completion (0 = infinite)
 * @return DROID_SUCCESS on success, appropriate error code on failure
 */
DroidError midi_send_sysex(MIDIEndpointRef endpoint, const uint8_t *sysex_data, 
                          size_t sysex_size, double timeout_seconds);

/**
 * Validates that a MIDI endpoint is still valid and accessible.
 * 
 * @param endpoint MIDI endpoint to validate
 * @return DROID_SUCCESS if valid, error code if invalid or inaccessible
 */
DroidError midi_validate_endpoint(MIDIEndpointRef endpoint);

/**
 * Gets the display name of a MIDI endpoint.
 * 
 * @param endpoint MIDI endpoint to get name for
 * @param name_buffer Buffer to store the name (must not be NULL)
 * @param buffer_size Size of the name buffer (must be > 0)
 * @return DROID_SUCCESS on success, error code on failure
 */
DroidError midi_get_endpoint_name(MIDIEndpointRef endpoint, char *name_buffer, size_t buffer_size);

/**
 * Checks if MIDI services are available on the system.
 * 
 * @return DROID_SUCCESS if MIDI is available, error code otherwise
 */
DroidError midi_check_availability(void);

/**
 * SysEx completion callback function type.
 * Used internally for asynchronous SysEx sending.
 */
void midi_sysex_completion_callback(MIDISysexSendRequest *request);

#endif // MIDI_H
