#include "sysex.h"
#include "constants.h"
#include "file_utils.h"
#include <stdio.h>
#include <stdlib.h>
#include <string.h>

// Forward declaration for sysex_validate_message
DroidError sysex_validate_message(const uint8_t *sysex_data, size_t sysex_size);

Result sysex_encode_with_padding(const uint8_t *input, size_t input_size,
                                size_t chunk_size, uint8_t padding_byte) {
    if (chunk_size == 0) {
        RETURN_ERROR(DROID_ERROR_INVALID_ARGS);
    }

    // Allow empty input for empty files
    if (!input && input_size > 0) {
        RETURN_ERROR(DROID_ERROR_INVALID_ARGS);
    }

    // Handle empty input
    if (input_size == 0) {
        Result buffer_result = buffer_create(1);
        if (DROID_IS_ERROR(buffer_result)) {
            return buffer_result;
        }
        Buffer *output_buffer = (Buffer *)buffer_result.data;
        output_buffer->size = 0;
        RETURN_SUCCESS(output_buffer, 0);
    }

    // Calculate output size - padding is only added AFTER complete chunks if there's more data
    // This matches the original logic: spaceCount = inputSize / 255
    size_t padding_count = input_size / chunk_size;
    size_t output_size = input_size + padding_count;

    // Create output buffer
    Result buffer_result = buffer_create(output_size);
    if (DROID_IS_ERROR(buffer_result)) {
        return buffer_result;
    }

    Buffer *output_buffer = (Buffer *)buffer_result.data;

    size_t input_position = 0;
    size_t output_position = 0;

    while (input_position < input_size) {
        // Calculate chunk size for this iteration - exactly like original
        size_t current_chunk_size = ((input_size - input_position) > chunk_size)
                                   ? chunk_size
                                   : (input_size - input_position);

        // Copy chunk to output
        memcpy(output_buffer->data + output_position, input + input_position, current_chunk_size);
        input_position += current_chunk_size;
        output_position += current_chunk_size;

        // Add padding byte ONLY if we copied a full chunk AND there's more data
        // This exactly matches: if (chunk == 255 && inPos < inputSize)
        if (current_chunk_size == chunk_size && input_position < input_size) {
            output_buffer->data[output_position++] = padding_byte;
        }
    }

    output_buffer->size = output_position;
    RETURN_SUCCESS(output_buffer, output_position);
}

Result sysex_prepare_message(const uint8_t *data, size_t data_size) {
    // Use optimized single-pass approach for better performance
    size_t header_data_size = DROID_SYSEX_HEADER_SIZE + data_size;
    
    // First create combined header+data buffer, then encode with padding
    Result temp_buffer_result = buffer_create(header_data_size);
    if (DROID_IS_ERROR(temp_buffer_result)) {
        return temp_buffer_result;
    }

    Buffer *temp_buffer = (Buffer *)temp_buffer_result.data;
    
    // Copy header
    memcpy(temp_buffer->data, DROID_SYSEX_HEADER, DROID_SYSEX_HEADER_SIZE);
    temp_buffer->size = DROID_SYSEX_HEADER_SIZE;
    
    // Copy data if present
    if (data && data_size > 0) {
        memcpy(temp_buffer->data + DROID_SYSEX_HEADER_SIZE, data, data_size);
        temp_buffer->size = header_data_size;
    }
    
    // Encode with padding
    Result encoded_result = sysex_encode_with_padding(temp_buffer->data, temp_buffer->size,
                                                     SYSEX_CHUNK_SIZE, SYSEX_PADDING_BYTE);
    
    // Free temp buffer
    buffer_free(temp_buffer);
    free(temp_buffer);
    
    if (DROID_IS_ERROR(encoded_result)) {
        return encoded_result;
    }

    Buffer *encoded_buffer = (Buffer *)encoded_result.data;
    
    // Resize buffer to add F7 - avoid extra allocation by using buffer_resize
    DroidError resize_error = buffer_resize(encoded_buffer, encoded_buffer->size + 1);
    if (resize_error != DROID_SUCCESS) {
        buffer_free(encoded_buffer);
        free(encoded_buffer);
        RETURN_ERROR(resize_error);
    }
    
    // Add F7 end byte
    encoded_buffer->data[encoded_buffer->size] = SYSEX_END_BYTE;
    encoded_buffer->size++;

    RETURN_SUCCESS(encoded_buffer, encoded_buffer->size);
}

Result sysex_prepare_custom_message(const uint8_t *header, size_t header_size,
                                   const uint8_t *data, size_t data_size,
                                   size_t chunk_size, uint8_t padding_byte) {
    if (!header || header_size == 0 || chunk_size == 0) {
        RETURN_ERROR(DROID_ERROR_INVALID_ARGS);
    }

    // Allow empty data for header-only messages
    if (!data && data_size > 0) {
        RETURN_ERROR(DROID_ERROR_INVALID_ARGS);
    }

    // Check if header already includes start byte
    bool header_has_start_byte = (header[0] == SYSEX_START_BYTE);

    // Step 1: Combine header and data
    size_t combined_size = header_size + data_size;
    Result combined_buffer_result = buffer_create(combined_size);
    if (DROID_IS_ERROR(combined_buffer_result)) {
        return combined_buffer_result;
    }

    Buffer *combined_buffer = (Buffer *)combined_buffer_result.data;

    // Copy header
    memcpy(combined_buffer->data, header, header_size);
    combined_buffer->size = header_size;

    // Copy data if present
    if (data && data_size > 0) {
        memcpy(combined_buffer->data + header_size, data, data_size);
        combined_buffer->size = combined_size;
    }

    // Step 2: Encode with padding
    Result encoded_result = sysex_encode_with_padding(combined_buffer->data, combined_buffer->size,
                                                     chunk_size, padding_byte);

    // Free intermediate buffer
    buffer_free(combined_buffer);
    free(combined_buffer);

    if (DROID_IS_ERROR(encoded_result)) {
        return encoded_result;
    }

    Buffer *encoded_buffer = (Buffer *)encoded_result.data;

    // Step 3: Add SysEx bytes as needed
    size_t final_size = encoded_buffer->size;
    if (!header_has_start_byte) final_size++; // +1 for start byte if not in header
    final_size++; // +1 for end byte

    Result final_buffer_result = buffer_create(final_size);
    if (DROID_IS_ERROR(final_buffer_result)) {
        buffer_free(encoded_buffer);
        free(encoded_buffer);
        return final_buffer_result;
    }

    Buffer *final_buffer = (Buffer *)final_buffer_result.data;
    size_t pos = 0;

    // Add start byte if not in header
    if (!header_has_start_byte) {
        final_buffer->data[pos++] = SYSEX_START_BYTE;
    }

    // Add encoded data
    memcpy(final_buffer->data + pos, encoded_buffer->data, encoded_buffer->size);
    pos += encoded_buffer->size;

    // Add end byte
    final_buffer->data[pos] = SYSEX_END_BYTE;
    final_buffer->size = pos + 1;

    // Free intermediate buffer
    buffer_free(encoded_buffer);
    free(encoded_buffer);

    RETURN_SUCCESS(final_buffer, final_buffer->size);
}

DroidError sysex_validate_message(const uint8_t *sysex_data, size_t sysex_size) {
    if (!sysex_data || sysex_size < 2) {
        return DROID_ERROR_INVALID_ARGS;
    }

    // Check start byte
    if (sysex_data[0] != SYSEX_START_BYTE) {
        return DROID_ERROR_SYSEX_ENCODING;
    }

    // Check end byte
    if (sysex_data[sysex_size - 1] != SYSEX_END_BYTE) {
        return DROID_ERROR_SYSEX_ENCODING;
    }

    // Check reasonable size limits
    if (sysex_size > MAX_FILE_SIZE + 1000) { // Allow some overhead for encoding
        return DROID_ERROR_FILE_TOO_LARGE;
    }

    return DROID_SUCCESS;
}

size_t sysex_calculate_encoded_size(size_t input_size, size_t chunk_size) {
    if (input_size == 0 || chunk_size == 0) {
        return 2; // Just start and end bytes
    }

    // For sysex_prepare_message, input_size includes header
    size_t padding_count = input_size / chunk_size;

    // Total size = input + padding + end byte (start byte already in header)
    return input_size + padding_count + 1;
}
