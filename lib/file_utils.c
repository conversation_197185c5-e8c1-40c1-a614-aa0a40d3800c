#include "file_utils.h"
#include "constants.h"
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <sys/stat.h>
#include <errno.h>
#include <unistd.h>

Result file_read_all(const char *file_path) {
    if (!file_path) {
        RETURN_ERROR(DROID_ERROR_INVALID_ARGS);
    }

    // Validate the file path first
    DroidError validation_error = file_validate_path(file_path);
    if (validation_error != DROID_SUCCESS) {
        RETURN_ERROR(validation_error);
    }

    // Get file size
    size_t file_size;
    DroidError size_error = file_get_size(file_path, &file_size);
    if (size_error != DROID_SUCCESS) {
        RETURN_ERROR(size_error);
    }

    // Handle empty files specially
    if (file_size == 0) {
        Result buffer_result = buffer_create(1); // Minimal buffer
        if (DROID_IS_ERROR(buffer_result)) {
            RETURN_ERROR(buffer_result.error);
        }
        Buffer *buffer = (Buffer *)buffer_result.data;
        buffer->size = 0; // Actually empty
        RETURN_SUCCESS(buffer, 0);
    }

    // Open file
    FILE *file = fopen(file_path, "rb");
    if (!file) {
        RETURN_ERROR(DROID_ERROR_FILE_NOT_FOUND);
    }

    // Create buffer
    Result buffer_result = buffer_create(file_size);
    if (DROID_IS_ERROR(buffer_result)) {
        fclose(file);
        return buffer_result;
    }

    Buffer *buffer = (Buffer *)buffer_result.data;

    // Read file contents
    size_t bytes_read = fread(buffer->data, 1, file_size, file);
    fclose(file);

    if (bytes_read != file_size) {
        buffer_free(buffer);
        free(buffer);
        RETURN_ERROR(DROID_ERROR_FILE_READ_FAILED);
    }

    buffer->size = bytes_read;
    RETURN_SUCCESS(buffer, bytes_read);
}

DroidError file_validate_path(const char *file_path) {
    if (!file_path || strlen(file_path) == 0) {
        return DROID_ERROR_INVALID_ARGS;
    }

    if (strlen(file_path) >= MAX_PATH_LENGTH) {
        return DROID_ERROR_INVALID_ARGS;
    }

    // Check if file exists and is readable
    if (access(file_path, R_OK) != 0) {
        return DROID_ERROR_FILE_NOT_FOUND;
    }

    return DROID_SUCCESS;
}

DroidError file_get_size(const char *file_path, size_t *out_size) {
    if (!file_path || !out_size) {
        return DROID_ERROR_INVALID_ARGS;
    }

    struct stat file_stat;
    if (stat(file_path, &file_stat) != 0) {
        return DROID_ERROR_FILE_NOT_FOUND;
    }

    // Check if it's a regular file
    if (!S_ISREG(file_stat.st_mode)) {
        return DROID_ERROR_FILE_NOT_FOUND;
    }

    // Check file size limits
    if (file_stat.st_size < 0 || (size_t)file_stat.st_size > MAX_FILE_SIZE) {
        return DROID_ERROR_FILE_TOO_LARGE;
    }

    *out_size = (size_t)file_stat.st_size;
    return DROID_SUCCESS;
}

Result buffer_create(size_t capacity) {
    // Allow zero capacity for empty files
    if (capacity == 0) {
        capacity = 1; // Allocate minimal buffer for empty files
    }

    Buffer *buffer = malloc(sizeof(Buffer));
    if (!buffer) {
        RETURN_ERROR(DROID_ERROR_MEMORY_ALLOCATION);
    }

    buffer->data = malloc(capacity);
    if (!buffer->data) {
        free(buffer);
        RETURN_ERROR(DROID_ERROR_MEMORY_ALLOCATION);
    }

    buffer->size = 0;
    buffer->capacity = capacity;

    RETURN_SUCCESS(buffer, capacity);
}

void buffer_free(Buffer *buffer) {
    if (!buffer) {
        return;
    }

    if (buffer->data) {
        free(buffer->data);
        buffer->data = NULL;
    }

    buffer->size = 0;
    buffer->capacity = 0;
}

DroidError buffer_resize(Buffer *buffer, size_t new_capacity) {
    if (!buffer || new_capacity == 0) {
        return DROID_ERROR_INVALID_ARGS;
    }

    if (new_capacity <= buffer->capacity) {
        return DROID_SUCCESS; // No need to resize
    }

    // Optimize by growing in larger chunks to reduce reallocations
    size_t growth_capacity = buffer->capacity + (buffer->capacity / 2); // Grow by 50%
    if (growth_capacity > new_capacity) {
        new_capacity = growth_capacity;
    }

    uint8_t *new_data = realloc(buffer->data, new_capacity);
    if (!new_data) {
        return DROID_ERROR_MEMORY_ALLOCATION;
    }

    buffer->data = new_data;
    buffer->capacity = new_capacity;

    return DROID_SUCCESS;
}

DroidError buffer_append(Buffer *buffer, const uint8_t *data, size_t data_size) {
    if (!buffer || !data || data_size == 0) {
        return DROID_ERROR_INVALID_ARGS;
    }

    size_t required_capacity = buffer->size + data_size;
    if (required_capacity > buffer->capacity) {
        // Grow buffer by 50% or required size, whichever is larger
        size_t new_capacity = buffer->capacity + (buffer->capacity / 2);
        if (new_capacity < required_capacity) {
            new_capacity = required_capacity;
        }

        DroidError resize_error = buffer_resize(buffer, new_capacity);
        if (resize_error != DROID_SUCCESS) {
            return resize_error;
        }
    }

    memcpy(buffer->data + buffer->size, data, data_size);
    buffer->size += data_size;

    return DROID_SUCCESS;
}

const char *error_to_string(DroidError error) {
    switch (error) {
        case DROID_SUCCESS:
            return "Success";
        case DROID_ERROR_INVALID_ARGS:
            return "Invalid arguments";
        case DROID_ERROR_FILE_NOT_FOUND:
            return "File not found or not accessible";
        case DROID_ERROR_FILE_TOO_LARGE:
            return "File too large";
        case DROID_ERROR_FILE_READ_FAILED:
            return "Failed to read file";
        case DROID_ERROR_MEMORY_ALLOCATION:
            return "Memory allocation failed";
        case DROID_ERROR_MIDI_CLIENT_CREATION:
            return "Failed to create MIDI client";
        case DROID_ERROR_MIDI_PORT_CREATION:
            return "Failed to create MIDI port";
        case DROID_ERROR_MIDI_DEVICE_NOT_FOUND:
            return "MIDI device not found";
        case DROID_ERROR_MIDI_SEND_FAILED:
            return "Failed to send MIDI message";
        case DROID_ERROR_SYSEX_ENCODING:
            return "SysEx encoding failed";
        case DROID_ERROR_UNKNOWN:
        default:
            return "Unknown error";
    }
}
