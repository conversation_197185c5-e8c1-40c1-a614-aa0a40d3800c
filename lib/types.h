#ifndef TYPES_H
#define TYPES_H

#include <stddef.h>
#include <stdint.h>
#include <stdbool.h>
#include <dispatch/dispatch.h>
#include <CoreMIDI/CoreMIDI.h>

// CoreMIDI types are already defined in CoreMIDI.h

// Error codes for consistent error handling
typedef enum {
    DROID_SUCCESS = 0,
    DROID_ERROR_INVALID_ARGS,
    DROID_ERROR_FILE_NOT_FOUND,
    DROID_ERROR_FILE_TOO_LARGE,
    DROID_ERROR_FILE_READ_FAILED,
    DROID_ERROR_MEMORY_ALLOCATION,
    DROID_ERROR_MIDI_CLIENT_CREATION,
    DROID_ERROR_MIDI_PORT_CREATION,
    DROID_ERROR_MIDI_DEVICE_NOT_FOUND,
    DROID_ERROR_MIDI_SEND_FAILED,
    DROID_ERROR_SYSEX_ENCODING,
    DROID_ERROR_UNKNOWN
} DroidError;

// Buffer structure for better memory management
typedef struct {
    uint8_t *data;
    size_t size;
    size_t capacity;
} Buffer;

// SysEx context for asynchronous operations
typedef struct {
    uint8_t *buffer;
    dispatch_semaphore_t semaphore;
} SysExContext;

// MIDI client context for resource management
typedef struct {
    MIDIClientRef client;
    MIDIPortRef output_port;
    bool is_initialized;
} MidiContext;

// Configuration structure
typedef struct {
    const char *input_file_path;
    const char *midi_device_name;
    bool verbose;
    bool dry_run;
} Config;

// Function result type for better error handling
typedef struct {
    DroidError error;
    void *data;
    size_t size;
} Result;

// Utility macros for error handling
#define RETURN_ERROR(err) do { \
    Result result = {.error = (err), .data = NULL, .size = 0}; \
    return result; \
} while(0)

#define RETURN_SUCCESS(ptr, sz) do { \
    Result result = {.error = DROID_SUCCESS, .data = (ptr), .size = (sz)}; \
    return result; \
} while(0)

#define DROID_IS_SUCCESS(result) ((result).error == DROID_SUCCESS)
#define DROID_IS_ERROR(result) ((result).error != DROID_SUCCESS)

// Buffer management macros
#define BUFFER_INIT {.data = NULL, .size = 0, .capacity = 0}

#endif // TYPES_H
