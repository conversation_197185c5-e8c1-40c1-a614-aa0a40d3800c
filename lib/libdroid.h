#ifndef LIBDROID_H
#define LIBDROID_H

#include <stdint.h>
#include <stddef.h>

#ifdef __cplusplus
extern "C" {
#endif

// Error codes for the library
typedef enum {
    DROID_LIB_SUCCESS = 0,
    DROID_LIB_ERROR_FILE_NOT_FOUND,
    DROID_LIB_ERROR_FILE_READ_FAILED,
    DROID_LIB_ERROR_DEVICE_NOT_FOUND,
    DROID_LIB_ERROR_MIDI_FAILED,
    DROID_LIB_ERROR_INVALID_CONFIG,
    DROID_LIB_ERROR_MEMORY_ALLOCATION,
    DROID_LIB_ERROR_INVALID_ARGS,
    DROID_LIB_ERROR_SYSEX_ENCODING
} DroidLibErrorCode;

// Device information structure
typedef struct {
    char *name;
    int id;
    int is_active;  // 1 if device is currently active/online, 0 if offline
} DroidDevice;

// Result structure for operations
typedef struct {
    DroidLibErrorCode error_code;
    char *error_message;
    void *data;
    size_t data_size;
} DroidLibResult;

// Device list result
typedef struct {
    DroidLibErrorCode error_code;
    char *error_message;
    DroidDevice *devices;
    int device_count;
} DroidDeviceList;

// Core library functions

/**
 * Upload a DROID configuration file to a device
 * @param filename Path to the .ini file
 * @param device_name Name of the MIDI device (NULL for auto-detect)
 * @param verbose Enable verbose output
 * @param dry_run Don't actually send, just prepare
 * @return Result structure with error information
 */
DroidLibResult* droid_upload_file(const char* filename, const char* device_name,
                                  int verbose, int dry_run);

/**
 * Upload DROID configuration data from memory buffer
 * @param data Configuration data buffer
 * @param size Size of the data buffer
 * @param device_name Name of the MIDI device (NULL for auto-detect)
 * @param verbose Enable verbose output
 * @param dry_run Don't actually send, just prepare
 * @return Result structure with error information
 */
DroidLibResult* droid_upload_buffer(const uint8_t* data, size_t size,
                                    const char* device_name, int verbose, int dry_run);

/**
 * List available MIDI devices
 * @return Device list structure
 */
DroidDeviceList* droid_list_devices(void);

/**
 * List available MIDI devices with options
 * @param active_only If 1, only return currently active devices
 * @return Device list structure
 */
DroidDeviceList* droid_list_devices_with_options(int active_only);

/**
 * Check if a specific DROID device is currently connected
 * @param device_name Name of the MIDI device (NULL for default "DROID X7 MIDI")
 * @return 1 if device is connected and accessible, 0 otherwise
 */
int droid_is_device_connected(const char* device_name);

/**
 * Validate a DROID configuration file
 * @param filename Path to the .ini file
 * @return Result structure with validation information
 */
DroidLibResult* droid_validate_file(const char* filename);

/**
 * Validate DROID configuration data from memory buffer
 * @param data Configuration data buffer
 * @param size Size of the data buffer
 * @return Result structure with validation information
 */
DroidLibResult* droid_validate_buffer(const uint8_t* data, size_t size);

/**
 * Get library version information
 * @return Version string (caller should not free)
 */
const char* droid_get_version(void);

/**
 * Free a DroidLibResult structure
 * @param result Result to free
 */
void droid_free_result(DroidLibResult* result);

/**
 * Free a DroidDeviceList structure
 * @param device_list Device list to free
 */
void droid_free_device_list(DroidDeviceList* device_list);

#ifdef __cplusplus
}
#endif

#endif // LIBDROID_H
