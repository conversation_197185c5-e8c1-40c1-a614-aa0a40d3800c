#ifndef FILE_UTILS_H
#define FILE_UTILS_H

#include "types.h"

/**
 * Reads an entire file into a buffer with proper error handling and size validation.
 * 
 * @param file_path Path to the file to read
 * @return Result containing buffer data on success, or error code on failure
 */
Result file_read_all(const char *file_path);

/**
 * Validates that a file path is safe and accessible.
 * 
 * @param file_path Path to validate
 * @return DROID_SUCCESS if valid, appropriate error code otherwise
 */
DroidError file_validate_path(const char *file_path);

/**
 * Gets the size of a file without reading it.
 * 
 * @param file_path Path to the file
 * @param out_size Pointer to store the file size
 * @return DROID_SUCCESS on success, error code on failure
 */
DroidError file_get_size(const char *file_path, size_t *out_size);

/**
 * Creates a buffer with the specified capacity.
 * 
 * @param capacity Initial capacity for the buffer
 * @return Result containing allocated buffer on success, error on failure
 */
Result buffer_create(size_t capacity);

/**
 * Frees a buffer and sets its fields to safe values.
 * 
 * @param buffer Pointer to buffer to free
 */
void buffer_free(Buffer *buffer);

/**
 * Resizes a buffer to the new capacity if needed.
 * 
 * @param buffer Pointer to buffer to resize
 * @param new_capacity New capacity for the buffer
 * @return DROID_SUCCESS on success, error code on failure
 */
DroidError buffer_resize(Buffer *buffer, size_t new_capacity);

/**
 * Appends data to a buffer, resizing if necessary.
 * 
 * @param buffer Pointer to buffer to append to
 * @param data Data to append
 * @param data_size Size of data to append
 * @return DROID_SUCCESS on success, error code on failure
 */
DroidError buffer_append(Buffer *buffer, const uint8_t *data, size_t data_size);

/**
 * Converts error codes to human-readable strings.
 * 
 * @param error Error code to convert
 * @return String description of the error
 */
const char *error_to_string(DroidError error);

#endif // FILE_UTILS_H
