#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include "libdroid.h"

void test_version() {
    printf("=== Testing Version ===\n");
    const char* version = droid_get_version();
    printf("Library version: %s\n\n", version);
}

void test_device_list() {
    printf("=== Testing Device List ===\n");
    DroidDeviceList* devices = droid_list_devices();

    if (!devices) {
        printf("ERROR: Failed to get device list\n\n");
        return;
    }

    if (devices->error_code != DROID_LIB_SUCCESS) {
        printf("ERROR: %s\n", devices->error_message ? devices->error_message : "Unknown error");
    } else {
        printf("Found %d device(s):\n", devices->device_count);
        for (int i = 0; i < devices->device_count; i++) {
            printf("  %d: %s\n", devices->devices[i].id, devices->devices[i].name);
        }
    }

    droid_free_device_list(devices);
    printf("\n");
}

void test_validation() {
    printf("=== Testing Validation ===\n");

    // Test with empty buffer (should be valid)
    DroidLibResult* result = droid_validate_buffer(NULL, 0);
    if (!result) {
        printf("ERROR: Failed to create validation result\n");
        return;
    }

    if (result->error_code == DROID_LIB_SUCCESS) {
        printf("✓ Empty buffer validation: PASSED\n");
    } else {
        printf("✗ Empty buffer validation: FAILED - %s\n",
               result->error_message ? result->error_message : "Unknown error");
    }

    droid_free_result(result);

    // Test with simple config data
    const char* test_config = "[lfo]\nhz = 2\noutput = O1\n";
    result = droid_validate_buffer((const uint8_t*)test_config, strlen(test_config));

    if (!result) {
        printf("ERROR: Failed to create validation result\n");
        return;
    }

    if (result->error_code == DROID_LIB_SUCCESS) {
        printf("✓ Simple config validation: PASSED\n");
    } else {
        printf("✗ Simple config validation: FAILED - %s\n",
               result->error_message ? result->error_message : "Unknown error");
    }

    droid_free_result(result);
    printf("\n");
}

void test_dry_run_upload() {
    printf("=== Testing Dry Run Upload ===\n");

    // Test with empty buffer
    DroidLibResult* result = droid_upload_buffer(NULL, 0, NULL, 1, 1); // verbose=1, dry_run=1

    if (!result) {
        printf("ERROR: Failed to create upload result\n");
        return;
    }

    if (result->error_code == DROID_LIB_SUCCESS) {
        printf("✓ Dry run upload: PASSED\n");
    } else {
        printf("✗ Dry run upload: FAILED - %s\n",
               result->error_message ? result->error_message : "Unknown error");
    }

    droid_free_result(result);
    printf("\n");
}

int main() {
    printf("libdroid Test Program\n");
    printf("====================\n\n");

    test_version();
    test_device_list();
    test_validation();
    test_dry_run_upload();

    printf("All tests completed!\n");
    return 0;
}
