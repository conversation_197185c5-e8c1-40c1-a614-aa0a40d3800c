# Makefile for libdroid static library

# Compiler and flags
CC = clang
CFLAGS = -Wall -Wextra -std=c99 -O2 -fPIC
LDFLAGS = -framework CoreMIDI -framework CoreFoundation

# Library name
LIBRARY = libdroid.a

# Source files (all in lib directory now)
LIB_SOURCES = libdroid.c
DEP_SOURCES = file_utils.c sysex.c midi.c

# Object files
LIB_OBJECTS = $(LIB_SOURCES:.c=.o)
DEP_OBJECTS = $(DEP_SOURCES:.c=.o)
ALL_OBJECTS = $(LIB_OBJECTS) $(DEP_OBJECTS)

# Header files
HEADERS = libdroid.h constants.h types.h file_utils.h sysex.h midi.h

# Default target
all: $(LIBRARY)

# Build the static library
$(LIBRARY): $(ALL_OBJECTS)
	@echo "Creating static library: $(LIBRARY)"
	ar rcs $(LIBRARY) $(ALL_OBJECTS)
	@echo "Library created successfully"

# Compile library source files
%.o: %.c $(HEADERS)
	@echo "Compiling: $<"
	$(CC) $(CFLAGS) -c $< -o $@

# Compile dependency source files (now local)
%.o: %.c $(HEADERS)
	@echo "Compiling: $<"
	$(CC) $(CFLAGS) -c $< -o $@

# Test the library with a simple program
test: $(LIBRARY) test.c
	@echo "Building test program..."
	$(CC) $(CFLAGS) test.c -L. -ldroid $(LDFLAGS) -o test_libdroid
	@echo "Test program built: test_libdroid"

# Clean build artifacts
clean:
	@echo "Cleaning build artifacts..."
	rm -f $(ALL_OBJECTS) $(LIBRARY) test_libdroid
	@echo "Clean complete"

# Install library to system location (optional)
install: $(LIBRARY)
	@echo "Installing library to /usr/local/lib..."
	sudo cp $(LIBRARY) /usr/local/lib/
	sudo cp libdroid.h /usr/local/include/
	@echo "Library installed"

# Uninstall library from system location
uninstall:
	@echo "Uninstalling library..."
	sudo rm -f /usr/local/lib/$(LIBRARY)
	sudo rm -f /usr/local/include/libdroid.h
	@echo "Library uninstalled"

# Show help
help:
	@echo "Available targets:"
	@echo "  all       - Build the static library (default)"
	@echo "  test      - Build test program"
	@echo "  clean     - Remove build artifacts"
	@echo "  install   - Install library to system location"
	@echo "  uninstall - Remove library from system location"
	@echo "  help      - Show this help message"

# Declare phony targets
.PHONY: all test clean install uninstall help
