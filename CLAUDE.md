# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

DROID Tools is a command-line toolkit for DROID synthesizer devices. It features a hybrid architecture combining Go for the CLI interface with a C library for Core MIDI integration on macOS.

## Build Commands

### Primary Build Commands
```bash
make                 # Build the main droid CLI tool
make test            # Run all tests (unit + smoke)
make test-unit       # Run unit tests only
make test-smoke      # Run smoke tests only  
make fmt             # Format Go code
make install         # Install to ~/bin
make clean           # Clean build artifacts
```

### DROID-Specific Upload Commands
```bash
make configs/filename.ini                    # Upload config file directly
make upload CONFIG=file.ini                  # Upload with <PERSON><PERSON>IG variable
make dry-upload CONFIG=file.ini              # Test upload without sending
make wipe                                    # Wipe DROID configuration
make list-devices                            # List MIDI devices
```

## Architecture

### Hybrid Architecture Design
The codebase uses a unique hybrid approach:
- **Go CLI Application** (`cmd/droid/`): User interface, commands, business logic
- **C Library Backend** (`lib/`): Core MIDI integration and DROID protocol handling
- **Go-C Bridge** (`pkg/droid/droid.go`): CGo bindings connecting Go and C layers

### Key Components

#### CLI Layer (`cmd/droid/`)
- `main.go`: Entry point, uses Cobra command framework
- `commands/`: All CLI command implementations
  - `upload.go`: Configuration upload with SysEx protocol
  - `device.go`: MIDI device listing and management
  - `circuit.go`: Circuit library search and documentation
  - `validate.go`: Configuration file validation
  - `config.go`: Template and configuration management

#### Go Packages (`pkg/`)
- `droid/`: Core functionality and C library bindings
  - `droid.go`: Main CGo bridge with C library
  - `parser.go`: DROID INI configuration parser
  - `errors.go`: Structured error types
- `circuits/`: Circuit registry and embedded database
  - `registry.go`: Circuit definitions and search
  - `types.go`: Circuit parameter types and validation
  - `embedded/droid-firmware.json`: Official DROID firmware data
- `display/`: CLI output formatting and tables
- `logger/`: Structured logging system using logrus
  - `logger.go`: Global logger with custom formatters and level control

#### C Library (`lib/`)
- `libdroid.c/h`: Main library interface and device enumeration
- `midi.c/h`: Core MIDI client management and communication
- `sysex.c/h`: SysEx message encoding and DROID protocol implementation
- `file_utils.c/h`: File I/O operations with error handling

### DROID SysEx Protocol
The C library implements the DROID-specific SysEx protocol:
- Header: `F0 00 66 66 50` (SysEx start + DROID manufacturer ID)
- Padding: Space bytes (0x20) inserted after every 255 bytes including header
- Supports empty file uploads to wipe DROID configurations

## Development Workflow

### Testing Strategy
- **Unit Tests**: Go packages in `pkg/` have comprehensive test coverage
- **Smoke Tests**: CLI commands tested for basic functionality
- **Command Tests**: CLI commands in `cmd/droid/commands/` have specific tests

### Code Structure Patterns
- **Error Handling**: Structured error types in `pkg/droid/errors.go`
- **CGo Integration**: All C library calls go through `pkg/droid/droid.go`
- **Command Pattern**: Each CLI command is a separate file in `commands/`
- **Registry Pattern**: Circuits stored in embedded registry with search capabilities

### Build Dependencies
- **Go 1.24+**: Main language
- **Clang**: For C library compilation
- **CoreMIDI Framework**: macOS-specific MIDI support
- **CoreFoundation Framework**: macOS system integration

### Important Files
- `go.mod`: Uses Cobra CLI framework, go-pretty for tables, and logrus for logging
- `Makefile`: Unified build system handling both Go and C compilation
- `lib/Makefile`: Separate C library build with static linking
- `pkg/circuits/embedded/droid-firmware.json`: Official circuit database

## Key Concepts

### Circuit Library System
- Circuits defined in `pkg/circuits/types.go` with parameters, examples, and documentation
- Embedded firmware data from official DROID Forge loaded at runtime
- Search functionality by name, category, or description
- Validation of circuit parameters and register usage

### Configuration Validation
- INI-style configuration parsing in `pkg/droid/parser.go`
- Multi-layer validation: syntax, semantics, circuit parameters, register usage
- Cable connection validation for DROID-specific wiring rules

### Device Management
- Core MIDI integration for device discovery and communication
- Active vs offline device detection
- Device filtering and pattern matching
- JSON output support for scripting integration

## Recent Optimizations & Improvements

### C Library Optimizations (2024)
Major performance and maintainability improvements to the C library:

#### Device Enumeration Consolidation
- **Before**: 145+ lines of duplicate code across device listing functions
- **After**: Single `enumerate_devices_internal()` function with helper functions
- **Benefits**: ~120 lines reduced, consistent logic, eliminated redundant MIDI API calls

#### SysEx Buffer Management
- **Optimization**: Reduced from 3 to 2 buffer allocations using `buffer_resize()`
- **Buffer Growth**: Implemented 50% growth strategy to reduce reallocation frequency
- **Memory Impact**: ~33% fewer allocations for typical operations

#### Enhanced Error Handling
- **Input Validation**: Centralized helpers (`validate_filename()`, `validate_buffer_params()`)
- **Consistent Messages**: Better user experience with descriptive error messages
- **Path Validation**: Added length limits and improved safety checks

#### API Improvements
- **Documentation**: Enhanced function documentation with parameter requirements
- **Resource Management**: Better cleanup patterns and NULL-safety
- **Performance**: Optimized for both memory usage and execution speed

### Structured Logging System (2024)
Replaced ad-hoc `fmt.Printf` statements with professional structured logging:

#### Logging Infrastructure
- **Library**: Uses `github.com/sirupsen/logrus` for structured logging
- **Package**: Custom `pkg/logger/` with clean API and custom formatters
- **Levels**: Full support for trace, debug, info, warn, error, fatal, panic

#### Command Line Control
- **New Flag**: `--log-level` with all severity options
- **Backward Compatibility**: `--verbose` maps to debug level, `--quiet` still works
- **Examples**:
  ```bash
  droid --log-level debug device list    # Detailed diagnostic output
  droid --log-level warn upload config   # Only warnings and errors
  droid --verbose validate config        # Same as --log-level debug
  ```

#### Intelligent Message Classification
- **Debug**: Internal operations, API calls, diagnostic information
- **Info**: User-facing status updates, successful operations, progress indicators
- **Warn**: Non-critical issues (like `--force` usage, deprecated features)
- **Error**: Validation failures, upload errors, critical issues

#### Structured Context Fields
Messages include relevant context for better debugging:
```bash
# Debug level shows full context
DEBUG Device listing completed active_only=false pattern="" total_devices=1

# Info level shows clean user messages
INFO Starting configuration validation file=test.ini strict=false format=text
```

#### Output Formatting
- **Debug/Trace**: Full timestamps, colored output, detailed structured fields
- **Info and above**: Clean user-friendly messages without noise
- **Error/Warn**: Clear level indicators for important messages
- **Quiet Mode**: Respects existing `--quiet` behavior

### Development Best Practices
- **Testing**: All optimizations maintain full test coverage
- **Compatibility**: Backward compatible with existing CLI usage patterns
- **Performance**: Measurable improvements in memory usage and execution speed
- **Maintainability**: Cleaner code structure with consistent patterns