BAT(1)                      General Commands Manual                     BAT(1)

NNAAMMEE
       bat - a cat(1) clone with syntax highlighting and Git integration.

UUSSAAGGEE
       bat [OPTIONS] [FILE]...

       bat cache [CACHE-OPTIONS] [--build|--clear]

DDEESSCCRRIIPPTTIIOONN
       bat prints the syntax-highlighted content of a collection of FILEs to
       the terminal. If no FILE is specified, or when FILE is '-', it reads
       from standard input.

       bat supports a large number of programming and markup languages.  It
       also communicates with git(1) to show modifications with respect to the
       git index.  bat automatically pipes its output through a pager (by
       default: less).

       Whenever the output of bat goes to a non-interactive terminal, i.e.
       when the output is piped into another process or into a file, bat will
       act as a drop-in replacement for cat(1) and fall back to printing the
       plain file contents.


OOPPTTIIOONNSS
       General remarks: Command-line options like '-l'/'--language' that take
       values can be specified as either '--language value',
       '--language=value', '-l value' or '-lvalue'.

       --AA, ----sshhooww--aallll

              Show non-printable characters like space, tab or newline. Use
              '--tabs' to control the width of the tab-placeholders.

       ----nnoonnpprriinnttaabbllee--nnoottaattiioonn <notation>

              Specify how to display non-printable characters when using
              --show-all.

              Possible values:

              caret  Use character sequences like ^G, ^J, ^@, .. to identify
                     non-printable characters

              unicode
                     Use special Unicode code points to identify non-printable
                     characters

       --pp, ----ppllaaiinn

              Only show plain style, no decorations. This is an alias for
              '--style=plain'. When '-p' is used twice ('-pp'), it also
              disables automatic paging (alias for '--style=plain
              ----ppaaggiinngg=_n_e_v_e_r').

       --ll, ----llaanngguuaaggee <language>

              Explicitly set the language for syntax highlighting. The
              language can be specified as a name (like 'C++' or 'LaTeX') or
              possible file extension (like 'cpp', 'hpp' or 'md'). Use
              '--list-languages' to show all supported language names and file
              extensions.

       --HH, ----hhiigghhlliigghhtt--lliinnee <N:M>...

              Highlight the specified line ranges with a different background
              color. For example:

              --highlight-line 40
                     highlights line 40

              --highlight-line 30:40
                     highlights lines 30 to 40

              --highlight-line :40
                     highlights lines 1 to 40

              --highlight-line 40:
                     highlights lines 40 to the end of the file

              --highlight-line 30:+10
                     highlights lines 30 to 40

       ----ffiillee--nnaammee <name>...

              Specify the name to display for a file. Useful when piping data
              to bat from STDIN when bat does not otherwise know the filename.
              Note that the provided file name is also used for syntax
              detection.

       --dd, ----ddiiffff

              Only show lines that have been added/removed/modified with
              respect to the Git index. Use '--diff-context=N' to control how
              much context you want to see.

       ----ddiiffff--ccoonntteexxtt <N>...

              Include N lines of context around added/removed/modified lines
              when using '--diff'.

       ----ttaabbss <T>

              Set the tab width to T spaces. Use a width of 0 to pass tabs
              through directly

       ----wwrraapp <mode>

              Specify the text-wrapping mode (*auto*, never, character). The
              '--terminal-width' option can be used in addition to control the
              output width.

       --SS, ----cchhoopp--lloonngg--lliinneess

              Truncate all lines longer than screen width. Alias for
              '--wrap=never'.

       ----tteerrmmiinnaall--wwiiddtthh <width>

              Explicitly set the width of the terminal instead of determining
              it automatically. If prefixed with '+' or '-', the value will be
              treated as an offset to the actual terminal width. See also:
              '--wrap'.

       --nn, ----nnuummbbeerr

              Only show line numbers, no other decorations. This is an alias
              for '--style=numbers'

       ----ccoolloorr <when>

              Specify when to use colored output. The automatic mode only
              enables colors if an interactive terminal is detected. Possible
              values: *auto*, never, always.

       ----iittaalliicc--tteexxtt <when>

              Specify when to use ANSI sequences for italic text in the
              output. Possible values: always, *never*.

       ----ddeeccoorraattiioonnss <when>

              Specify when to use the decorations that have been specified via
              '--style'. The automatic mode only enables decorations if an
              interactive terminal is detected. Possible values: *auto*,
              never, always.

       --ff, ----ffoorrccee--ccoolloorriizzaattiioonn

              Alias for '--decorations=always --color=always'. This is useful
              if the output of bat is piped to another program, but you want
              to keep the colorization/decorations.

       ----ppaaggiinngg <when>

              Specify when to use the pager. To disable the pager, use
              '--paging=never' or its alias, --PP. To disable the pager
              permanently, set BAT_PAGER to an empty string. To control which
              pager is used, see the '--pager' option. Possible values:
              *auto*, never, always.

       ----ppaaggeerr <command>

              Determine which pager is used. This option will override the
              PAGER and BAT_PAGER environment variables. The default pager is
              'less'. To control when the pager is used, see the '--paging'
              option. Example: '--pager "less --RRFF"'.

              Note: By default, if the pager is set to 'less' (and no command-
              line options are specified), 'bat' will pass the following
              command line options to the pager: '-R'/'--RAW-CONTROL-CHARS',
              '-F'/'--quit-if-one-screen' and '-X'/'--no-init'. The last
              option ('-X') is only used for 'less' versions older than 530.
              The '-R' option is needed to interpret ANSI colors correctly.
              The second option ('-F') instructs less to exit immediately if
              the output size is smaller than the vertical size of the
              terminal. This is convenient for small files because you do not
              have to press 'q' to quit the pager. The third option ('-X') is
              needed to fix a bug with the '--quit-if-one-screen' feature in
              old versions of 'less'. Unfortunately, it also breaks mouse-
              wheel support in 'less'. If you want to enable mouse-wheel
              scrolling on older versions of 'less', you can pass just '-R'
              (as in the example above, this will disable the quit-if-one-
              screen feature). For less 530 or newer, it should work out of
              the box.

       --mm, ----mmaapp--ssyynnttaaxx <glob-pattern:syntax-name>...

              Map a glob pattern to an existing syntax name. The glob pattern
              is matched on the full path and the filename. For example, to
              highlight *.build files with the Python syntax, use -m
              '*.build:Python'. To highlight files named '.myignore' with the
              Git Ignore syntax, use -m '.myignore:Git Ignore'.  Note that the
              right-hand side is the *name* of the syntax, not a file
              extension.

       ----iiggnnoorreedd--ssuuffffiixx <ignored-suffix>

              Ignore extension. For example: 'bat --ignored-suffix ".dev"
              my_file.json.dev' will use JSON syntax, and ignore '.dev'

       ----tthheemmee <theme>

              Set the theme for syntax highlighting. Use ----lliisstt--tthheemmeess to see
              all available themes.  To set a default theme, add the
              ----tthheemmee==""......"" option to the configuration file or export the
              BBAATT__TTHHEEMMEE environment variable (e.g.: eexxppoorrtt BBAATT__TTHHEEMMEE==""......"").

              Special values:

              auto (_d_e_f_a_u_l_t)
                     Picks a dark or light theme depending on the terminal's
                     colors.  Use ----tthheemmee--lliigghhtt and ----tthheemmee--ddaarrkk to customize
                     the selected theme.

              auto:always
                     Variation of aauuttoo where where the terminal's colors are
                     detected even when the output is redirected.

              auto:system (macOS only)
                     Variation of aauuttoo where the color scheme is detected from
                     the system-wide preference instead.

              dark   Use the dark theme specified by ----tthheemmee--ddaarrkk.

              light  Use the light theme specified by ----tthheemmee--lliigghhtt.

       ----tthheemmee--ddaarrkk <theme>

              Sets the theme name for syntax highlighting used when the
              terminal uses a dark background.  To set a default theme, add
              the ----tthheemmee--ddaarrkk==""......"" option to the configuration file or
              export the BBAATT__TTHHEEMMEE__DDAARRKK environment variable (e.g. eexxppoorrtt
              BBAATT__TTHHEEMMEE__DDAARRKK==""......"").  This option only has an effect when
              ----tthheemmee option is set to aauuttoo or ddaarrkk.

       ----tthheemmee--lliigghhtt <theme>

              Sets the theme name for syntax highlighting used when the
              terminal uses a dark background.  To set a default theme, add
              the ----tthheemmee--ddaarrkk==""......"" option to the configuration file or
              export the BBAATT__TTHHEEMMEE__LLIIGGHHTT environment variable (e.g. eexxppoorrtt
              BBAATT__TTHHEEMMEE__LLIIGGHHTT==""......"").  This option only has an effect when
              ----tthheemmee option is set to aauuttoo or lliigghhtt.

       ----lliisstt--tthheemmeess

              Display a list of supported themes for syntax highlighting.

       --ss, ----ssqquueeeezzee--bbllaannkk

              Squeeze consecutive empty lines into a single empty line.

       ----ssqquueeeezzee--lliimmiitt <squeeze-limit>

              Set the maximum number of consecutive empty lines to be printed.

       ----ssttyyllee <style-components>

              Configure which elements (line numbers, file headers, grid
              borders, Git modifications, ..) to display in addition to the
              file contents. The argument is a comma-separated list of
              components to display (e.g. 'numbers,changes,grid') or a
              pre-defined style ('full').  To set a default style, add the
              '--style=".."' option to the configuration file or export the
              BAT_STYLE environment variable (e.g.: export BAT_STYLE="..").
              Possible values: *default*, full, auto, plain, changes, header,
              header-filename, header-filesize, grid, rule, numbers, snip.

       --rr, ----lliinnee--rraannggee <N:M>...

              Only print the specified range of lines for each file. For
              example:

              --line-range 30:40
                     prints lines 30 to 40

              --line-range :40
                     prints lines 1 to 40

              --line-range 40:
                     prints lines 40 to the end of the file

              --line-range 30:+10
                     prints lines 30 to 40

       --LL, ----lliisstt--llaanngguuaaggeess

              Display a list of supported languages for syntax highlighting.

       --uu, ----uunnbbuuffffeerreedd

              This option exists for POSIX-compliance reasons ('u' is for
              'unbuffered'). The output is always unbuffered - this option is
              simply ignored.

       ----nnoo--ccuussttoomm--aasssseettss

              Do not load custom assets.

       ----ccoonnffiigg--ddiirr

              Show bat's configuration directory.

       ----ccaacchhee--ddiirr

              Show bat's cache directory.

       ----ddiiaaggnnoossttiicc

              Show diagnostic information for bug reports.

       ----aacckknnoowwlleeddggeemmeennttss

              Show acknowledgements.

       ----sseett--tteerrmmiinnaall--ttiittllee

              Sets terminal title to filenames when using a pager.

       --hh, ----hheellpp

              Print this help message.

       --VV, ----vveerrssiioonn

              Show version information.

PPOOSSIITTIIOONNAALL AARRGGUUMMEENNTTSS
       <<FFIILLEE>>......

              Files to print and concatenate. Use a dash ('-') or no argument
              at all to read from standard input.

SSUUBBCCOOMMMMAANNDDSS
       ccaacchhee - Modify the syntax-definition and theme cache.

FFIILLEESS
       bat can also be customized with a configuration file. The location of
       the file is dependent on your operating system. To get the default path
       for your system, call:

       bbaatt ----ccoonnffiigg--ffiillee

       Alternatively, you can use the BAT_CONFIG_PATH environment variable to
       point bat to a non-default location of the configuration file.

       To generate a default configuration file, call:

       bbaatt ----ggeenneerraattee--ccoonnffiigg--ffiillee

       These are related options:

       ----ccoonnffiigg--ffiillee

              Show path to the configuration file.

       ----ggeenneerraattee--ccoonnffiigg--ffiillee

              Generates a default configuration file.

       ----nnoo--ccoonnffiigg

              Do not use the configuration file.

AADDDDIINNGG CCUUSSTTOOMM LLAANNGGUUAAGGEESS
       bat supports Sublime Text ..ssuubblliimmee--ssyynnttaaxx language files, and can be
       customized to add additional languages to your local installation. To
       do this, add the ..ssuubblliimmee--ssyynnttaaxx language files to `$$((bbaatt ----ccoonnffiigg--
       ddiirr))//ssyynnttaaxxeess` and run `bbaatt ccaacchhee ----bbuuiilldd`.

       EExxaammppllee::

            mkdir -p "$(bat --config-dir)/syntaxes"
            cd "$(bat --config-dir)/syntaxes"

            # Put new '.sublime-syntax' language definition files
            # in this folder (or its subdirectories), for example:
            git clone https://github.com/tellnobody1/sublime-purescript-syntax

            # And then build the cache.
            bat cache --build

       Once the cache is built, the new language will be visible in `bbaatt
       ----lliisstt--llaanngguuaaggeess`.
       If you ever want to remove the custom languages, you can clear the
       cache with `bbaatt ccaacchhee ----cclleeaarr`.


AADDDDIINNGG CCUUSSTTOOMM TTHHEEMMEESS
       Similarly to custom languages, bat supports Sublime Text ..ttmmTThheemmee
       themes.  These can be installed to `$$((bbaatt ----ccoonnffiigg--ddiirr))//tthheemmeess`, and
       are added to the cache with `bbaatt ccaacchhee ----bbuuiilldd``..


IINNPPUUTT PPRREEPPRROOCCEESSSSOORR
       Much like less(1) does, bat supports input preprocessors via the
       LESSOPEN and LESSCLOSE environment variables.  In addition, bat
       attempts to be as compatible with less's preprocessor implementation as
       possible.

       To use the preprocessor, call:

       bbaatt ----lleessssooppeenn

       Alternatively, the preprocessor may be enabled by default by adding the
       '--lessopen' option to the configuration file.

       To temporarily disable the preprocessor if it is enabled by default,
       call:

       bbaatt ----nnoo--lleessssooppeenn

       These are related options:

       ----lleessssooppeenn

              Enable the $LESSOPEN preprocessor.

       ----nnoo--lleessssooppeenn

              Disable the $LESSOPEN preprocessor if enabled (overrides
              --lessopen)

       For more information, see the "INPUT PREPROCESSOR" section of less(1).


MMOORREE IINNFFOORRMMAATTIIOONN
       For more information and up-to-date documentation, visit the bat repo:
       hhttttppss::////ggiitthhuubb..ccoomm//sshhaarrkkddpp//bbaatt

                                                                        BAT(1)
