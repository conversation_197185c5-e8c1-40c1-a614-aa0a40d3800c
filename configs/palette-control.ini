[p2b8]

# -----
# Clock
# -----

[lfo]
    hz = 2
    output = _BPM

[clocktool]
    clock = _BPM
    divide = 8
    output = _RESET

# ----------
# Pagination
# ----------

[button]
    button = B1.7
    states = 1
    output = _PREV_PAGE_TRIGGER
    led = L1.7

[button]
    button = B1.8
    states = 1
    output = _NEXT_PAGE_TRIGGER
    led = L1.8

[switch]
    input1 = 1
    input2 = 2
    input3 = 3
    input4 = 4
    backward = _PREV_PAGE_TRIGGER
    forward = _NEXT_PAGE_TRIGGER
    output1 = _MOD

[copy]
    input = 1
    output = L1.7

[copy]
    input = 1
    output = L1.8

# -------------------
# Modulator Selection
# -------------------

[ifequal]
    input1 = _MOD
    input2 = 1
    output = _MOD_1

[copy]
    input = _MOD_1
    output = R1

[ifequal]
    input1 = _MOD
    input2 = 2
    output = _MOD_2

[copy]
    input = _MOD_2
    output = R2

[ifequal]
    input1 = _MOD
    input2 = 3
    output = _MOD_3

[copy]
    input = _MOD_3
    output = R3

[ifequal]
    input1 = _MOD
    input2 = 4
    output = _MOD_4

[copy]
    input = _MOD_4
    output = R4

# -----------
# Modulator 1
# -----------

[buttongroup]
    select = _MOD_1
    button1 = B1.1
    button2 = B1.2
    led1 = L1.1
    led2 = L1.2
    led3 = L1.3
    led4 = L1.4
    led5 = L1.5
    led6 = L1.6
    buttonoutput1 = _MOD_1_PAGE_1
    buttonoutput2 = _MOD_1_PAGE_2

[logic]
    input1 = _MOD_1
    input2 = _MOD_1_PAGE_1
    and = _MOD_1_PAGE_1_SELECTED

[logic]
    input1 = _MOD_1
    input2 = _MOD_1_PAGE_2
    and = _MOD_1_PAGE_2_SELECTED

[pot]
    pot = P1.1
    select = _MOD_1_PAGE_1_SELECTED
    ledgauge = off
    output = _MOD_1_PATTERN

[pot]
    pot = P1.2
    select = _MOD_1_PAGE_1_SELECTED
    ledgauge = off
    output = _MOD_1_OFFSET

[pot]
    pot = P1.1
    select = _MOD_1_PAGE_2_SELECTED
    slope = 4
    ledgauge = off
    output = _MOD_1_ATTACK

[pot]
    pot = P1.2
    select = _MOD_1_PAGE_2_SELECTED
    slope = 4
    ledgauge = off
    output = _MOD_1_RELEASE

[switch]
    input1 = 1
    input2 = 1
    input3 = 7
    input4 = 1
    input5 = 4
    input6 = 7
    input7 = 2
    input8 = 5
    input9 = 3
    input10 = 4
    input11 = 3
    input12 = 5
    input13 = 1
    input14 = 3
    input15 = 2
    input16 = 1
    offset = _MOD_1_PATTERN * 15
    output1 = _MOD_1_BEATS

[switch]
    input1 = 1
    input2 = 2
    input3 = 8
    input4 = 4
    input5 = 7
    input6 = 12
    input7 = 4
    input8 = 9
    input9 = 8
    input10 = 11
    input11 = 10
    input12 = 13
    input13 = 8
    input14 = 14
    input15 = 15
    input16 = 16
    offset = _MOD_1_PATTERN * 15
    output1 = _MOD_1_LENGTH

[clocktool]
    clock = _BPM
    multiply = 4
    output = _MOD_1_CLOCK

[euklid]
    clock = _MOD_1_CLOCK
    length = _MOD_1_LENGTH
    beats = _MOD_1_BEATS
    offset = _MOD_1_OFFSET * 16
    output = _MOD_1_TRIGGER
    reset = _RESET

[contour]
    gate = _MOD_1_TRIGGER
    attack = _MOD_1_ATTACK * 2
    release = _MOD_1_RELEASE * 8
    output = O1

# -----------
# Modulator 2
# -----------

[buttongroup]
    select = _MOD_2
    button1 = B1.1
    button2 = B1.2
    led1 = L1.1
    led2 = L1.2
    led3 = L1.3
    led4 = L1.4
    led5 = L1.5
    led6 = L1.6
    buttonoutput1 = _MOD_2_PAGE_1
    buttonoutput2 = _MOD_2_PAGE_2

[logic]
    input1 = _MOD_2
    input2 = _MOD_2_PAGE_1
    and = _MOD_2_PAGE_1_SELECTED

[logic]
    input1 = _MOD_2
    input2 = _MOD_2_PAGE_2
    and = _MOD_2_PAGE_2_SELECTED

[pot]
    pot = P1.1
    select = _MOD_2_PAGE_1_SELECTED
    ledgauge = off
    output = _MOD_2_PATTERN

[pot]
    pot = P1.2
    select = _MOD_2_PAGE_1_SELECTED
    ledgauge = off
    output = _MOD_2_OFFSET

[pot]
    pot = P1.1
    select = _MOD_2_PAGE_2_SELECTED
    slope = 4
    ledgauge = off
    output = _MOD_2_ATTACK

[pot]
    pot = P1.2
    select = _MOD_2_PAGE_2_SELECTED
    slope = 4
    ledgauge = off
    output = _MOD_2_RELEASE

[switch]
    input1 = 1
    input2 = 1
    input3 = 7
    input4 = 1
    input5 = 4
    input6 = 7
    input7 = 2
    input8 = 5
    input9 = 3
    input10 = 4
    input11 = 3
    input12 = 5
    input13 = 1
    input14 = 3
    input15 = 2
    input16 = 1
    offset = _MOD_2_PATTERN * 15
    output1 = _MOD_2_BEATS

[switch]
    input1 = 1
    input2 = 2
    input3 = 8
    input4 = 4
    input5 = 7
    input6 = 12
    input7 = 4
    input8 = 9
    input9 = 8
    input10 = 11
    input11 = 10
    input12 = 13
    input13 = 8
    input14 = 14
    input15 = 15
    input16 = 16
    offset = _MOD_2_PATTERN * 15
    output1 = _MOD_2_LENGTH

[clocktool]
    clock = _BPM
    multiply = 4
    output = _MOD_2_CLOCK

[euklid]
    clock = _MOD_2_CLOCK
    length = _MOD_2_LENGTH
    beats = _MOD_2_BEATS
    offset = _MOD_2_OFFSET * 16
    output = _MOD_2_TRIGGER
    reset = _RESET

[contour]
    gate = _MOD_2_TRIGGER
    attack = _MOD_2_ATTACK * 2
    release = _MOD_2_RELEASE * 8
    output = O2
