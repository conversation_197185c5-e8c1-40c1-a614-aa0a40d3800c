# DivKids DROID Modulation Hub v1

# creator: effiksmusic
# date: September 2024
# firemware: bule-5
# DivKid Video: https://youtu.be/m5q_G0ROVHk

# INPUTS:
#  I1: [Trig 1] Trigger input
#  I2: [Trig 2] Trigger input
#  I3: [Trig 3] Trigger input
#  I4: [Trig 4] Trigger input
#  I5: [Env 1 shape] CV input (10V)
#  I6: [Env 2 lvl] CV input (10V)
#  I7: [LFO 1 rate] CV input (5V)
#  I8: [LFO 2 rate] CV input (5V)

# OUTPUTS:
#  O1: [Env 1] AD envelop output
#  O2: [Env 2] AD envelop output
#  O3: [Env 3] AD envelop output
#  O4: [Env 4] AD envelop output
#  O5: [S/H 1] Sample an holde output (0-10V)
#  O6: [S/H 2] Sample an holde output (0-10V)
#  O7: [LFO 1] LFO output (0-10V)
#  O8: [LFO 2] LFO output (0-10V)

# CONTROLLER 1:
#  P1.1: [Pot 1] parameter 1
#  P1.2: [Pot 2] parameter 1
#  B1.1: [Env 1] Longpress analog/digital
#  B1.2: [Env 2] Longpress analog/digital
#  B1.3: [Env 3] Longpress trigger/loop
#  B1.4: [Env 4] Longpress trigger/loop
#  B1.5: [S/H 1] Longpress loop/constant
#  B1.6: [S/H 2] Longpress loop/constant
#  B1.7: [LFO 1] No longprss at the moment
#  B1.8: [LFO 2] No longprss at the moment

[p2b8]

# -------------------------------------------------
# global settings
# -------------------------------------------------

# brightness of the LEDs on the Master
[droid]
    ledbrightness = 0.5

# Sets the max length of the decay on all four envelops in the patch
[copy]
    input = 0.75
    output = _DECAY_MAX

# Sets the min length of the decay on all four envelops in the patch
[copy]
    input = 0.03 + _DECAY_MAX
    output = _DECAY_MIN

# Sets the max length of the attack on all four envelops in the patch (attack parameter is 1/20 shorter than that of decay, see manual)
[copy]
    input = 15
    output = _ATTACK_MAX

# [lfo]
#   level = 0.2
#   square = _LONGPRESS_BLINK
#   pulsewidth = 0.2
#   hz = 2

# Amount of dimmed LED when buttons are long pressed
[copy]
    input = 0.2
    output = _LONGPRESS_LED

# Button group on the P2B8 to selct the differnet modulation tracks. A longpress on each button is assign to a particular function (confirmed by a blinking led on the button)
[buttongroup]
    button1 = B1.1
    button2 = B1.2
    button3 = B1.3
    button4 = B1.4
    button5 = B1.5
    button6 = B1.6
    button7 = B1.7
    button8 = B1.8
    led1 = _LED1
    led2 = _LED2
    led3 = _LED3
    led4 = _LED4
    led5 = _LED5
    led6 = _LED6
    led7 = _LED7
    led8 = _LED8
    output = _SELECT

# -------------------------------------------------
# env 1
# -------------------------------------------------

# Longpress on the select button (confirmed by a blinking led on the button) switches linear vs. exponential curve
[button]
    button = B1.1
    longpress = _ENV1_SWITCH

[burst]
    trigger = _ENV1_SWITCH
    hz = 5
    count = 5
    output = _ENV1_SWITCH_LED

[flipflop]
    toggle = _ENV1_SWITCH
    output = _ENV1_LIN_EXP

[mixer]
    input1 = _ENV1_SWITCH_LED * -0.5 + _LED1
    input2 = _ENV1_LIN_EXP * _LONGPRESS_LED
    output = L1.1

# Pot for attack lenght
[pot]
    select = _SELECT
    selectat = 0
    pot = P1.1
    righthalf = _ENV1_ATTACK_HALF

[compare]
    input = _ENV1_ATTACK_HALF
    compare = 0.5
    ifgreater = 0.5
    else = _ENV1_ATTACK_HALF
    output = _ENV1_ATTACK_POT

# Pot for decay lenght
[pot]
    select = _SELECT
    selectat = 0
    pot = P1.1
    ledgauge = off
    outputscale = _DECAY_MAX
    absbipolar = _ENV1_DECAY_POT_ABSBIPOLAR

[copy]
    input = _ENV1_DECAY_POT_ABSBIPOLAR * -1 + _DECAY_MIN
    output = _ENV1_DECAY_POT

# Pot for CV attenuverter to control the CV input for the envelop shape
[pot]
    select = _SELECT
    selectat = 0
    pot = P1.2
    startvalue = 0.5
    notch = 0.1
    bipolar = _ENV1_ATTENUVERTER_POT

[copy]
    input = I5 * _ENV1_ATTENUVERTER_POT
    output = _ENV1_ATTENUVERTER

[compare]
    input = _ENV1_ATTACK_POT + _ENV1_ATTENUVERTER
    compare = 0
    else = 0
    precision = 0.1
    ifgreater = _ENV1_ATTACK_POT + _ENV1_ATTENUVERTER
    output = _ENV1_ATTACK

[compare]
    input = _ENV1_DECAY_POT + _ENV1_ATTENUVERTER
    compare = 0.02
    else = 0.02
    ifgreater = _ENV1_DECAY_POT + _ENV1_ATTENUVERTER
    output = _ENV1_DECAY

# AD envelop with CV control over the shape
# pot 1: envelop shape
# pot 2: CV attenuverter
[contour]
    trigger = I1
    attack = _ENV1_ATTACK * _ATTACK_MAX
    release = _ENV1_DECAY
    level = 1
    attackshape = _ENV1_LIN_EXP * 0.25 + 0.5
    releaseshape = _ENV1_LIN_EXP * 0.5 + 0.5
    output = O1

# -------------------------------------------------
# env 2
# -------------------------------------------------

# Longpress on the select button (confirmed by a blinking led on the button) switches linear vs. exponential curve
[button]
    button = B1.2
    longpress = _ENV2_SWITCH

[burst]
    trigger = _ENV2_SWITCH
    hz = 5
    count = 5
    output = _ENV2_SWITCH_LED

[flipflop]
    toggle = _ENV2_SWITCH
    output = _ENV2_LIN_EXP

[mixer]
    input1 = _ENV2_SWITCH_LED * -0.5 + _LED2
    input2 = _ENV2_LIN_EXP * _LONGPRESS_LED
    output = L1.2

# Pot for attack lenght
[pot]
    select = _SELECT
    selectat = 1
    pot = P1.1
    righthalf = _ENV2_ATTACK_HALF

[compare]
    input = _ENV2_ATTACK_HALF
    compare = 0.5
    ifgreater = 0.5
    else = _ENV2_ATTACK_HALF
    output = _ENV2_ATTACK_POT

# Pot for decay lenght
[pot]
    select = _SELECT
    selectat = 1
    pot = P1.1
    ledgauge = off
    outputscale = _DECAY_MAX
    absbipolar = _ENV2_DECAY_POT_ABSBIPOLAR

[copy]
    input = _ENV2_DECAY_POT_ABSBIPOLAR * -1 + _DECAY_MIN
    output = _ENV2_DECAY_POT

# Pot for CV attenuverter for level
[pot]
    select = _SELECT
    selectat = 1
    pot = P1.2
    outputscale = 2
    startvalue = 1
    notch = 0.1
    bipolar = _ENV2_ATTENUVERTER_POT

# Normalization for trigger in. If nothing is plugged into input 2, triggers from input 1 are past on  to envelop 2
[copy]
    input = I1
    output = N2

# Normalization of input 6. If nothing is plugged in, it sends 5V to the level.
[copy]
    input = 5V
    output = N6

[copy]
    input = I6 * _ENV2_ATTENUVERTER_POT
    output = _ENV2_LEVEL

# AD envelop with CV control over level
# pot 1: envelop shape
# pot 2: CV attenuverter
[contour]
    trigger = I2
    attack = _ENV2_ATTACK_POT * _ATTACK_MAX
    release = _ENV2_DECAY_POT
    level = _ENV2_LEVEL
    attackshape = _ENV2_LIN_EXP * 0.25 + 0.5
    releaseshape = _ENV2_LIN_EXP * 0.5 + 0.5
    output = O2

# -------------------------------------------------
# env 3
# -------------------------------------------------

# Longpress on the select button (confirmed by a blinking led on the button) switches between trigger and looping mode of the AD envelop
[button]
    button = B1.3
    longpress = _ENV3_SWITCH

[burst]
    trigger = _ENV3_SWITCH
    hz = 5
    count = 5
    output = _ENV3_SWITCH_LED

[flipflop]
    toggle = _ENV3_SWITCH
    output = _ENV3_LOOP

[mixer]
    input1 = _ENV3_SWITCH_LED * -0.5 + _LED3
    input2 = _ENV3_LOOP * _LONGPRESS_LED
    output = L1.3

# Pot for attack lenght
[pot]
    select = _SELECT
    selectat = 2
    pot = P1.1
    righthalf = _ENV3_ATTACK_HALF

[compare]
    input = _ENV3_ATTACK_HALF
    compare = 0.5
    ifgreater = 0.5
    else = _ENV3_ATTACK_HALF
    output = _ENV3_ATTACK_POT

# Pot for decay lenght
[pot]
    select = _SELECT
    selectat = 2
    pot = P1.1
    ledgauge = off
    outputscale = _DECAY_MAX
    absbipolar = _ENV3_DECAY_POT_ABSBIPOLAR

[copy]
    input = _ENV3_DECAY_POT_ABSBIPOLAR * -1 + _DECAY_MIN
    output = _ENV3_DECAY_POT

# Pot for level attenuverter
[pot]
    select = _SELECT
    selectat = 2
    pot = P1.2
    outputscale = 1
    startvalue = 1
    notch = 0.1
    bipolar = _ENV3_ATTENUVERTER_POT

# AD envelop with POT control over level
# pot 1: envelop shape
# pot 2: level attenuverter
[contour]
    trigger = I3
    attack = _ENV3_ATTACK_POT * _ATTACK_MAX
    release = _ENV3_DECAY_POT
    level = _ENV3_ATTENUVERTER_POT
    loop = _ENV3_LOOP
    output = O3

# -------------------------------------------------
# env 4
# -------------------------------------------------

# Longpress on the select button (confirmed by a blinking led on the button) switches between trigger and looping mode of the AD envelop
[button]
    button = B1.4
    longpress = _ENV4_SWITCH

[burst]
    trigger = _ENV4_SWITCH
    hz = 5
    count = 5
    output = _ENV4_SWITCH_LED

[flipflop]
    toggle = _ENV4_SWITCH
    output = _ENV4_LOOP

[mixer]
    input1 = _ENV4_SWITCH_LED * -0.5 + _LED4
    input2 = _ENV4_LOOP * _LONGPRESS_LED
    output = L1.4

# Pot for attack lenght
[pot]
    select = _SELECT
    selectat = 3
    pot = P1.1
    righthalf = _ENV4_ATTACK_HALF

[compare]
    input = _ENV4_ATTACK_HALF
    compare = 0.5
    ifgreater = 0.5
    else = _ENV4_ATTACK_HALF
    output = _ENV4_ATTACK_POT

# Pot for decay lenght
[pot]
    select = _SELECT
    selectat = 3
    pot = P1.1
    ledgauge = off
    outputscale = _DECAY_MAX
    absbipolar = _ENV4_DECAY_POT_ABSBIPOLAR

[copy]
    input = _ENV4_DECAY_POT_ABSBIPOLAR * -1 + _DECAY_MIN
    output = _ENV4_DECAY_POT

# Pot for level attenuverter
[pot]
    select = _SELECT
    selectat = 3
    pot = P1.2
    outputscale = 1
    startvalue = 1
    notch = 0.1
    bipolar = _ENV4_ATTENUVERTER_POT

# AD envelop with POT control over level
# pot 1: envelop shape
# pot 2: level attenuverter
[contour]
    trigger = I4
    attack = _ENV4_ATTACK_POT * _ATTACK_MAX
    release = _ENV4_DECAY_POT
    level = _ENV4_ATTENUVERTER_POT
    loop = _ENV4_LOOP
    output = O4

# -------------------------------------------------
# sample 1
# -------------------------------------------------

# Longpress on the select button (confirmed by a blinking led on the button) switches between looped and constant randomness mode
[button]
    button = B1.5
    longpress = _SAMPLE1_SWITCH

[burst]
    trigger = _SAMPLE1_SWITCH
    hz = 5
    count = 5
    output = _SAMPLE1_SWITCH_LED

[flipflop]
    toggle = _SAMPLE1_SWITCH
    output = _SAMPLE1_DEJAVU

[mixer]
    input1 = _SAMPLE1_SWITCH_LED * -0.5 + _LED5
    input2 = _SAMPLE1_DEJAVU * _LONGPRESS_LED
    output = L1.5

# Pot for level of random CV, each turn of the knob will creat a new random seed
[pot]
    select = _SELECT
    selectat = 4
    pot = P1.1
    startvalue = 0.2
    output = _SAMPLE1_LEVEL

# Pot for slew time
[pot]
    select = _SELECT
    selectat = 4
    pot = P1.2
    outputscale = 0.5
    startvalue = 0
    slope = 2
    output = _SAMPLE1_SLEW

# algoquencer to produce random CV, can be switched between looped and constant randomness
[algoquencer]
    clock = I1
    pitchlow = 0
    pitchhigh = _SAMPLE1_LEVEL
#   pitchresolution = _SAMPLE1_DEJAVU * 10
    length = 16
    reroll = _SAMPLE1_DEJAVU
    dejavu = _SAMPLE1_DEJAVU
    pitch = _SAMPLE1_RANDOM

# quantiser to minor pentatonic
[minifonion]
    input = _SAMPLE1_RANDOM
    trigger = I1
    root = 0
    degree = 7
    select1 = 1
    select3 = 1
    select5 = 1
    select7 = 1
    select9 = 0
    select11 = 1
    select13 = 0
    output = _SAMPLE1_QUANTIESED

[slew]
    input = _SAMPLE1_QUANTIESED
    slew = _SAMPLE1_SLEW
    linear = O5

# -------------------------------------------------
# sample 2
# -------------------------------------------------

# Longpress on the select button (confirmed by a blinking led on the button) switches between looped and constant randomness mode
[button]
    button = B1.6
    longpress = _SAMPLE2_SWITCH

[burst]
    trigger = _SAMPLE2_SWITCH
    hz = 5
    count = 5
    output = _SAMPLE2_SWITCH_LED

[flipflop]
    toggle = _SAMPLE2_SWITCH
    output = _SAMPLE2_DEJAVU

[mixer]
    input1 = _SAMPLE2_SWITCH_LED * -0.5 + _LED6
    input2 = _SAMPLE2_DEJAVU * _LONGPRESS_LED
    output = L1.6

# Pot for level of random CV, each turn of the knob will creat a new random seed
[pot]
    select = _SELECT
    selectat = 5
    pot = P1.1
    startvalue = 0.2
    output = _SAMPLE2_LEVEL

# Pot for slew time
[pot]
    select = _SELECT
    selectat = 5
    pot = P1.2
    outputscale = 0.5
    startvalue = 0
    slope = 2
    output = _SAMPLE2_SLEW

# looped and constant randomness
[algoquencer]
    clock = I2
    pitchlow = _SAMPLE2_LEVEL * -5V
    pitchhigh = _SAMPLE2_LEVEL * 5V
#   pitchresolution = _SAMPLE2_DEJAVU * 10
    length = 16
    reroll = _SAMPLE2_DEJAVU
    dejavu = _SAMPLE2_DEJAVU
    pitch = _SAMPLE2_RANDOM

# quantiser to minor pentatonic
[minifonion]
    input = _SAMPLE2_RANDOM
    trigger = I2
    root = 0
    degree = 7
    select1 = 1
    select3 = 1
    select5 = 1
    select7 = 1
    select9 = 0
    select11 = 1
    select13 = 0
    output = _SAMPLE2_QUANTIESED

[slew]
    input = _SAMPLE2_QUANTIESED
    slew = _SAMPLE2_SLEW
    linear = O6

# -------------------------------------------------
# lfo 1
# -------------------------------------------------

# Longpress on the select button (confirmed by a blinking led on the button) switches between constand level and random level
[button]
    button = B1.7
    longpress = _LFO1_SWITCH

[burst]
    trigger = _LFO1_SWITCH
    hz = 5
    count = 5
    output = _LFO1_SWITCH_LED

[flipflop]
    toggle = _LFO1_SWITCH
    output = _LFO1_RANDOM_LVL

[mixer]
    input1 = _LFO1_SWITCH_LED * -0.5 + _LED7
    input2 = _LFO1_RANDOM_LVL * _LONGPRESS_LED
    output = L1.7

# Pot for LFO rate
[pot]
    select = _SELECT
    selectat = 6
    pot = P1.1
    outputscale = 20
    slope = 3
    output = _LFO1_RATE_POT

# Pot for waveshape, morphs between sine, triangle, falling and rising saw to a random source
[pot]
    select = _SELECT
    selectat = 6
    pot = P1.2
    output = _LFO1_WAVEFORM_POT

[fold]
    input = I7
    minimum = 0
    maximum = 0.5
    foldby = 0.01
    output = _LFO1_CVIN

# LFO with control over rate via pot an CV (5V)
[lfo]
    hz = _LFO1_RATE_POT
    rate = _LFO1_CVIN
    level = 0.5
    bipolar = 1
    randomize = _LFO1_RANDOM_LVL
    square = _LFO1_SQUARE
    sine = _LFO1_SINE
    triangle = _LFO1_TRIANGLE
    ramp = _LFO1_RAMP
    sawtooth = _LFO1_SAWTHOOTH

[gatetool]
    inputtrigger = _LFO1_SQUARE
    outputtrigger = _LFO1_CLOCK

[random]
    clock = _LFO1_CLOCK
    minimum = -5V
    maximum = 5V
    output = _LFO1_RANDOM

[crossfader]
    input1 = _LFO1_SINE
    input2 = _LFO1_TRIANGLE
    input3 = _LFO1_RAMP
    input4 = _LFO1_SAWTHOOTH
    input5 = _LFO1_RANDOM
    fade = _LFO1_WAVEFORM_POT
    output = O7

# -------------------------------------------------
# lfo 2
# -------------------------------------------------

# Longpress on the select button (confirmed by a blinking led on the button) switches between constand level and random level
[button]
    button = B1.8
    longpress = _LFO2_SWITCH

[burst]
    trigger = _LFO2_SWITCH
    hz = 5
    count = 5
    output = _LFO2_SWITCH_LED

[flipflop]
    toggle = _LFO2_SWITCH
    output = _LFO2_RANDOM_LVL

[mixer]
    input1 = _LFO2_SWITCH_LED * -0.5 + _LED8
    input2 = _LFO2_RANDOM_LVL * _LONGPRESS_LED
    output = L1.8

# Pot for LFO level
[pot]
    select = _SELECT
    selectat = 7
    pot = P1.1
    outputscale = 20
    slope = 3
    output = _LFO2_RATE_POT

# Pot for waveshape, morphs between sine, triangle, falling and rising saw to a random source
[pot]
    select = _SELECT
    selectat = 7
    pot = P1.2
    output = _LFO2_WAVEFORM_POT

[copy]
    input = 5V
    output = N8

[fold]
    input = I8
    minimum = 0
    maximum = 0.5
    foldby = 0.01
    output = _LFO2_CVIN

# LFO with control over level via CV (5V)
[lfo]
    hz = _LFO2_RATE_POT
    rate = 0
    level = _LFO2_CVIN
    bipolar = 1
    randomize = _LFO2_RANDOM_LVL
    square = _LFO2_SQUARE
    sine = _LFO2_SINE
    triangle = _LFO2_TRIANGLE
    ramp = _LFO2_RAMP
    sawtooth = _LFO2_SAWTHOOTH

[gatetool]
    inputtrigger = _LFO2_SQUARE
    outputtrigger = _LFO2_CLOCK

[random]
    clock = _LFO2_CLOCK
    minimum = -5V
    maximum = 5V
    output = _LFO2_RANDOM

[crossfader]
    input1 = _LFO2_SINE
    input2 = _LFO2_TRIANGLE
    input3 = _LFO2_RAMP
    input4 = _LFO2_SAWTHOOTH
    input5 = _LFO2_RANDOM
    fade = _LFO2_WAVEFORM_POT
    output = O8
