# Melody sequencer with 4 tracks
# LABELS: master=18
# INPUTS:
#  I1: [CLK In] External clock
#  I2: [RST In] Reset all sequences to step 1 on a gate or trigger here

# OUTPUTS:
#  O1: [V/O 1] Pitch CV output track 1
#  O2: [Velo 1] Modulation CV output track 1
#  O3: [V/O 2] Pitch CV output track 2
#  O4: [Velo 2] Modulation CV output track 2
#  O5: [V/O 3] Pitch CV output track 3
#  O6: [Velo 3] Modulation CV output track 3
#  O7: [V/O 4] Pitch CV output track 4
#  O8: [Velo 4] Modulation CV output track 4

# GATES ON MODULE 1:
#  G1.1: [Gate 1] Gate output track 1
#  G1.2: [Gate 2] Gate output track 2
#  G1.3: [Gate 3] Gate output track 3
#  G1.4: [Gate 4] Gate output track 4

# CONTROLLER 1:
#  P1.1: [RANGE/REPEAT] Arpeggio pitch range / Repeat note shift
#  P1.2: [BASE/RATCH] Arpeggio base pitch / Ratchet note shift
#  B1.1: [ROOT] Allow root note
#  B1.2: [3RD] Allow third note
#  B1.3: [5TH] Allow fifth note
#  B1.4: [7TH] Allow seventh note
#  B1.5: [9TH] Allow nineth note
#  B1.6: [11TH] Allow eleventh note
#  B1.7: [13TH] Allow thirtheenth note
#  B1.8: [CTRL] Switch some of the button functions

# CONTROLLER 2:
#  B2.1: [Preset A] Ctrl: Switch to preset A, long press: save to preset A
#  B2.2: [Preset B] Ctrl: Switch to preset B, long press: save to preset B
#  B2.3: [Preset C] Ctrl: Switch to preset C, long press: save to preset C
#  B2.4: [CLR] Short press: reset play mode, with CTRL: clear track, long press factory reset
#  B2.5: [TBR] If enabled, transposes the melody when the root note changes.
#  B2.6: [INV] Invert the melody (change low to high and vice versa)
#  B2.7: [AAAB] Switch between the song from A, AAAB and AABB
#  B2.8: [CHRO] Disable scale and note selection, allow all 12 notes
#  B2.9: [ARP] Switch this track to arpeggio mode
#  B2.10: [LUCK] Randomly change step buttons, if Ctrl is held, change the gates instead.
#  B2.11: [RST] Reset current track (Ctrl: all track) to step 1 immediately
#  B2.12: [START] Start/stop sequencer - start also does reset
#  B2.13: [TON] Tonality menu
#  B2.14: [CLK] Menu for clock speed and swing
#  B2.15: [TRK] Track menu
#  B2.16: [PERF] Performance menu
#  B2.17: [RATC] Number of sub gates per clock
#  B2.18: [REP] Number of repeats, skip steps
#  B2.19: [PAT] Pattern for repeated gates
#  B2.20: [S/E] Set start and end (range of steps to play)
#  B2.21: [RAND] Pitch randomization
#  B2.22: [ACT] Set step activities
#  B2.23: [PROB] Gate probability
#  B2.24: [COPY] Copy current page of current track. With CTRL: Paste
#  B2.25: [NOTE] Set motor faders to pitch
#  B2.26: [VELO] Velocity / Additional gate
#  B2.27: [GL] Gate length and glide
#  B2.28: [9 - 16] Select second page of steps
#  B2.29: [Track 1] Select track 1
#  B2.30: [Track 2] Select track 2
#  B2.31: [Track 3] Select track 3
#  B2.32: [Track 4] Select track 4

[p2b8]
[b32]
[m4]
[m4]

# -------------------------------------------------
# Tonality
# -------------------------------------------------

# Set the root note (from C, C#, D, ... to C again)
[motorfader]
    select = _GLOBAL_FADER_SELECTION
    selectat = 97
    fader = 1
    notches = 13
    ledcolor = 1.1
    ledvalue = 1
    output = _ROOT

# Set the musical scale
[motorfader]
    select = _GLOBAL_FADER_SELECTION
    selectat = 97
    fader = 2
    notches = 12
    startvalue = 7
    ledcolor = _COLOR_DEGREE_FADER
    ledvalue = 1
    output = _DEGREE

# Select the color for the scale fader
[multicompare]
    input = _DEGREE
    compare1 = 0
    ifequal1 = 0.5
    compare2 = 1
    ifequal2 = 0.4
    compare3 = 2
    ifequal3 = 0.73
    compare4 = 6
    ifequal4 = 0.2
    compare5 = 7
    ifequal5 = 1.2
    compare6 = 9
    ifequal6 = 1.1
    else = 0
    output = _COLOR_DEGREE_FADER

# Global octave switch for all tracks
[motorfader]
    select = _GLOBAL_FADER_SELECTION
    selectat = 97
    fader = 3
    notches = 5
    startvalue = 2
    ledcolor = 0.2
    ledvalue = _LED_GLOBAL_OCTAVE
    output = _GLOBAL_OCTAVE_SWITCH
    button = _RESET_OCTAVE_SWITCH
    clear = _RESET_OCTAVE_SWITCH

# Make the LED just a bit brighter if the switch is at neutral position
[compare]
    input = _GLOBAL_OCTAVE_SWITCH
    compare = 2
    ifequal = 1
    else = 0.3
    output = _LED_GLOBAL_OCTAVE

# Global diatonic transposition for all tracks
[motorfader]
    select = _GLOBAL_FADER_SELECTION
    selectat = 97
    fader = 4
    notches = 15
    startvalue = 7
    ledcolor = 0.4
    ledvalue = _LED_GLOBAL_NOTESHIFT
    output = _GLOBAL_NOTESHIFT
    button = _RESET_NOTESHIFT
    clear = _RESET_NOTESHIFT

# Prevent the pitch to change between clock ticks
[sample]
    input = _GLOBAL_NOTESHIFT
    sample = _CLOCK
    output = _GLOBAL_NOTESHIFT_CLOCKED

# Make the LED just a bit brighter if the switch is at neutral position
[compare]
    input = _GLOBAL_NOTESHIFT
    compare = 7
    ifequal = 1
    else = 0.3
    output = _LED_GLOBAL_NOTESHIFT

# Global transposition by semitones for all tracks
[motorfader]
    select = _GLOBAL_FADER_SELECTION
    selectat = 97
    fader = 5
    notches = 25
    startvalue = 12
    ledcolor = 0.85
    ledvalue = _LED_GLOBAL_TRANSPOSITION
    output = _GLOBAL_TRANSPOSITION
    button = _RESET_TRANSPOSITION
    clear = _RESET_TRANSPOSITION

# Make the LED just a bit brighter if the switch is at neutral position
[compare]
    input = _GLOBAL_TRANSPOSITION
    compare = 12
    ifequal = 1
    else = 0.3
    output = _LED_GLOBAL_TRANSPOSITION

# Global transposition by semitones for all tracks
[motorfader]
    select = _GLOBAL_FADER_SELECTION
    selectat = 97
    fader = 6
    notches = 3
    startvalue = 0
    ledcolor = 0
    ledvalue = _TUNING_LED + 0.3
    output = _TUNING_COMPOSE

# Make tuning mode or compose mode active depending on the fader position.
[switch]
    input1 = 1
    offset = _TUNING_COMPOSE
    output2 = _COMPOSEMODE
    output3 = _TUNINGMODE

# Let the LED blink so that we are aware of the special mode
[lfo]
    hz = 5
    level = 0.7 * _TUNING_COMPOSE
    square = _TUNING_LED

# Glide length (duration of slides)
[motorfader]
    select = _GLOBAL_FADER_SELECTION * _SELECTED_TRACK
    selectat = 97
    fader = 7
    startvalue = 0.3
    ledcolor = 0.5
    ledvalue = 1
    output = _T1_GLIDE_LENGTH_FADER

# Make glide reasonably fast, make fader curve zoomed in in the short glide times.
[math]
    input1 = _T1_GLIDE_LENGTH_FADER * 0.5
    input2 = 3
    power = _T1_GLIDE_LENGTH

# Set the CV range for track 1
[motorfader]
    select = _GLOBAL_FADER_SELECTION * _SELECTED_TRACK
    selectat = 97
    fader = 8
    notches = 6
    startvalue = 1
    ledcolor = 0.65
    ledvalue = _TUNING_LED + 0.3
    output = _T1_CVRANGE

# Glide length (duration of slides)
[motorfader]
    select = _GLOBAL_FADER_SELECTION * _SELECTED_TRACK
    selectat = 194
    fader = 7
    startvalue = 0.3
    ledcolor = 0.5
    ledvalue = 1
    output = _T2_GLIDE_LENGTH_FADER

# Make glide reasonably fast, make fader curve zoomed in in the short glide times.
[math]
    input1 = _T2_GLIDE_LENGTH_FADER * 0.5
    input2 = 3
    power = _T2_GLIDE_LENGTH

# Set the CV range for track 2
[motorfader]
    select = _GLOBAL_FADER_SELECTION * _SELECTED_TRACK
    selectat = 194
    fader = 8
    notches = 6
    startvalue = 1
    ledcolor = 0.65
    ledvalue = _TUNING_LED + 0.3
    output = _T2_CVRANGE

# Glide length (duration of slides)
[motorfader]
    select = _GLOBAL_FADER_SELECTION * _SELECTED_TRACK
    selectat = 291
    fader = 7
    startvalue = 0.3
    ledcolor = 0.5
    ledvalue = 1
    output = _T3_GLIDE_LENGTH_FADER

# Make glide reasonably fast, make fader curve zoomed in in the short glide times.
[math]
    input1 = _T3_GLIDE_LENGTH_FADER * 0.5
    input2 = 3
    power = _T3_GLIDE_LENGTH

# Set the CV range for track 3
[motorfader]
    select = _GLOBAL_FADER_SELECTION * _SELECTED_TRACK
    selectat = 291
    fader = 8
    notches = 6
    startvalue = 1
    ledcolor = 0.65
    ledvalue = _TUNING_LED + 0.3
    output = _T3_CVRANGE

# Glide length (duration of slides)
[motorfader]
    select = _GLOBAL_FADER_SELECTION * _SELECTED_TRACK
    selectat = 388
    fader = 7
    startvalue = 0.3
    ledcolor = 0.5
    ledvalue = 1
    output = _T4_GLIDE_LENGTH_FADER

# Make glide reasonably fast, make fader curve zoomed in in the short glide times.
[math]
    input1 = _T4_GLIDE_LENGTH_FADER * 0.5
    input2 = 3
    power = _T4_GLIDE_LENGTH

# Set the CV range for track 4
[motorfader]
    select = _GLOBAL_FADER_SELECTION * _SELECTED_TRACK
    selectat = 388
    fader = 8
    notches = 6
    startvalue = 1
    ledcolor = 0.65
    ledvalue = _TUNING_LED + 0.3
    output = _T4_CVRANGE

# -------------------------------------------------
# Clocking
# -------------------------------------------------

# Selects 100s of BPM
[motorfader]
    select = _GLOBAL_FADER_SELECTION
    selectat = 99
    fader = 1
    notches = 3
    output = _CLOCK_BPM_100
    startvalue = 1
    ledcolor = 0.2
    ledvalue = 1

# Selects 10s of BPM
[motorfader]
    select = _GLOBAL_FADER_SELECTION
    selectat = 99
    fader = 2
    notches = 10
    output = _CLOCK_BPM_10
    startvalue = 2
    ledcolor = 0.2
    ledvalue = 1

# Selects 1s of BPM
[motorfader]
    select = _GLOBAL_FADER_SELECTION
    selectat = 99
    fader = 3
    notches = 10
    output = _CLOCK_BPM_1
    startvalue = 0
    ledcolor = 0.2
    ledvalue = 1

# Clock bend half speed / double speed. Touch button to reset.
[motorfader]
    select = _GLOBAL_FADER_SELECTION
    selectat = 99
    fader = 4
    output = _CLOCK_BEND_FADER
    startvalue = 0.5
    ledcolor = _CLOCK_BEND_COLOR
    ledvalue = 1
    button = _RESET_CLOCK_BEND
    clear = _RESET_CLOCK_BEND

# Snap to the perfectly neutral position if the fader is almost there.
[pot]
    pot = _CLOCK_BEND_FADER
    notch = 0.01
    output = _CLOCK_BEND

# Let the LED light bright if the bending alteration is exactly zero.
[compare]
    input = _CLOCK_BEND
    compare = 0.5
    precision = 0
    ifequal = 0.4
    else = 0.8
    output = _CLOCK_BEND_COLOR

# Add up all three BPM faders
[mixer]
    input1 = _CLOCK_BPM_100 * 100
    input2 = _CLOCK_BPM_10 * 10
    input3 = _CLOCK_BPM_1
    output = _CLOCK_BPM_UNMUTED

# Clock swing (shuffle)
[motorfader]
    select = _GLOBAL_FADER_SELECTION
    selectat = 99
    fader = 5
    output = _CLOCK_SWING
    ledcolor = 1.1
    ledvalue = _CLOCK_SWING * 0.9 + 0.1

# Small hack for letting the user toggle the state with the touch button
[switch]
    input1 = -1 * _INT_CLOCK_UNMUTED + 1
    input2 = 1
    offset = B2.4
    output1 = _CLOCK_MUTE_STARTVALUE

# Mute the clock, switch to external clocks if present. Touch button to toggle.
[motorfader]
    select = _GLOBAL_FADER_SELECTION
    selectat = 99
    fader = 6
    notches = 2
    output = _INT_CLOCK_UNMUTED
    button = _TOGGLE_CLOCK_MUTE
    clear = _TOGGLE_CLOCK_MUTE
    startvalue = _CLOCK_MUTE_STARTVALUE
    ledcolor = _INT_CLOCK_UNMUTED * -0.4 + 0.8
    ledvalue = 1

# Set the BPM to zero while the clock is muted.
[copy]
    input = _CLOCK_BPM_UNMUTED * _INT_CLOCK_UNMUTED
    output = _CLOCK_BPM

# Range of pitch accumulator
[motorfader]
    select = _GLOBAL_FADER_SELECTION * _SELECTED_TRACK
    selectat = 99
    fader = 8
    startvalue = 3
    notches = 17
    ledcolor = 1.2
    ledvalue = 1
    output = _T1_ACCU_RANGE

# Range of pitch accumulator
[motorfader]
    select = _GLOBAL_FADER_SELECTION * _SELECTED_TRACK
    selectat = 198
    fader = 8
    startvalue = 3
    notches = 17
    ledcolor = 1.2
    ledvalue = 1
    output = _T2_ACCU_RANGE

# Range of pitch accumulator
[motorfader]
    select = _GLOBAL_FADER_SELECTION * _SELECTED_TRACK
    selectat = 297
    fader = 8
    startvalue = 3
    notches = 17
    ledcolor = 1.2
    ledvalue = 1
    output = _T3_ACCU_RANGE

# Range of pitch accumulator
[motorfader]
    select = _GLOBAL_FADER_SELECTION * _SELECTED_TRACK
    selectat = 396
    fader = 8
    startvalue = 3
    notches = 17
    ledcolor = 1.2
    ledvalue = 1
    output = _T4_ACCU_RANGE

# Split clock bending fader in two hemispheres, so the the lower one is linear and can go to
# 0 Hz, while the upper half is going to exactly double speed.
[pot]
    pot = _CLOCK_BEND
    lefthalfinv = _CLOCK_BEND_LINEAR
    righthalf = _CLOCK_BEND_EXP

# Convert BPM (beat = quarter note) to 16th note clock measured in Hz
[copy]
    input = _CLOCK_BPM / 15
    output = _CLOCK_HZ

# The actual clock, use a very short pulse so we have essentially just triggers
[lfo]
    hz = _CLOCK_HZ * _CLOCK_BEND_LINEAR
    rate = _CLOCK_BEND_EXP * 1V
    pulsewidth = 0.1
    square = _CLOCK_UNSWUNG

# Swing for our internal clock
[timing]
    clock = _CLOCK_UNSWUNG
    reset = _RESET
    timing1 = 0
    timing2 = _CLOCK_SWING * 0.3
    output = _INT_CLOCK

# Detect an external clock signal, if within the last 2 seconds there was a trigger
[gatetool]
    inputtrigger = I1
    gatelength = 2
    outputgate = _EXTERNAL_CLOCK_PRESENT

# Get clock via MIDI TRS jack port 1
[midiin]
    trs = 1
    clock = _TRS1_CLOCK

# Detect a clock signal, if within the last 2 seconds there is a pulse
[gatetool]
    inputtrigger = _TRS1_CLOCK
    gatelength = 2
    outputgate = _TRS1_CLOCK_PRESENT

# Get clock via MIDI TRS jack port 2
[midiin]
    trs = 2
    clock = _TRS2_CLOCK

# Detect a clock signal, if within the last 2 seconds there is a pulse
[gatetool]
    inputtrigger = _TRS2_CLOCK
    gatelength = 2
    outputgate = _TRS2_CLOCK_PRESENT

# Get clock via MIDI USB jack port 1
[midiin]
    usb = 1
    clock = _USB1_CLOCK

# Detect a clock signal, if within the last 2 seconds there is a pulse
[gatetool]
    inputtrigger = _USB1_CLOCK
    gatelength = 2
    outputgate = _USB1_CLOCK_PRESENT

# Select first avaiable clock in defined precedence order
[case]
    case1 = _INT_CLOCK_UNMUTED
    value1 = _INT_CLOCK
    case2 = _EXTERNAL_CLOCK_PRESENT
    value2 = I1
    case3 = _TRS1_CLOCK_PRESENT
    value3 = _TRS1_CLOCK
    case4 = _TRS2_CLOCK_PRESENT
    value4 = _TRS2_CLOCK
    case5 = _USB1_CLOCK_PRESENT
    value5 = _USB1_CLOCK
    output = _CLOCK

# Pulses for changes via lucky buttons / lucky faders
[lfo]
    hz = 40
    square = _LUCKY_CLOCK

# Clock division 3/2 for the arpeggiators
[clocktool]
    clock = _CLOCK
    output = _CLOCK_3_2
    multiply = 3
    divide = 2

# Clock division 2/1 for the arpeggiators
[clocktool]
    clock = _CLOCK
    output = _CLOCK_2_1
    multiply = 2
    divide = 1

# Clock division 3/1 for the arpeggiators
[clocktool]
    clock = _CLOCK
    output = _CLOCK_3_1
    multiply = 3
    divide = 1

# Clock division 4/1 for the arpeggiators
[clocktool]
    clock = _CLOCK
    output = _CLOCK_4_1
    multiply = 4
    divide = 1

# Clock division 6/1 for the arpeggiators
[clocktool]
    clock = _CLOCK
    output = _CLOCK_6_1
    multiply = 6
    divide = 1

# Clock division 8/1 for the arpeggiators
[clocktool]
    clock = _CLOCK
    output = _CLOCK_8_1
    multiply = 8
    divide = 1

# -------------------------------------------------
# Global buttons
# -------------------------------------------------

# Animation of track buttons when muted.
[lfo]
    level = 0.8
    bipolar = 0
    square = _MUTE_BLINK
    pulsewidth = 0.2
    hz = 2.5

# Mute button for track 1 (same as track button)
[button]
    select = _CONTROL
    button = B2.29
    led = L2.29
    output = _T1_MUTED

# Compute state of track LED based on wether it is selected and/or muted
[switch]
    offset = _T1_SELECTED * 2 + _T1_MUTED
    input1 = 0
    input2 = _MUTE_BLINK
    input3 = 1
    input4 = -1 * _MUTE_BLINK + 1
    output1 = _T1_TRACKLED

# Show mute state of track while CTRL is held
[compare]
    input = _CONTROL
    compare = 1
    ifequal = _T1_MUTED
    else = _T1_TRACKLED
    output = L2.29

# Mute button for track 2 (same as track button)
[button]
    select = _CONTROL
    button = B2.30
    led = L2.30
    output = _T2_MUTED

# Compute state of track LED based on wether it is selected and/or muted
[switch]
    offset = _T2_SELECTED * 2 + _T2_MUTED
    input1 = 0
    input2 = _MUTE_BLINK
    input3 = 1
    input4 = -1 * _MUTE_BLINK + 1
    output1 = _T2_TRACKLED

# Show mute state of track while CTRL is held
[compare]
    input = _CONTROL
    compare = 1
    ifequal = _T2_MUTED
    else = _T2_TRACKLED
    output = L2.30

# Mute button for track 3 (same as track button)
[button]
    select = _CONTROL
    button = B2.31
    led = L2.31
    output = _T3_MUTED

# Compute state of track LED based on wether it is selected and/or muted
[switch]
    offset = _T3_SELECTED * 2 + _T3_MUTED
    input1 = 0
    input2 = _MUTE_BLINK
    input3 = 1
    input4 = -1 * _MUTE_BLINK + 1
    output1 = _T3_TRACKLED

# Show mute state of track while CTRL is held
[compare]
    input = _CONTROL
    compare = 1
    ifequal = _T3_MUTED
    else = _T3_TRACKLED
    output = L2.31

# Mute button for track 4 (same as track button)
[button]
    select = _CONTROL
    button = B2.32
    led = L2.32
    output = _T4_MUTED

# Compute state of track LED based on wether it is selected and/or muted
[switch]
    offset = _T4_SELECTED * 2 + _T4_MUTED
    input1 = 0
    input2 = _MUTE_BLINK
    input3 = 1
    input4 = -1 * _MUTE_BLINK + 1
    output1 = _T4_TRACKLED

# Show mute state of track while CTRL is held
[compare]
    input = _CONTROL
    compare = 1
    ifequal = _T4_MUTED
    else = _T4_TRACKLED
    output = L2.32

# Track selection
[buttongroup]
    output = _SELECTED_TRACK
    select = _NO_CONTROL
    button1 = B2.29
    value1 = 1
    led1 = _T1_SELECTED
    button2 = B2.30
    value2 = 2
    led2 = _T2_SELECTED
    button3 = B2.31
    value3 = 3
    led3 = _T3_SELECTED
    button4 = B2.32
    value4 = 4
    led4 = _T4_SELECTED

# -------------------------------------------------
# Special menus
# -------------------------------------------------

# Select a global menu function of the faders. There is no button for "normal" in order to save
# buttons. Simply press the currently lit button to deactivate all menu buttons and go back
# to controlling the sequence.
[buttongroup]
    button1 = B2.13
    led1 = L2.13
    value1 = 97
    button2 = B2.14
    led2 = L2.14
    value2 = 99
    button3 = B2.15
    led3 = L2.15
    value3 = _SELECTED_TRACK + 10
    button4 = B2.16
    led4 = L2.16
    value4 = 98
    clear = _FADERMODE_CHANGED
    startbutton = 0
    minactive = 0
    maxactive = 1
    output = _GLOBAL_MENU

# If there is no menu active, the fader edit the sequence of the currently selected track.
[compare]
    input = _GLOBAL_MENU
    compare = 0
    ifequal = _SELECTED_TRACK
    else = _GLOBAL_MENU
    output = _GLOBAL_FADER_SELECTION

# A long press on the clear button resets all settings of the sequencer to its default state
[button]
    button = B2.4
    states = 1
    longpress = _FACTORY_RESET

# Do the factory reset
[droid]
    clearall = _FACTORY_RESET

# Provide state of the control button in a patch cable.
[copy]
    input = B1.8
    output = _CONTROL

# Inverted state of control button, is 1 of not held.
[copy]
    input = -1 * B1.8 + 1
    output = _NO_CONTROL

# Select the current mode of the faders for the sequencer
[buttongroup]
    button1 = B2.25
    led1 = L2.25
    value1 = 0
    button2 = B2.26
    led2 = L2.26
    value2 = 10
    button3 = B2.18
    led3 = L2.18
    value3 = 3
    button4 = B2.19
    led4 = L2.19
    value4 = 4
    button5 = B2.21
    led5 = L2.21
    value5 = 1
    button6 = B2.27
    led6 = L2.27
    value6 = 20
    button7 = B2.17
    led7 = L2.17
    value7 = 5
    button8 = B2.23
    led8 = L2.23
    value8 = 2
    button9 = B2.22
    led9 = L2.22
    value9 = 30
    buttonpress = _FADERMODE_CHANGED
    output = _FADERMODE

# Select a button mode that is most useful in the current fader mode
[multicompare]
    input = _FADERMODE
    compare1 = 3
    ifequal1 = 3
    compare2 = 4
    ifequal2 = 2
    else = 0
    output = _BM
    compare3 = 20
    ifequal3 = 20

# When the range button is active, it always has precedence
[compare]
    input = _RANGE
    compare = 0
    ifequal = _BM
    else = 1
    output = _BUTTONMODE

# Button for choosing between gates on/off and start/end
[button]
    button = B2.20
    led = L2.20
    states = 1
    output = _RANGE

# Button for reseting all tracks to step 1
[button]
    button = B2.11 * _CONTROL
    led = L2.11
    states = 1
    output = _RESET_BUTTON

# Combine all source of global reset
[mixer]
    input1 = _RESET_BUTTON
    input2 = I2
    input3 = _STARTSTOP_RESET
    output = _RESET

# Button for run/stop
[button]
    led = L2.12
    output = _RUNNING
    button = B2.12
    startvalue = 1

# Create a reset trigger when the button changes to 'running'
[gatetool]
    inputgate = _RUNNING
    outputtrigger = _STARTSTOP_RESET

# Button for selecting the second page of steps
[button]
    button = B2.28
    led = L2.28
    output = _PAGE

# -------------------------------------------------
# Track 1
# -------------------------------------------------

# Provide trigger when the clear button is pressed (no Ctrl needed)
[button]
    select = _SELECTED_TRACK
    selectat = 1
    button = B2.4
    shortpress = _T1_CLEAR_BUTTON
    states = 1

# Clear button with control together clears the track
[copy]
    input = _T1_CLEAR_BUTTON * _CONTROL
    output = _T1_CLEAR

# Clear button without control removes all mode that alter the pattern length
[logic]
    input1 = _T1_CLEAR_BUTTON
    input2 = -1 * _CONTROL + 1
    and = _T1_PEACE

# Five blinks for the clear LED
[burst]
    trigger = _T1_CLEAR + _FACTORY_RESET
    hz = 5
    count = 5
    output = _T1_CLEAR_LED

# LED only blinks when track is selected
[select]
    select = _SELECTED_TRACK
    selectat = 1
    input = _T1_CLEAR_LED + 0.3
    output = L2.4

# Reset track immediately
[button]
    select = _SELECTED_TRACK
    selectat = 1
    button = B2.11 * _NO_CONTROL
    states = 1
    output = _T1_RESET

# Button for copying the current page of the current track
[button]
    select = _SELECTED_TRACK
    selectat = 1
    button = B2.24
    states = 1
    output = _T1_COPY_BUTTON

# This button cycles between the song froms A, AAAB and AABB
[button]
    select = _SELECTED_TRACK
    selectat = 1
    clear = _T1_CLEAR
    loadpreset = _T1_LOAD_PRESET
    savepreset = _T1_SAVE_PRESET
    button = B2.7
    led = L2.7
    output = _T1_FORM
    states = 3

# Disables scale and note selection if on
[button]
    select = _SELECTED_TRACK
    selectat = 1
    loadpreset = _T1_LOAD_PRESET
    savepreset = _T1_SAVE_PRESET
    button = B2.8
    led = L2.8
    output = _T1_12TONE

# Enable transposing the melody by the difference of the root note to C
[button]
    select = _SELECTED_TRACK
    selectat = 1
    loadpreset = _T1_LOAD_PRESET
    savepreset = _T1_SAVE_PRESET
    button = B2.5
    led = L2.5
    output = _T1_TBR_ON
    inverted = _T1_TBR_OFF

# Provide the amount of 'transpose by root' in a patch cable
[compare]
    input = _T1_TBR_ON
    compare = 1
    ifequal = _ROOT / 120
    output = _T1_TBR

# Hold to contiously step faders to random positions, hold with Ctrl to change buttons
[button]
    select = _SELECTED_TRACK
    selectat = 1
    button = B2.10
    states = 1
    onvalue = _LUCKY_CLOCK
    output = _T1_LUCKY

# Selects melodic inversion (swap low and high notes)
[button]
    select = _SELECTED_TRACK
    selectat = 1
    clear = _T1_CLEAR
    loadpreset = _T1_LOAD_PRESET
    savepreset = _T1_SAVE_PRESET
    button = B2.6
    led = L2.6
    output = _T1_INVERT

# Make LED brighter if the fader is in its default position
[compare]
    input = _T1_AUTORESET
    compare = 0
    ifequal = 1
    else = 0.3
    output = _T1_AUTORESET_LED

# Combine all triggers for clearing
[mixer]
    input1 = _T1_CLEAR + _T1_PEACE
    input2 = _T1_BPAR_1
    output = _T1_CLEAR_PAR

# Reset track after X clock ticks
[motorfader]
    select = _GLOBAL_FADER_SELECTION * _T1_ARP_OFF
    selectat = 11
    loadpreset = _T1_LOAD_PRESET
    savepreset = _T1_SAVE_PRESET
    notches = 17
    startvalue = 0
    ledcolor = 0.8
    ledvalue = _T1_AUTORESET_LED
    clear = _T1_CLEAR_PAR
    output = _T1_AUTORESET
    fader = 1
    button = _T1_BPAR_1

# Make LED brighter if the fader is in its default position
[compare]
    input = _T1_SHIFTSTEPS
    compare = 0
    ifequal = 1
    else = 0.3
    output = _T1_SHIFTSTEPS_LED

# Combine all triggers for clearing
[mixer]
    input1 = _T1_CLEAR + _T1_PEACE
    input2 = _T1_BPSS_1
    output = _T1_CLEAR_PSS

# Shift the steps of the sequence by this number
[motorfader]
    select = _GLOBAL_FADER_SELECTION * _T1_ARP_OFF
    selectat = 11
    loadpreset = _T1_LOAD_PRESET
    savepreset = _T1_SAVE_PRESET
    notches = 17
    startvalue = 0
    ledcolor = 0.6
    ledvalue = _T1_SHIFTSTEPS_LED
    clear = _T1_CLEAR_PSS
    output = _T1_SHIFTSTEPS
    fader = 2
    button = _T1_BPSS_1

# Make LED brighter if the fader is in its default position
[compare]
    input = _T1_OCTAVE
    compare = 2
    ifequal = 1
    else = 0.3
    output = _T1_OCTAVE_LED

# Combine all triggers for clearing
[mixer]
    input1 = _T1_CLEAR
    input2 = _T1_BPOCT_1 + _T1_BPOCT_2
    output = _T1_CLEAR_POCT

# Octave switch: -2, -1, 0, +1, +2
[motorfader]
    select = _GLOBAL_FADER_SELECTION * _T1_ARP_OFF
    selectat = 11
    loadpreset = _T1_LOAD_PRESET
    savepreset = _T1_SAVE_PRESET
    notches = 5
    startvalue = 2
    ledcolor = 0.2
    ledvalue = _T1_OCTAVE_LED
    clear = _T1_CLEAR_POCT
    sharewithnext = 1
    fader = 3
    button = _T1_BPOCT_1

# Octave switch: -2, -1, 0, +1, +2
[motorfader]
    loadpreset = _T1_LOAD_PRESET
    savepreset = _T1_SAVE_PRESET
    notches = 5
    startvalue = 2
    ledcolor = 0.2
    ledvalue = _T1_OCTAVE_LED
    clear = _T1_CLEAR_POCT
    select = _GLOBAL_FADER_SELECTION
    selectat = 98
    fader = 1
    button = _T1_BPOCT_2
    output = _T1_OCTAVE

# Make LED brighter if the fader is in its default position
[compare]
    input = _T1_NOTESHIFT
    compare = 7
    ifequal = 1
    else = 0.3
    output = _T1_NOTESHIFT_LED

# Combine all triggers for clearing
[mixer]
    input1 = _T1_CLEAR
    input2 = _T1_BPDT_1 + _T1_BPDT_2
    output = _T1_CLEAR_PDT

# Diatonic transposition
[motorfader]
    select = _GLOBAL_FADER_SELECTION * _T1_ARP_OFF
    selectat = 11
    loadpreset = _T1_LOAD_PRESET
    savepreset = _T1_SAVE_PRESET
    notches = 15
    startvalue = 7
    ledcolor = 0.4
    ledvalue = _T1_NOTESHIFT_LED
    clear = _T1_CLEAR_PDT
    sharewithnext = 1
    fader = 4
    button = _T1_BPDT_1

# Diatonic transposition
[motorfader]
    loadpreset = _T1_LOAD_PRESET
    savepreset = _T1_SAVE_PRESET
    notches = 15
    startvalue = 7
    ledcolor = 0.4
    ledvalue = _T1_NOTESHIFT_LED
    clear = _T1_CLEAR_PDT
    select = _GLOBAL_FADER_SELECTION
    selectat = 98
    fader = 2
    button = _T1_BPDT_2
    output = _T1_NOTESHIFT

# Prevent diatonic transposition from happending between clock ticks
[sample]
    input = _T1_NOTESHIFT
    sample = _CLOCK
    output = _T1_NOTESHIFT_CLOCKED

# Make LED brighter if the fader is in its default position
[compare]
    input = _T1_ACTIVITY
    compare = 0
    ifequal = 1
    else = 0.3
    output = _T1_ACTIVITY_LED

# Combine all triggers for clearing
[mixer]
    input1 = _T1_CLEAR
    input2 = _T1_BPACT_1
    output = _T1_CLEAR_PACT

# Minimum activity a step needs to have to be played
[motorfader]
    select = _GLOBAL_FADER_SELECTION * _T1_ARP_OFF
    selectat = 11
    loadpreset = _T1_LOAD_PRESET
    savepreset = _T1_SAVE_PRESET
    notches = 8
    startvalue = 0
    ledcolor = 0.55
    ledvalue = _T1_ACTIVITY_LED
    clear = _T1_CLEAR_PACT
    output = _T1_ACTIVITY
    fader = 5
    button = _T1_BPACT_1

# Make LED brighter if the fader is in its default position
[compare]
    input = _T1_PATTERN_FADER
    compare = 0
    ifequal = 1
    else = 0.3
    output = _T1_PATTERN_FADER_LED

# Combine all triggers for clearing
[mixer]
    input1 = _T1_CLEAR + _T1_PEACE
    input2 = _T1_BPPAT_1
    output = _T1_CLEAR_PPAT

# Movement pattern: normal, reverse, ping pong, >><, 2><, etc.
[motorfader]
    select = _GLOBAL_FADER_SELECTION * _T1_ARP_OFF
    selectat = 11
    loadpreset = _T1_LOAD_PRESET
    savepreset = _T1_SAVE_PRESET
    notches = 10
    startvalue = 0
    ledcolor = 0.3
    ledvalue = _T1_PATTERN_FADER_LED
    clear = _T1_CLEAR_PPAT
    output = _T1_PATTERN_FADER
    fader = 6
    button = _T1_BPPAT_1

# Set the direction to reverse on fader position 1
[compare]
    input = _T1_PATTERN_FADER
    compare = 1
    ifequal = 1
    output = _T1_DIRECTION

# Enable ping pong on fader position 2
[compare]
    input = _T1_PATTERN_FADER
    compare = 2
    ifequal = 1
    output = _T1_PINGPONG

# Make LED brighter if the fader is in its default position
[compare]
    input = _T1_CD_EVEN
    compare = 3
    ifequal = 1
    else = 0.3
    output = _T1_CD_EVEN_LED

# Combine all triggers for clearing
[mixer]
    input1 = _T1_CLEAR
    input2 = _T1_BPCDEVEN_1
    output = _T1_CLEAR_PCDEVEN

# Clock division for even divides/multipliers
[motorfader]
    select = _GLOBAL_FADER_SELECTION * _T1_ARP_OFF
    selectat = 11
    loadpreset = _T1_LOAD_PRESET
    savepreset = _T1_SAVE_PRESET
    notches = 7
    startvalue = 3
    ledcolor = 1.1
    ledvalue = _T1_CD_EVEN_LED
    clear = _T1_CLEAR_PCDEVEN
    output = _T1_CD_EVEN
    fader = 7
    button = _T1_BPCDEVEN_1

# Select the multiplier based on the fader position
[switch]
    input1 = 1
    input2 = 1
    input3 = 1
    input4 = 1
    input5 = 2
    input6 = 4
    input7 = 8
    offset = _T1_CD_EVEN
    output1 = _T1_CD_EVEN_MULT

# Select the division based on the fader position
[switch]
    input1 = 8
    input2 = 4
    input3 = 2
    input4 = 1
    input5 = 1
    input6 = 1
    input7 = 1
    offset = _T1_CD_EVEN
    output1 = _T1_CD_EVEN_DIV

# Make LED brighter if the fader is in its default position
[compare]
    input = _T1_CD_ODD
    compare = 3
    ifequal = 1
    else = 0.3
    output = _T1_CD_ODD_LED

# Combine all triggers for clearing
[mixer]
    input1 = _T1_CLEAR
    input2 = _T1_BPCDODD_1
    output = _T1_CLEAR_PCDODD

# Clock division for odd divides/multipliers
[motorfader]
    select = _GLOBAL_FADER_SELECTION * _T1_ARP_OFF
    selectat = 11
    loadpreset = _T1_LOAD_PRESET
    savepreset = _T1_SAVE_PRESET
    notches = 7
    startvalue = 3
    ledcolor = 1.2
    ledvalue = _T1_CD_ODD_LED
    clear = _T1_CLEAR_PCDODD
    output = _T1_CD_ODD
    fader = 8
    button = _T1_BPCDODD_1

# Select the multiplier based on the fader position
[switch]
    input1 = 1
    input2 = 1
    input3 = 1
    input4 = 1
    input5 = 3
    input6 = 5
    input7 = 7
    offset = _T1_CD_ODD
    output1 = _T1_CD_ODD_MULT

# Select the division based on the fader position
[switch]
    input1 = 7
    input2 = 5
    input3 = 3
    input4 = 1
    input5 = 1
    input6 = 1
    input7 = 1
    offset = _T1_CD_ODD
    output1 = _T1_CD_ODD_DIV

# Switch on/off arpeggio mode for track 1
[button]
    select = _SELECTED_TRACK
    selectat = 1
    button = B2.9
    led = L2.9
    output = _T1_ARP
    negated = _T1_ARP_OFF

# Auto reset arpeggiator after N number of steps
[motorfader]
    select = _GLOBAL_FADER_SELECTION * _T1_ARP
    selectat = 11
    loadpreset = _T1_LOAD_PRESET
    savepreset = _T1_SAVE_PRESET
    fader = 1
    notches = 17
    ledcolor = 0.8
    ledvalue = 1
    output = _T1_ARP_AUTORESET

# Enable ping pong mode
[motorfader]
    select = _GLOBAL_FADER_SELECTION * _T1_ARP
    selectat = 11
    loadpreset = _T1_LOAD_PRESET
    savepreset = _T1_SAVE_PRESET
    fader = 2
    notches = 3
    ledcolor = 0.85
    ledvalue = 1
    output = _T1_ARP_DIRECTION

# Provide gate for reversed direction of arpeggio
[compare]
    input = _T1_ARP_DIRECTION
    compare = 2
    ifequal = 1
    else = 0
    output = _T1_ARP_UPDOWN

# Provide gate for ping pong of arpeggio
[compare]
    input = _T1_ARP_DIRECTION
    compare = 1
    ifequal = 1
    else = 0
    output = _T1_ARP_PINGPONG

# Seperate octave switch for the arpeggio
[motorfader]
    select = _GLOBAL_FADER_SELECTION * _T1_ARP
    selectat = 11
    loadpreset = _T1_LOAD_PRESET
    savepreset = _T1_SAVE_PRESET
    fader = 3
    notches = 5
    ledcolor = 0.2
    startvalue = 2
    ledvalue = 1
    output = _T1_ARP_OCTAVE_SWITCH

# Enable butterfly mode
[motorfader]
    select = _GLOBAL_FADER_SELECTION * _T1_ARP
    selectat = 11
    loadpreset = _T1_LOAD_PRESET
    savepreset = _T1_SAVE_PRESET
    fader = 4
    notches = 2
    ledcolor = 1.15
    ledvalue = 1
    output = _T1_ARP_BUTTERFLY

# Select one of three octaving patterns
[motorfader]
    select = _GLOBAL_FADER_SELECTION * _T1_ARP
    selectat = 11
    loadpreset = _T1_LOAD_PRESET
    savepreset = _T1_SAVE_PRESET
    fader = 5
    notches = 3
    ledcolor = 0.4
    ledvalue = 1
    output = _T1_ARP_OCTAVING

# Compute octave offset pitches from fader setting
[copy]
    input = _T1_ARP_OCTAVE_SWITCH * 1V - 2V
    output = _T1_ARP_OCTAVE

# Select the arpeggio pattern
[motorfader]
    select = _GLOBAL_FADER_SELECTION * _T1_ARP
    selectat = 11
    loadpreset = _T1_LOAD_PRESET
    savepreset = _T1_SAVE_PRESET
    fader = 6
    notches = 7
    ledcolor = 0.3
    ledvalue = 1
    output = _T1_ARP_PATTERN

# Select one of four different drop patterns
[motorfader]
    select = _GLOBAL_FADER_SELECTION * _T1_ARP
    selectat = 11
    loadpreset = _T1_LOAD_PRESET
    savepreset = _T1_SAVE_PRESET
    fader = 7
    notches = 4
    ledcolor = 0.5
    ledvalue = 1
    output = _T1_ARP_DROP

# Select one of several clocking patterns
[motorfader]
    select = _GLOBAL_FADER_SELECTION * _T1_ARP
    selectat = 11
    loadpreset = _T1_LOAD_PRESET
    savepreset = _T1_SAVE_PRESET
    fader = 8
    notches = 8
    ledcolor = 1.1
    ledvalue = 1
    output = _T1_ARP_CLOCKING

# Button for loading preset 1 on track 1
[button]
    select = _SELECTED_TRACK
    selectat = 1
    button = B2.1 * _CONTROL
    states = 1
    onvalue = 1
    output = _T1_P1_LOAD

# Button for saving preset 1 on track 1
[button]
    select = _SELECTED_TRACK
    selectat = 1
    button = B2.1
    states = 1
    onvalue = 1
    longpress = _T1_P1_SAVE

# Five blinks for the LED of the preset button on saving
[burst]
    trigger = _T1_P1_SAVE
    hz = 5
    count = 5
    output = _T1_P1_SAVE_LED

# Write the save LED animation to the LED if the track is still selected.
[select]
    select = _SELECTED_TRACK
    selectat = 1
    input = _T1_P1_SAVE_LED + 0.3
    output = L2.1

# Button for loading preset 2 on track 1
[button]
    select = _SELECTED_TRACK
    selectat = 1
    button = B2.2 * _CONTROL
    states = 1
    onvalue = 2
    output = _T1_P2_LOAD

# Button for saving preset 2 on track 1
[button]
    select = _SELECTED_TRACK
    selectat = 1
    button = B2.2
    states = 1
    onvalue = 2
    longpress = _T1_P2_SAVE

# Five blinks for the LED of the preset button on saving
[burst]
    trigger = _T1_P2_SAVE
    hz = 5
    count = 5
    output = _T1_P2_SAVE_LED

# Write the save LED animation to the LED if the track is still selected.
[select]
    select = _SELECTED_TRACK
    selectat = 1
    input = _T1_P2_SAVE_LED + 0.3
    output = L2.2

# Button for loading preset 3 on track 1
[button]
    select = _SELECTED_TRACK
    selectat = 1
    button = B2.3 * _CONTROL
    states = 1
    onvalue = 3
    output = _T1_P3_LOAD

# Button for saving preset 3 on track 1
[button]
    select = _SELECTED_TRACK
    selectat = 1
    button = B2.3
    states = 1
    onvalue = 3
    longpress = _T1_P3_SAVE

# Five blinks for the LED of the preset button on saving
[burst]
    trigger = _T1_P3_SAVE
    hz = 5
    count = 5
    output = _T1_P3_SAVE_LED

# Write the save LED animation to the LED if the track is still selected.
[select]
    select = _SELECTED_TRACK
    selectat = 1
    input = _T1_P3_SAVE_LED + 0.3
    output = L2.3

# Create a trigger with the preset as value when any of the presets is loaded
[mixer]
    input1 = _T1_P1_LOAD
    input2 = _T1_P2_LOAD
    input3 = _T1_P3_LOAD
    output = _T1_LOAD_PRESET

# Create a trigger with the preset as value when any of the presets is saved
[mixer]
    input1 = _T1_P1_SAVE * 1
    input2 = _T1_P2_SAVE * 2
    input3 = _T1_P3_SAVE * 3
    output = _T1_SAVE_PRESET

# Select interval note 1 from the current scale
[button]
    select = _SELECTED_TRACK
    selectat = 1
    button = B1.1
    led = L1.1
    startvalue = 1
    output = _T1_SEL_1

# Select interval note 2 from the current scale
[button]
    select = _SELECTED_TRACK
    selectat = 1
    button = B1.2
    led = L1.2
    startvalue = 1
    output = _T1_SEL_2

# Select interval note 3 from the current scale
[button]
    select = _SELECTED_TRACK
    selectat = 1
    button = B1.3
    led = L1.3
    startvalue = 1
    output = _T1_SEL_3

# Select interval note 4 from the current scale
[button]
    select = _SELECTED_TRACK
    selectat = 1
    button = B1.4
    led = L1.4
    startvalue = 1
    output = _T1_SEL_4

# Select interval note 5 from the current scale
[button]
    select = _SELECTED_TRACK
    selectat = 1
    button = B1.5
    led = L1.5
    startvalue = 1
    output = _T1_SEL_5

# Select interval note 6 from the current scale
[button]
    select = _SELECTED_TRACK
    selectat = 1
    button = B1.6
    led = L1.6
    startvalue = 1
    output = _T1_SEL_6

# Select interval note 7 from the current scale
[button]
    select = _SELECTED_TRACK
    selectat = 1
    button = B1.7
    led = L1.7
    startvalue = 1
    output = _T1_SEL_7

# Clock divider for track 1
[clocktool]
    clock = _CLOCK
    reset = _RESET
    divide = _T1_CD_EVEN_DIV * _T1_CD_ODD_DIV
    multiply = _T1_CD_EVEN_MULT * _T1_CD_ODD_MULT
    output = _T1_CLOCK

# Mix global and per-track diatonic transposition
[mixer]
    input1 = -1 * 7 + _T1_NOTESHIFT_CLOCKED
    input2 = -1 * 7 + _GLOBAL_NOTESHIFT_CLOCKED
    output = _T1_NOTESHIFT_SUM

# collect different sources of resetting the start/end point
[logic]
    input1 = _T1_CLEAR + _T1_PEACE
    input2 = _RANGE * _CONTROL
    or = _T1_CLEARSTARTEND

# Note shift for each step repetition
[pot]
    select = _T1_ARP * 100 + _SELECTED_TRACK
    selectat = 1
    pot = P1.1
    discrete = 15
    startvalue = 7
    output = _T1_REPEATSHIFT

# Note shift for each ratchet
[pot]
    select = _T1_ARP * 100 + _SELECTED_TRACK
    selectat = 1
    pot = P1.2
    discrete = 15
    startvalue = 7
    output = _T1_RATCHETSHIFT

# Sequencer for pitch and gate of track 1
[motoquencer]
    select = _GLOBAL_FADER_SELECTION
    selectat = 1
    page = _PAGE
    copy = _T1_COPY_BUTTON * _NO_CONTROL
    paste = _T1_COPY_BUTTON * _CONTROL
    loadpreset = _T1_LOAD_PRESET
    savepreset = _T1_SAVE_PRESET
    clear = _T1_CLEAR
    select1 = _T1_SEL_1 + _T1_12TONE
    select3 = _T1_SEL_2 + _T1_12TONE
    select5 = _T1_SEL_3 + _T1_12TONE
    select7 = _T1_SEL_4 + _T1_12TONE
    select9 = _T1_SEL_5 + _T1_12TONE
    select11 = _T1_SEL_6 + _T1_12TONE
    select13 = _T1_SEL_7 + _T1_12TONE
    selectfill1 = _T1_12TONE
    selectfill2 = _T1_12TONE
    selectfill3 = _T1_12TONE
    selectfill4 = _T1_12TONE
    selectfill5 = _T1_12TONE
    degree = _DEGREE
    clock = _T1_CLOCK * _RUNNING
    taptempo = _T1_CLOCK
    reset = _RESET + _T1_RESET
    cv = _T1_PITCH
    cvrange = _T1_CVRANGE * 1V + 1V
    defaultgate = 0
    gate = _T1_SEQUENCER_GATE
    numfaders = 8
    numsteps = 16
    linktonext = 1
    autoreset = _T1_AUTORESET
    shiftsteps = _T1_SHIFTSTEPS
    pattern = -1 * 2 + _T1_PATTERN_FADER
    direction = _T1_DIRECTION
    pingpong = _T1_PINGPONG
    selectnoteshift = _T1_NOTESHIFT_SUM
    tuningmode = _TUNINGMODE
    tuningpitch = _GLOBAL_OCTAVE_SWITCH * 1V
    accumulatorrange = _T1_ACCU_RANGE
    root = _ROOT * _T1_TBR_OFF
    transpose = _T1_TBR
    luckybuttons = _T1_LUCKY * _CONTROL
    luckyfaders = _T1_LUCKY * _NO_CONTROL
    luckychance = 1 / 8
    luckyamount = 0.5 * _NO_CONTROL + 0.5
    luckyscope = 3
    fadermode = _FADERMODE
    mute = -1 * _RUNNING + 1
    form = _T1_FORM
    invert = _T1_INVERT
    buttonmode = _BUTTONMODE
    composemode = _COMPOSEMODE
    clearskips = _T1_CLEAR + _T1_PEACE
    clearrepeats = _T1_CLEAR + _T1_PEACE
    clearstartend = _T1_CLEARSTARTEND
    repeatshift = -1 * 7 + _T1_REPEATSHIFT
    ratchetshift = -1 * 7 + _T1_RATCHETSHIFT

# Sequencer for velocity
[motoquencer]
    select = _GLOBAL_FADER_SELECTION
    selectat = 1
    page = _PAGE
    copy = _T1_COPY_BUTTON * _NO_CONTROL
    paste = _T1_COPY_BUTTON * _CONTROL
    clear = _T1_CLEAR
    loadpreset = _T1_LOAD_PRESET
    savepreset = _T1_SAVE_PRESET
    cv = _T1_VELOCITY
    cvrange = 1
    defaultcv = 0.5
    quantize = 0
    linktonext = 1

# Copy velocity to CV output
[copy]
    input = _T1_VELOCITY
    output = O2

# Sequencer for gate length and glide
[motoquencer]
    select = _GLOBAL_FADER_SELECTION
    selectat = 1
    page = _PAGE
    copy = _T1_COPY_BUTTON * _NO_CONTROL
    paste = _T1_COPY_BUTTON * _CONTROL
    clear = _T1_CLEAR
    loadpreset = _T1_LOAD_PRESET
    savepreset = _T1_SAVE_PRESET
    cv = _T1_GATELENGTH
    cvrange = 1
    defaultcv = 0.5
    defaultgate = 0
    quantize = 0
    buttoncolor = 0.5
    linktonext = 1
    gate = _T1_GLIDE

# Sequencer for activity level per step
[motoquencer]
    select = _GLOBAL_FADER_SELECTION
    selectat = 1
    clear = _T1_CLEAR
    loadpreset = _T1_LOAD_PRESET
    savepreset = _T1_SAVE_PRESET
    cv = _T1_STEP_ACTIVITY
    quantize = 0
    defaultcv = 7
    cvnotches = 8

# Silence the gate if the step does not reach the required activity
[compare]
    input = _T1_STEP_ACTIVITY
    compare = _T1_ACTIVITY
    ifless = 0
    else = _T1_SEQUENCER_GATE
    output = _T1_ACT_GATE

# Switch gate to arpeggiator if active
[compare]
    input = _T1_ARP
    compare = 0
    ifequal = _T1_ACT_GATE
    else = _T1_ARP_CLOCK
    output = _T1_SWITCHED_GATE

# Logic for make the gate silent if the track is muted
[logic]
    input1 = -1 * _T1_MUTED + 1
    input2 = _T1_SWITCHED_GATE
    and = _T1_MUTED_GATE

# Select clock for arpeggiator of track 1
[switch]
    input1 = _CLOCK
    input2 = _T1_SEQUENCER_GATE
    input3 = _CLOCK_3_2
    input4 = _CLOCK_2_1
    input5 = _CLOCK_3_1
    input6 = _CLOCK_4_1
    input7 = _CLOCK_6_1
    input8 = _CLOCK_8_1
    output1 = _T1_ARP_CLOCK
    offset = _T1_ARP_CLOCKING

# Create taptempo for arp clock to be used for gate length
[switch]
    input1 = _CLOCK
    input2 = _T1_CLOCK
    input3 = _CLOCK_3_2
    input4 = _CLOCK_2_1
    input5 = _CLOCK_3_1
    input6 = _CLOCK_4_1
    input7 = _CLOCK_6_1
    input8 = _CLOCK_8_1
    output1 = _T1_ARP_TAPTEMPO
    offset = _T1_ARP_CLOCKING * _T1_ARP

# Virtual pot for base pitch of arpeggiator
[pot]
    select = _T1_ARP * 100 + _SELECTED_TRACK
    selectat = 101
    pot = P1.2
    outputscale = 3V
    output = _T1_ARP_PITCH

# Virtual pot for pitch range of arpeggiator
[pot]
    select = _T1_ARP * 100 + _SELECTED_TRACK
    selectat = 101
    pot = P1.1
    outputscale = 4V
    output = _T1_ARP_RANGE

# Arpeggiator for track 1
[arpeggio]
    select1 = _T1_SEL_1
    select3 = _T1_SEL_2
    select5 = _T1_SEL_3
    select7 = _T1_SEL_4
    select9 = _T1_SEL_5
    select11 = _T1_SEL_6
    select13 = _T1_SEL_7
    selectfill1 = 0
    selectfill2 = 0
    selectfill3 = 0
    selectfill4 = 0
    selectfill5 = 0
    root = _ROOT
    degree = _DEGREE
    clock = _T1_ARP_CLOCK * _RUNNING
    reset = _RESET
    tuningmode = _TUNINGMODE
    direction = _T1_ARP_UPDOWN
    pingpong = _T1_ARP_PINGPONG
    butterfly = _T1_ARP_BUTTERFLY
    octaves = _T1_ARP_OCTAVING
    drop = _T1_ARP_DROP
    pattern = _T1_ARP_PATTERN
    autoreset = _T1_ARP_AUTORESET
    pitch = _T1_ARP_PITCH
    range = _T1_ARP_RANGE
    output = _T1_ARP_OUTPUT

# Switch pitch output between sequencer and arpeggiator
[compare]
    input = _T1_ARP
    compare = 0
    ifequal = _T1_PITCH
    else = _T1_ARP_OUTPUT + _T1_ARP_OCTAVE
    output = _T1_SWITCHED_PITCH

# Mix all sources of pitch together
[mixer]
    input1 = _T1_OCTAVE * 1V - 2V
    input2 = _T1_SWITCHED_PITCH
    input3 = _GLOBAL_OCTAVE_SWITCH * 1V - 2V
    input4 = _GLOBAL_TRANSPOSITION * 0.00833333 - 1V
    output = _T1_FINAL_PITCH

# Slew limiter for glides
[slew]
    input = _T1_FINAL_PITCH
    exponential = O1
    slew = _T1_GLIDE * _T1_GLIDE_LENGTH

# Provide steady tap tempo for arpeggiator's gate length
[compare]
    input = _T1_ARP
    compare = 0
    ifequal = _T1_CLOCK
    else = _T1_ARP_TAPTEMPO
    output = _T1_GATEREF

# Compute gate length for arpeggiator output
[gatetool]
    taptempo = _T1_GATEREF
    inputgate = _T1_MUTED_GATE * _RUNNING
    outputgate = _T1_FINAL_GATE
    gatelength = _T1_GATELENGTH * 0.9 + 0.05

# Copy the gate signal to the CV jack
[copy]
    input = _T1_FINAL_GATE
    output = G1.1

# -------------------------------------------------
# Track 2
# -------------------------------------------------

# Provide trigger when the clear button is pressed (no Ctrl needed)
[button]
    select = _SELECTED_TRACK
    selectat = 2
    button = B2.4
    shortpress = _T2_CLEAR_BUTTON
    states = 1

# Clear button with control together clears the track
[copy]
    input = _T2_CLEAR_BUTTON * _CONTROL
    output = _T2_CLEAR

# Clear button without control removes all mode that alter the pattern length
[logic]
    input1 = _T2_CLEAR_BUTTON
    input2 = -1 * _CONTROL + 1
    and = _T2_PEACE

# Five blinks for the clear LED
[burst]
    trigger = _T2_CLEAR + _FACTORY_RESET
    hz = 5
    count = 5
    output = _T2_CLEAR_LED

# LED only blinks when track is selected
[select]
    select = _SELECTED_TRACK
    selectat = 2
    input = _T2_CLEAR_LED + 0.3
    output = L2.4

# Reset track immediately
[button]
    select = _SELECTED_TRACK
    selectat = 2
    button = B2.11 * _NO_CONTROL
    states = 1
    output = _T2_RESET

# Button for copying the current page of the current track
[button]
    select = _SELECTED_TRACK
    selectat = 2
    button = B2.24
    states = 1
    output = _T2_COPY_BUTTON

# This button cycles between the song froms A, AAAB and AABB
[button]
    select = _SELECTED_TRACK
    selectat = 2
    clear = _T2_CLEAR
    loadpreset = _T2_LOAD_PRESET
    savepreset = _T2_SAVE_PRESET
    button = B2.7
    led = L2.7
    output = _T2_FORM
    states = 3

# Disables scale and note selection if on
[button]
    select = _SELECTED_TRACK
    selectat = 2
    loadpreset = _T2_LOAD_PRESET
    savepreset = _T2_SAVE_PRESET
    button = B2.8
    led = L2.8
    output = _T2_12TONE

# Enable transposing the melody by the difference of the root note to C
[button]
    select = _SELECTED_TRACK
    selectat = 2
    loadpreset = _T2_LOAD_PRESET
    savepreset = _T2_SAVE_PRESET
    button = B2.5
    led = L2.5
    output = _T2_TBR_ON
    inverted = _T2_TBR_OFF

# Provide the amount of 'transpose by root' in a patch cable
[compare]
    input = _T2_TBR_ON
    compare = 1
    ifequal = _ROOT / 120
    output = _T2_TBR

# Hold to contiously step faders to random positions, hold with Ctrl to change buttons
[button]
    select = _SELECTED_TRACK
    selectat = 2
    button = B2.10
    states = 1
    onvalue = _LUCKY_CLOCK
    output = _T2_LUCKY

# Selects melodic inversion (swap low and high notes)
[button]
    select = _SELECTED_TRACK
    selectat = 2
    clear = _T2_CLEAR
    loadpreset = _T2_LOAD_PRESET
    savepreset = _T2_SAVE_PRESET
    button = B2.6
    led = L2.6
    output = _T2_INVERT

# Make LED brighter if the fader is in its default position
[compare]
    input = _T2_AUTORESET
    compare = 0
    ifequal = 1
    else = 0.3
    output = _T2_AUTORESET_LED

# Combine all triggers for clearing
[mixer]
    input1 = _T2_CLEAR + _T2_PEACE
    input2 = _T2_BPAR_1
    output = _T2_CLEAR_PAR

# Reset track after X clock ticks
[motorfader]
    select = _GLOBAL_FADER_SELECTION * _T2_ARP_OFF
    selectat = 12
    loadpreset = _T2_LOAD_PRESET
    savepreset = _T2_SAVE_PRESET
    notches = 17
    startvalue = 0
    ledcolor = 0.8
    ledvalue = _T2_AUTORESET_LED
    clear = _T2_CLEAR_PAR
    output = _T2_AUTORESET
    fader = 1
    button = _T2_BPAR_1

# Make LED brighter if the fader is in its default position
[compare]
    input = _T2_SHIFTSTEPS
    compare = 0
    ifequal = 1
    else = 0.3
    output = _T2_SHIFTSTEPS_LED

# Combine all triggers for clearing
[mixer]
    input1 = _T2_CLEAR + _T2_PEACE
    input2 = _T2_BPSS_1
    output = _T2_CLEAR_PSS

# Shift the steps of the sequence by this number
[motorfader]
    select = _GLOBAL_FADER_SELECTION * _T2_ARP_OFF
    selectat = 12
    loadpreset = _T2_LOAD_PRESET
    savepreset = _T2_SAVE_PRESET
    notches = 17
    startvalue = 0
    ledcolor = 0.6
    ledvalue = _T2_SHIFTSTEPS_LED
    clear = _T2_CLEAR_PSS
    output = _T2_SHIFTSTEPS
    fader = 2
    button = _T2_BPSS_1

# Make LED brighter if the fader is in its default position
[compare]
    input = _T2_OCTAVE
    compare = 2
    ifequal = 1
    else = 0.3
    output = _T2_OCTAVE_LED

# Combine all triggers for clearing
[mixer]
    input1 = _T2_CLEAR
    input2 = _T2_BPOCT_1 + _T2_BPOCT_2
    output = _T2_CLEAR_POCT

# Octave switch: -2, -1, 0, +1, +2
[motorfader]
    select = _GLOBAL_FADER_SELECTION * _T2_ARP_OFF
    selectat = 12
    loadpreset = _T2_LOAD_PRESET
    savepreset = _T2_SAVE_PRESET
    notches = 5
    startvalue = 2
    ledcolor = 0.2
    ledvalue = _T2_OCTAVE_LED
    clear = _T2_CLEAR_POCT
    sharewithnext = 1
    fader = 3
    button = _T2_BPOCT_1

# Octave switch: -2, -1, 0, +1, +2
[motorfader]
    loadpreset = _T2_LOAD_PRESET
    savepreset = _T2_SAVE_PRESET
    notches = 5
    startvalue = 2
    ledcolor = 0.2
    ledvalue = _T2_OCTAVE_LED
    clear = _T2_CLEAR_POCT
    select = _GLOBAL_FADER_SELECTION
    selectat = 98
    fader = 3
    button = _T2_BPOCT_2
    output = _T2_OCTAVE

# Make LED brighter if the fader is in its default position
[compare]
    input = _T2_NOTESHIFT
    compare = 7
    ifequal = 1
    else = 0.3
    output = _T2_NOTESHIFT_LED

# Combine all triggers for clearing
[mixer]
    input1 = _T2_CLEAR
    input2 = _T2_BPDT_1 + _T2_BPDT_2
    output = _T2_CLEAR_PDT

# Diatonic transposition
[motorfader]
    select = _GLOBAL_FADER_SELECTION * _T2_ARP_OFF
    selectat = 12
    loadpreset = _T2_LOAD_PRESET
    savepreset = _T2_SAVE_PRESET
    notches = 15
    startvalue = 7
    ledcolor = 0.4
    ledvalue = _T2_NOTESHIFT_LED
    clear = _T2_CLEAR_PDT
    sharewithnext = 1
    fader = 4
    button = _T2_BPDT_1

# Diatonic transposition
[motorfader]
    loadpreset = _T2_LOAD_PRESET
    savepreset = _T2_SAVE_PRESET
    notches = 15
    startvalue = 7
    ledcolor = 0.4
    ledvalue = _T2_NOTESHIFT_LED
    clear = _T2_CLEAR_PDT
    select = _GLOBAL_FADER_SELECTION
    selectat = 98
    fader = 4
    button = _T2_BPDT_2
    output = _T2_NOTESHIFT

# Prevent diatonic transposition from happending between clock ticks
[sample]
    input = _T2_NOTESHIFT
    sample = _CLOCK
    output = _T2_NOTESHIFT_CLOCKED

# Make LED brighter if the fader is in its default position
[compare]
    input = _T2_ACTIVITY
    compare = 0
    ifequal = 1
    else = 0.3
    output = _T2_ACTIVITY_LED

# Combine all triggers for clearing
[mixer]
    input1 = _T2_CLEAR
    input2 = _T2_BPACT_1
    output = _T2_CLEAR_PACT

# Minimum activity a step needs to have to be played
[motorfader]
    select = _GLOBAL_FADER_SELECTION * _T2_ARP_OFF
    selectat = 12
    loadpreset = _T2_LOAD_PRESET
    savepreset = _T2_SAVE_PRESET
    notches = 8
    startvalue = 0
    ledcolor = 0.55
    ledvalue = _T2_ACTIVITY_LED
    clear = _T2_CLEAR_PACT
    output = _T2_ACTIVITY
    fader = 5
    button = _T2_BPACT_1

# Make LED brighter if the fader is in its default position
[compare]
    input = _T2_PATTERN_FADER
    compare = 0
    ifequal = 1
    else = 0.3
    output = _T2_PATTERN_FADER_LED

# Combine all triggers for clearing
[mixer]
    input1 = _T2_CLEAR + _T2_PEACE
    input2 = _T2_BPPAT_1
    output = _T2_CLEAR_PPAT

# Movement pattern: normal, reverse, ping pong, >><, 2><, etc.
[motorfader]
    select = _GLOBAL_FADER_SELECTION * _T2_ARP_OFF
    selectat = 12
    loadpreset = _T2_LOAD_PRESET
    savepreset = _T2_SAVE_PRESET
    notches = 10
    startvalue = 0
    ledcolor = 0.3
    ledvalue = _T2_PATTERN_FADER_LED
    clear = _T2_CLEAR_PPAT
    output = _T2_PATTERN_FADER
    fader = 6
    button = _T2_BPPAT_1

# Set the direction to reverse on fader position 1
[compare]
    input = _T2_PATTERN_FADER
    compare = 1
    ifequal = 1
    output = _T2_DIRECTION

# Enable ping pong on fader position 2
[compare]
    input = _T2_PATTERN_FADER
    compare = 2
    ifequal = 1
    output = _T2_PINGPONG

# Make LED brighter if the fader is in its default position
[compare]
    input = _T2_CD_EVEN
    compare = 3
    ifequal = 1
    else = 0.3
    output = _T2_CD_EVEN_LED

# Combine all triggers for clearing
[mixer]
    input1 = _T2_CLEAR
    input2 = _T2_BPCDEVEN_1
    output = _T2_CLEAR_PCDEVEN

# Clock division for even divides/multipliers
[motorfader]
    select = _GLOBAL_FADER_SELECTION * _T2_ARP_OFF
    selectat = 12
    loadpreset = _T2_LOAD_PRESET
    savepreset = _T2_SAVE_PRESET
    notches = 7
    startvalue = 3
    ledcolor = 1.1
    ledvalue = _T2_CD_EVEN_LED
    clear = _T2_CLEAR_PCDEVEN
    output = _T2_CD_EVEN
    fader = 7
    button = _T2_BPCDEVEN_1

# Select the multiplier based on the fader position
[switch]
    input1 = 1
    input2 = 1
    input3 = 1
    input4 = 1
    input5 = 2
    input6 = 4
    input7 = 8
    offset = _T2_CD_EVEN
    output1 = _T2_CD_EVEN_MULT

# Select the division based on the fader position
[switch]
    input1 = 8
    input2 = 4
    input3 = 2
    input4 = 1
    input5 = 1
    input6 = 1
    input7 = 1
    offset = _T2_CD_EVEN
    output1 = _T2_CD_EVEN_DIV

# Make LED brighter if the fader is in its default position
[compare]
    input = _T2_CD_ODD
    compare = 3
    ifequal = 1
    else = 0.3
    output = _T2_CD_ODD_LED

# Combine all triggers for clearing
[mixer]
    input1 = _T2_CLEAR
    input2 = _T2_BPCDODD_1
    output = _T2_CLEAR_PCDODD

# Clock division for odd divides/multipliers
[motorfader]
    select = _GLOBAL_FADER_SELECTION * _T2_ARP_OFF
    selectat = 12
    loadpreset = _T2_LOAD_PRESET
    savepreset = _T2_SAVE_PRESET
    notches = 7
    startvalue = 3
    ledcolor = 1.2
    ledvalue = _T2_CD_ODD_LED
    clear = _T2_CLEAR_PCDODD
    output = _T2_CD_ODD
    fader = 8
    button = _T2_BPCDODD_1

# Select the multiplier based on the fader position
[switch]
    input1 = 1
    input2 = 1
    input3 = 1
    input4 = 1
    input5 = 3
    input6 = 5
    input7 = 7
    offset = _T2_CD_ODD
    output1 = _T2_CD_ODD_MULT

# Select the division based on the fader position
[switch]
    input1 = 7
    input2 = 5
    input3 = 3
    input4 = 1
    input5 = 1
    input6 = 1
    input7 = 1
    offset = _T2_CD_ODD
    output1 = _T2_CD_ODD_DIV

# Switch on/off arpeggio mode for track 2
[button]
    select = _SELECTED_TRACK
    selectat = 2
    button = B2.9
    led = L2.9
    output = _T2_ARP
    negated = _T2_ARP_OFF

# Auto reset arpeggiator after N number of steps
[motorfader]
    select = _GLOBAL_FADER_SELECTION * _T2_ARP
    selectat = 12
    loadpreset = _T2_LOAD_PRESET
    savepreset = _T2_SAVE_PRESET
    fader = 1
    notches = 17
    ledcolor = 0.8
    ledvalue = 1
    output = _T2_ARP_AUTORESET

# Enable ping pong mode
[motorfader]
    select = _GLOBAL_FADER_SELECTION * _T2_ARP
    selectat = 12
    loadpreset = _T2_LOAD_PRESET
    savepreset = _T2_SAVE_PRESET
    fader = 2
    notches = 3
    ledcolor = 0.85
    ledvalue = 1
    output = _T2_ARP_DIRECTION

# Provide gate for reversed direction of arpeggio
[compare]
    input = _T2_ARP_DIRECTION
    compare = 2
    ifequal = 1
    else = 0
    output = _T2_ARP_UPDOWN

# Provide gate for ping pong of arpeggio
[compare]
    input = _T2_ARP_DIRECTION
    compare = 1
    ifequal = 1
    else = 0
    output = _T2_ARP_PINGPONG

# Seperate octave switch for the arpeggio
[motorfader]
    select = _GLOBAL_FADER_SELECTION * _T2_ARP
    selectat = 12
    loadpreset = _T2_LOAD_PRESET
    savepreset = _T2_SAVE_PRESET
    fader = 3
    notches = 5
    ledcolor = 0.2
    startvalue = 2
    ledvalue = 1
    output = _T2_ARP_OCTAVE_SWITCH

# Enable butterfly mode
[motorfader]
    select = _GLOBAL_FADER_SELECTION * _T2_ARP
    selectat = 12
    loadpreset = _T2_LOAD_PRESET
    savepreset = _T2_SAVE_PRESET
    fader = 4
    notches = 2
    ledcolor = 1.15
    ledvalue = 1
    output = _T2_ARP_BUTTERFLY

# Select one of three octaving patterns
[motorfader]
    select = _GLOBAL_FADER_SELECTION * _T2_ARP
    selectat = 12
    loadpreset = _T2_LOAD_PRESET
    savepreset = _T2_SAVE_PRESET
    fader = 5
    notches = 3
    ledcolor = 0.4
    ledvalue = 1
    output = _T2_ARP_OCTAVING

# Compute octave offset pitches from fader setting
[copy]
    input = _T2_ARP_OCTAVE_SWITCH * 1V - 2V
    output = _T2_ARP_OCTAVE

# Select the arpeggio pattern
[motorfader]
    select = _GLOBAL_FADER_SELECTION * _T2_ARP
    selectat = 12
    loadpreset = _T2_LOAD_PRESET
    savepreset = _T2_SAVE_PRESET
    fader = 6
    notches = 7
    ledcolor = 0.3
    ledvalue = 1
    output = _T2_ARP_PATTERN

# Select one of four different drop patterns
[motorfader]
    select = _GLOBAL_FADER_SELECTION * _T2_ARP
    selectat = 12
    loadpreset = _T2_LOAD_PRESET
    savepreset = _T2_SAVE_PRESET
    fader = 7
    notches = 4
    ledcolor = 0.5
    ledvalue = 1
    output = _T2_ARP_DROP

# Select one of several clocking patterns
[motorfader]
    select = _GLOBAL_FADER_SELECTION * _T2_ARP
    selectat = 12
    loadpreset = _T2_LOAD_PRESET
    savepreset = _T2_SAVE_PRESET
    fader = 8
    notches = 8
    ledcolor = 1.1
    ledvalue = 1
    output = _T2_ARP_CLOCKING

# Button for loading preset 1 on track 2
[button]
    select = _SELECTED_TRACK
    selectat = 2
    button = B2.1 * _CONTROL
    states = 1
    onvalue = 1
    output = _T2_P1_LOAD

# Button for saving preset 1 on track 2
[button]
    select = _SELECTED_TRACK
    selectat = 2
    button = B2.1
    states = 1
    onvalue = 1
    longpress = _T2_P1_SAVE

# Five blinks for the LED of the preset button on saving
[burst]
    trigger = _T2_P1_SAVE
    hz = 5
    count = 5
    output = _T2_P1_SAVE_LED

# Write the save LED animation to the LED if the track is still selected.
[select]
    select = _SELECTED_TRACK
    selectat = 2
    input = _T2_P1_SAVE_LED + 0.3
    output = L2.1

# Button for loading preset 2 on track 2
[button]
    select = _SELECTED_TRACK
    selectat = 2
    button = B2.2 * _CONTROL
    states = 1
    onvalue = 2
    output = _T2_P2_LOAD

# Button for saving preset 2 on track 2
[button]
    select = _SELECTED_TRACK
    selectat = 2
    button = B2.2
    states = 1
    onvalue = 2
    longpress = _T2_P2_SAVE

# Five blinks for the LED of the preset button on saving
[burst]
    trigger = _T2_P2_SAVE
    hz = 5
    count = 5
    output = _T2_P2_SAVE_LED

# Write the save LED animation to the LED if the track is still selected.
[select]
    select = _SELECTED_TRACK
    selectat = 2
    input = _T2_P2_SAVE_LED + 0.3
    output = L2.2

# Button for loading preset 3 on track 2
[button]
    select = _SELECTED_TRACK
    selectat = 2
    button = B2.3 * _CONTROL
    states = 1
    onvalue = 3
    output = _T2_P3_LOAD

# Button for saving preset 3 on track 2
[button]
    select = _SELECTED_TRACK
    selectat = 2
    button = B2.3
    states = 1
    onvalue = 3
    longpress = _T2_P3_SAVE

# Five blinks for the LED of the preset button on saving
[burst]
    trigger = _T2_P3_SAVE
    hz = 5
    count = 5
    output = _T2_P3_SAVE_LED

# Write the save LED animation to the LED if the track is still selected.
[select]
    select = _SELECTED_TRACK
    selectat = 2
    input = _T2_P3_SAVE_LED + 0.3
    output = L2.3

# Create a trigger with the preset as value when any of the presets is loaded
[mixer]
    input1 = _T2_P1_LOAD
    input2 = _T2_P2_LOAD
    input3 = _T2_P3_LOAD
    output = _T2_LOAD_PRESET

# Create a trigger with the preset as value when any of the presets is saved
[mixer]
    input1 = _T2_P1_SAVE * 1
    input2 = _T2_P2_SAVE * 2
    input3 = _T2_P3_SAVE * 3
    output = _T2_SAVE_PRESET

# Select interval note 1 from the current scale
[button]
    select = _SELECTED_TRACK
    selectat = 2
    button = B1.1
    led = L1.1
    startvalue = 1
    output = _T2_SEL_1

# Select interval note 2 from the current scale
[button]
    select = _SELECTED_TRACK
    selectat = 2
    button = B1.2
    led = L1.2
    startvalue = 1
    output = _T2_SEL_2

# Select interval note 3 from the current scale
[button]
    select = _SELECTED_TRACK
    selectat = 2
    button = B1.3
    led = L1.3
    startvalue = 1
    output = _T2_SEL_3

# Select interval note 4 from the current scale
[button]
    select = _SELECTED_TRACK
    selectat = 2
    button = B1.4
    led = L1.4
    startvalue = 1
    output = _T2_SEL_4

# Select interval note 5 from the current scale
[button]
    select = _SELECTED_TRACK
    selectat = 2
    button = B1.5
    led = L1.5
    startvalue = 1
    output = _T2_SEL_5

# Select interval note 6 from the current scale
[button]
    select = _SELECTED_TRACK
    selectat = 2
    button = B1.6
    led = L1.6
    startvalue = 1
    output = _T2_SEL_6

# Select interval note 7 from the current scale
[button]
    select = _SELECTED_TRACK
    selectat = 2
    button = B1.7
    led = L1.7
    startvalue = 1
    output = _T2_SEL_7

# Clock divider for track 2
[clocktool]
    clock = _CLOCK
    reset = _RESET
    divide = _T2_CD_EVEN_DIV * _T2_CD_ODD_DIV
    multiply = _T2_CD_EVEN_MULT * _T2_CD_ODD_MULT
    output = _T2_CLOCK

# Mix global and per-track diatonic transposition
[mixer]
    input1 = -1 * 7 + _T2_NOTESHIFT_CLOCKED
    input2 = -1 * 7 + _GLOBAL_NOTESHIFT_CLOCKED
    output = _T2_NOTESHIFT_SUM

# collect different sources of resetting the start/end point
[logic]
    input1 = _T2_CLEAR + _T2_PEACE
    input2 = _RANGE * _CONTROL
    or = _T2_CLEARSTARTEND

# Note shift for each step repetition
[pot]
    select = _T2_ARP * 100 + _SELECTED_TRACK
    selectat = 2
    pot = P1.1
    discrete = 15
    startvalue = 7
    output = _T2_REPEATSHIFT

# Note shift for each ratchet
[pot]
    select = _T2_ARP * 100 + _SELECTED_TRACK
    selectat = 2
    pot = P1.2
    discrete = 15
    startvalue = 7
    output = _T2_RATCHETSHIFT

# Sequencer for pitch and gate of track 2
[motoquencer]
    select = _GLOBAL_FADER_SELECTION
    selectat = 2
    page = _PAGE
    copy = _T2_COPY_BUTTON * _NO_CONTROL
    paste = _T2_COPY_BUTTON * _CONTROL
    loadpreset = _T2_LOAD_PRESET
    savepreset = _T2_SAVE_PRESET
    clear = _T2_CLEAR
    select1 = _T2_SEL_1 + _T2_12TONE
    select3 = _T2_SEL_2 + _T2_12TONE
    select5 = _T2_SEL_3 + _T2_12TONE
    select7 = _T2_SEL_4 + _T2_12TONE
    select9 = _T2_SEL_5 + _T2_12TONE
    select11 = _T2_SEL_6 + _T2_12TONE
    select13 = _T2_SEL_7 + _T2_12TONE
    selectfill1 = _T2_12TONE
    selectfill2 = _T2_12TONE
    selectfill3 = _T2_12TONE
    selectfill4 = _T2_12TONE
    selectfill5 = _T2_12TONE
    degree = _DEGREE
    clock = _T2_CLOCK * _RUNNING
    taptempo = _T2_CLOCK
    reset = _RESET + _T2_RESET
    cv = _T2_PITCH
    cvrange = _T2_CVRANGE * 1V + 1V
    defaultgate = 0
    gate = _T2_SEQUENCER_GATE
    numfaders = 8
    numsteps = 16
    linktonext = 1
    autoreset = _T2_AUTORESET
    shiftsteps = _T2_SHIFTSTEPS
    pattern = -1 * 2 + _T2_PATTERN_FADER
    direction = _T2_DIRECTION
    pingpong = _T2_PINGPONG
    selectnoteshift = _T2_NOTESHIFT_SUM
    tuningmode = _TUNINGMODE
    tuningpitch = _GLOBAL_OCTAVE_SWITCH * 1V
    accumulatorrange = _T2_ACCU_RANGE
    root = _ROOT * _T2_TBR_OFF
    transpose = _T2_TBR
    luckybuttons = _T2_LUCKY * _CONTROL
    luckyfaders = _T2_LUCKY * _NO_CONTROL
    luckychance = 1 / 8
    luckyamount = 0.5 * _NO_CONTROL + 0.5
    luckyscope = 3
    fadermode = _FADERMODE
    mute = -1 * _RUNNING + 1
    form = _T2_FORM
    invert = _T2_INVERT
    buttonmode = _BUTTONMODE
    composemode = _COMPOSEMODE
    clearskips = _T2_CLEAR + _T2_PEACE
    clearrepeats = _T2_CLEAR + _T2_PEACE
    clearstartend = _T2_CLEARSTARTEND
    repeatshift = -1 * 7 + _T2_REPEATSHIFT
    ratchetshift = -1 * 7 + _T2_RATCHETSHIFT

# Sequencer for velocity
[motoquencer]
    select = _GLOBAL_FADER_SELECTION
    selectat = 2
    page = _PAGE
    copy = _T2_COPY_BUTTON * _NO_CONTROL
    paste = _T2_COPY_BUTTON * _CONTROL
    clear = _T2_CLEAR
    loadpreset = _T2_LOAD_PRESET
    savepreset = _T2_SAVE_PRESET
    cv = _T2_VELOCITY
    cvrange = 1
    defaultcv = 0.5
    quantize = 0
    linktonext = 1

# Copy velocity to CV output
[copy]
    input = _T2_VELOCITY
    output = O4

# Sequencer for gate length and glide
[motoquencer]
    select = _GLOBAL_FADER_SELECTION
    selectat = 2
    page = _PAGE
    copy = _T2_COPY_BUTTON * _NO_CONTROL
    paste = _T2_COPY_BUTTON * _CONTROL
    clear = _T2_CLEAR
    loadpreset = _T2_LOAD_PRESET
    savepreset = _T2_SAVE_PRESET
    cv = _T2_GATELENGTH
    cvrange = 1
    defaultcv = 0.5
    defaultgate = 0
    quantize = 0
    buttoncolor = 0.5
    linktonext = 1
    gate = _T2_GLIDE

# Sequencer for activity level per step
[motoquencer]
    select = _GLOBAL_FADER_SELECTION
    selectat = 2
    clear = _T2_CLEAR
    loadpreset = _T2_LOAD_PRESET
    savepreset = _T2_SAVE_PRESET
    cv = _T2_STEP_ACTIVITY
    quantize = 0
    defaultcv = 7
    cvnotches = 8

# Silence the gate if the step does not reach the required activity
[compare]
    input = _T2_STEP_ACTIVITY
    compare = _T2_ACTIVITY
    ifless = 0
    else = _T2_SEQUENCER_GATE
    output = _T2_ACT_GATE

# Switch gate to arpeggiator if active
[compare]
    input = _T2_ARP
    compare = 0
    ifequal = _T2_ACT_GATE
    else = _T2_ARP_CLOCK
    output = _T2_SWITCHED_GATE

# Logic for make the gate silent if the track is muted
[logic]
    input1 = -1 * _T2_MUTED + 1
    input2 = _T2_SWITCHED_GATE
    and = _T2_MUTED_GATE

# Select clock for arpeggiator of track 2
[switch]
    input1 = _CLOCK
    input2 = _T2_SEQUENCER_GATE
    input3 = _CLOCK_3_2
    input4 = _CLOCK_2_1
    input5 = _CLOCK_3_1
    input6 = _CLOCK_4_1
    input7 = _CLOCK_6_1
    input8 = _CLOCK_8_1
    output1 = _T2_ARP_CLOCK
    offset = _T2_ARP_CLOCKING

# Create taptempo for arp clock to be used for gate length
[switch]
    input1 = _CLOCK
    input2 = _T2_CLOCK
    input3 = _CLOCK_3_2
    input4 = _CLOCK_2_1
    input5 = _CLOCK_3_1
    input6 = _CLOCK_4_1
    input7 = _CLOCK_6_1
    input8 = _CLOCK_8_1
    output1 = _T2_ARP_TAPTEMPO
    offset = _T2_ARP_CLOCKING * _T2_ARP

# Virtual pot for base pitch of arpeggiator
[pot]
    select = _T2_ARP * 100 + _SELECTED_TRACK
    selectat = 102
    pot = P1.2
    outputscale = 3V
    output = _T2_ARP_PITCH

# Virtual pot for pitch range of arpeggiator
[pot]
    select = _T2_ARP * 100 + _SELECTED_TRACK
    selectat = 102
    pot = P1.1
    outputscale = 4V
    output = _T2_ARP_RANGE

# Arpeggiator for track 2
[arpeggio]
    select1 = _T2_SEL_1
    select3 = _T2_SEL_2
    select5 = _T2_SEL_3
    select7 = _T2_SEL_4
    select9 = _T2_SEL_5
    select11 = _T2_SEL_6
    select13 = _T2_SEL_7
    selectfill1 = 0
    selectfill2 = 0
    selectfill3 = 0
    selectfill4 = 0
    selectfill5 = 0
    root = _ROOT
    degree = _DEGREE
    clock = _T2_ARP_CLOCK * _RUNNING
    reset = _RESET
    tuningmode = _TUNINGMODE
    direction = _T2_ARP_UPDOWN
    pingpong = _T2_ARP_PINGPONG
    butterfly = _T2_ARP_BUTTERFLY
    octaves = _T2_ARP_OCTAVING
    drop = _T2_ARP_DROP
    pattern = _T2_ARP_PATTERN
    autoreset = _T2_ARP_AUTORESET
    pitch = _T2_ARP_PITCH
    range = _T2_ARP_RANGE
    output = _T2_ARP_OUTPUT

# Switch pitch output between sequencer and arpeggiator
[compare]
    input = _T2_ARP
    compare = 0
    ifequal = _T2_PITCH
    else = _T2_ARP_OUTPUT + _T2_ARP_OCTAVE
    output = _T2_SWITCHED_PITCH

# Mix all sources of pitch together
[mixer]
    input1 = _T2_OCTAVE * 1V - 2V
    input2 = _T2_SWITCHED_PITCH
    input3 = _GLOBAL_OCTAVE_SWITCH * 1V - 2V
    input4 = _GLOBAL_TRANSPOSITION * 0.00833333 - 1V
    output = _T2_FINAL_PITCH

# Slew limiter for glides
[slew]
    input = _T2_FINAL_PITCH
    exponential = O3
    slew = _T2_GLIDE * _T2_GLIDE_LENGTH

# Provide steady tap tempo for arpeggiator's gate length
[compare]
    input = _T2_ARP
    compare = 0
    ifequal = _T2_CLOCK
    else = _T2_ARP_TAPTEMPO
    output = _T2_GATEREF

# Compute gate length for arpeggiator output
[gatetool]
    taptempo = _T2_GATEREF
    inputgate = _T2_MUTED_GATE * _RUNNING
    outputgate = _T2_FINAL_GATE
    gatelength = _T2_GATELENGTH * 0.9 + 0.05

# Copy the gate signal to the CV jack
[copy]
    input = _T2_FINAL_GATE
    output = G1.2

# -------------------------------------------------
# Track 3
# -------------------------------------------------

# Provide trigger when the clear button is pressed (no Ctrl needed)
[button]
    select = _SELECTED_TRACK
    selectat = 3
    button = B2.4
    shortpress = _T3_CLEAR_BUTTON
    states = 1

# Clear button with control together clears the track
[copy]
    input = _T3_CLEAR_BUTTON * _CONTROL
    output = _T3_CLEAR

# Clear button without control removes all mode that alter the pattern length
[logic]
    input1 = _T3_CLEAR_BUTTON
    input2 = -1 * _CONTROL + 1
    and = _T3_PEACE

# Five blinks for the clear LED
[burst]
    trigger = _T3_CLEAR + _FACTORY_RESET
    hz = 5
    count = 5
    output = _T3_CLEAR_LED

# LED only blinks when track is selected
[select]
    select = _SELECTED_TRACK
    selectat = 3
    input = _T3_CLEAR_LED + 0.3
    output = L2.4

# Reset track immediately
[button]
    select = _SELECTED_TRACK
    selectat = 3
    button = B2.11 * _NO_CONTROL
    states = 1
    output = _T3_RESET

# Button for copying the current page of the current track
[button]
    select = _SELECTED_TRACK
    selectat = 3
    button = B2.24
    states = 1
    output = _T3_COPY_BUTTON

# This button cycles between the song froms A, AAAB and AABB
[button]
    select = _SELECTED_TRACK
    selectat = 3
    clear = _T3_CLEAR
    loadpreset = _T3_LOAD_PRESET
    savepreset = _T3_SAVE_PRESET
    button = B2.7
    led = L2.7
    output = _T3_FORM
    states = 3

# Disables scale and note selection if on
[button]
    select = _SELECTED_TRACK
    selectat = 3
    loadpreset = _T3_LOAD_PRESET
    savepreset = _T3_SAVE_PRESET
    button = B2.8
    led = L2.8
    output = _T3_12TONE

# Enable transposing the melody by the difference of the root note to C
[button]
    select = _SELECTED_TRACK
    selectat = 3
    loadpreset = _T3_LOAD_PRESET
    savepreset = _T3_SAVE_PRESET
    button = B2.5
    led = L2.5
    output = _T3_TBR_ON
    inverted = _T3_TBR_OFF

# Provide the amount of 'transpose by root' in a patch cable
[compare]
    input = _T3_TBR_ON
    compare = 1
    ifequal = _ROOT / 120
    output = _T3_TBR

# Hold to contiously step faders to random positions, hold with Ctrl to change buttons
[button]
    select = _SELECTED_TRACK
    selectat = 3
    button = B2.10
    states = 1
    onvalue = _LUCKY_CLOCK
    output = _T3_LUCKY

# Selects melodic inversion (swap low and high notes)
[button]
    select = _SELECTED_TRACK
    selectat = 3
    clear = _T3_CLEAR
    loadpreset = _T3_LOAD_PRESET
    savepreset = _T3_SAVE_PRESET
    button = B2.6
    led = L2.6
    output = _T3_INVERT

# Make LED brighter if the fader is in its default position
[compare]
    input = _T3_AUTORESET
    compare = 0
    ifequal = 1
    else = 0.3
    output = _T3_AUTORESET_LED

# Combine all triggers for clearing
[mixer]
    input1 = _T3_CLEAR + _T3_PEACE
    input2 = _T3_BPAR_1
    output = _T3_CLEAR_PAR

# Reset track after X clock ticks
[motorfader]
    select = _GLOBAL_FADER_SELECTION * _T3_ARP_OFF
    selectat = 13
    loadpreset = _T3_LOAD_PRESET
    savepreset = _T3_SAVE_PRESET
    notches = 17
    startvalue = 0
    ledcolor = 0.8
    ledvalue = _T3_AUTORESET_LED
    clear = _T3_CLEAR_PAR
    output = _T3_AUTORESET
    fader = 1
    button = _T3_BPAR_1

# Make LED brighter if the fader is in its default position
[compare]
    input = _T3_SHIFTSTEPS
    compare = 0
    ifequal = 1
    else = 0.3
    output = _T3_SHIFTSTEPS_LED

# Combine all triggers for clearing
[mixer]
    input1 = _T3_CLEAR + _T3_PEACE
    input2 = _T3_BPSS_1
    output = _T3_CLEAR_PSS

# Shift the steps of the sequence by this number
[motorfader]
    select = _GLOBAL_FADER_SELECTION * _T3_ARP_OFF
    selectat = 13
    loadpreset = _T3_LOAD_PRESET
    savepreset = _T3_SAVE_PRESET
    notches = 17
    startvalue = 0
    ledcolor = 0.6
    ledvalue = _T3_SHIFTSTEPS_LED
    clear = _T3_CLEAR_PSS
    output = _T3_SHIFTSTEPS
    fader = 2
    button = _T3_BPSS_1

# Make LED brighter if the fader is in its default position
[compare]
    input = _T3_OCTAVE
    compare = 2
    ifequal = 1
    else = 0.3
    output = _T3_OCTAVE_LED

# Combine all triggers for clearing
[mixer]
    input1 = _T3_CLEAR
    input2 = _T3_BPOCT_1 + _T3_BPOCT_2
    output = _T3_CLEAR_POCT

# Octave switch: -2, -1, 0, +1, +2
[motorfader]
    select = _GLOBAL_FADER_SELECTION * _T3_ARP_OFF
    selectat = 13
    loadpreset = _T3_LOAD_PRESET
    savepreset = _T3_SAVE_PRESET
    notches = 5
    startvalue = 2
    ledcolor = 0.2
    ledvalue = _T3_OCTAVE_LED
    clear = _T3_CLEAR_POCT
    sharewithnext = 1
    fader = 3
    button = _T3_BPOCT_1

# Octave switch: -2, -1, 0, +1, +2
[motorfader]
    loadpreset = _T3_LOAD_PRESET
    savepreset = _T3_SAVE_PRESET
    notches = 5
    startvalue = 2
    ledcolor = 0.2
    ledvalue = _T3_OCTAVE_LED
    clear = _T3_CLEAR_POCT
    select = _GLOBAL_FADER_SELECTION
    selectat = 98
    fader = 5
    button = _T3_BPOCT_2
    output = _T3_OCTAVE

# Make LED brighter if the fader is in its default position
[compare]
    input = _T3_NOTESHIFT
    compare = 7
    ifequal = 1
    else = 0.3
    output = _T3_NOTESHIFT_LED

# Combine all triggers for clearing
[mixer]
    input1 = _T3_CLEAR
    input2 = _T3_BPDT_1 + _T3_BPDT_2
    output = _T3_CLEAR_PDT

# Diatonic transposition
[motorfader]
    select = _GLOBAL_FADER_SELECTION * _T3_ARP_OFF
    selectat = 13
    loadpreset = _T3_LOAD_PRESET
    savepreset = _T3_SAVE_PRESET
    notches = 15
    startvalue = 7
    ledcolor = 0.4
    ledvalue = _T3_NOTESHIFT_LED
    clear = _T3_CLEAR_PDT
    sharewithnext = 1
    fader = 4
    button = _T3_BPDT_1

# Diatonic transposition
[motorfader]
    loadpreset = _T3_LOAD_PRESET
    savepreset = _T3_SAVE_PRESET
    notches = 15
    startvalue = 7
    ledcolor = 0.4
    ledvalue = _T3_NOTESHIFT_LED
    clear = _T3_CLEAR_PDT
    select = _GLOBAL_FADER_SELECTION
    selectat = 98
    fader = 6
    button = _T3_BPDT_2
    output = _T3_NOTESHIFT

# Prevent diatonic transposition from happending between clock ticks
[sample]
    input = _T3_NOTESHIFT
    sample = _CLOCK
    output = _T3_NOTESHIFT_CLOCKED

# Make LED brighter if the fader is in its default position
[compare]
    input = _T3_ACTIVITY
    compare = 0
    ifequal = 1
    else = 0.3
    output = _T3_ACTIVITY_LED

# Combine all triggers for clearing
[mixer]
    input1 = _T3_CLEAR
    input2 = _T3_BPACT_1
    output = _T3_CLEAR_PACT

# Minimum activity a step needs to have to be played
[motorfader]
    select = _GLOBAL_FADER_SELECTION * _T3_ARP_OFF
    selectat = 13
    loadpreset = _T3_LOAD_PRESET
    savepreset = _T3_SAVE_PRESET
    notches = 8
    startvalue = 0
    ledcolor = 0.55
    ledvalue = _T3_ACTIVITY_LED
    clear = _T3_CLEAR_PACT
    output = _T3_ACTIVITY
    fader = 5
    button = _T3_BPACT_1

# Make LED brighter if the fader is in its default position
[compare]
    input = _T3_PATTERN_FADER
    compare = 0
    ifequal = 1
    else = 0.3
    output = _T3_PATTERN_FADER_LED

# Combine all triggers for clearing
[mixer]
    input1 = _T3_CLEAR + _T3_PEACE
    input2 = _T3_BPPAT_1
    output = _T3_CLEAR_PPAT

# Movement pattern: normal, reverse, ping pong, >><, 2><, etc.
[motorfader]
    select = _GLOBAL_FADER_SELECTION * _T3_ARP_OFF
    selectat = 13
    loadpreset = _T3_LOAD_PRESET
    savepreset = _T3_SAVE_PRESET
    notches = 10
    startvalue = 0
    ledcolor = 0.3
    ledvalue = _T3_PATTERN_FADER_LED
    clear = _T3_CLEAR_PPAT
    output = _T3_PATTERN_FADER
    fader = 6
    button = _T3_BPPAT_1

# Set the direction to reverse on fader position 1
[compare]
    input = _T3_PATTERN_FADER
    compare = 1
    ifequal = 1
    output = _T3_DIRECTION

# Enable ping pong on fader position 2
[compare]
    input = _T3_PATTERN_FADER
    compare = 2
    ifequal = 1
    output = _T3_PINGPONG

# Make LED brighter if the fader is in its default position
[compare]
    input = _T3_CD_EVEN
    compare = 3
    ifequal = 1
    else = 0.3
    output = _T3_CD_EVEN_LED

# Combine all triggers for clearing
[mixer]
    input1 = _T3_CLEAR
    input2 = _T3_BPCDEVEN_1
    output = _T3_CLEAR_PCDEVEN

# Clock division for even divides/multipliers
[motorfader]
    select = _GLOBAL_FADER_SELECTION * _T3_ARP_OFF
    selectat = 13
    loadpreset = _T3_LOAD_PRESET
    savepreset = _T3_SAVE_PRESET
    notches = 7
    startvalue = 3
    ledcolor = 1.1
    ledvalue = _T3_CD_EVEN_LED
    clear = _T3_CLEAR_PCDEVEN
    output = _T3_CD_EVEN
    fader = 7
    button = _T3_BPCDEVEN_1

# Select the multiplier based on the fader position
[switch]
    input1 = 1
    input2 = 1
    input3 = 1
    input4 = 1
    input5 = 2
    input6 = 4
    input7 = 8
    offset = _T3_CD_EVEN
    output1 = _T3_CD_EVEN_MULT

# Select the division based on the fader position
[switch]
    input1 = 8
    input2 = 4
    input3 = 2
    input4 = 1
    input5 = 1
    input6 = 1
    input7 = 1
    offset = _T3_CD_EVEN
    output1 = _T3_CD_EVEN_DIV

# Make LED brighter if the fader is in its default position
[compare]
    input = _T3_CD_ODD
    compare = 3
    ifequal = 1
    else = 0.3
    output = _T3_CD_ODD_LED

# Combine all triggers for clearing
[mixer]
    input1 = _T3_CLEAR
    input2 = _T3_BPCDODD_1
    output = _T3_CLEAR_PCDODD

# Clock division for odd divides/multipliers
[motorfader]
    select = _GLOBAL_FADER_SELECTION * _T3_ARP_OFF
    selectat = 13
    loadpreset = _T3_LOAD_PRESET
    savepreset = _T3_SAVE_PRESET
    notches = 7
    startvalue = 3
    ledcolor = 1.2
    ledvalue = _T3_CD_ODD_LED
    clear = _T3_CLEAR_PCDODD
    output = _T3_CD_ODD
    fader = 8
    button = _T3_BPCDODD_1

# Select the multiplier based on the fader position
[switch]
    input1 = 1
    input2 = 1
    input3 = 1
    input4 = 1
    input5 = 3
    input6 = 5
    input7 = 7
    offset = _T3_CD_ODD
    output1 = _T3_CD_ODD_MULT

# Select the division based on the fader position
[switch]
    input1 = 7
    input2 = 5
    input3 = 3
    input4 = 1
    input5 = 1
    input6 = 1
    input7 = 1
    offset = _T3_CD_ODD
    output1 = _T3_CD_ODD_DIV

# Switch on/off arpeggio mode for track 3
[button]
    select = _SELECTED_TRACK
    selectat = 3
    button = B2.9
    led = L2.9
    output = _T3_ARP
    negated = _T3_ARP_OFF

# Auto reset arpeggiator after N number of steps
[motorfader]
    select = _GLOBAL_FADER_SELECTION * _T3_ARP
    selectat = 13
    loadpreset = _T3_LOAD_PRESET
    savepreset = _T3_SAVE_PRESET
    fader = 1
    notches = 17
    ledcolor = 0.8
    ledvalue = 1
    output = _T3_ARP_AUTORESET

# Enable ping pong mode
[motorfader]
    select = _GLOBAL_FADER_SELECTION * _T3_ARP
    selectat = 13
    loadpreset = _T3_LOAD_PRESET
    savepreset = _T3_SAVE_PRESET
    fader = 2
    notches = 3
    ledcolor = 0.85
    ledvalue = 1
    output = _T3_ARP_DIRECTION

# Provide gate for reversed direction of arpeggio
[compare]
    input = _T3_ARP_DIRECTION
    compare = 2
    ifequal = 1
    else = 0
    output = _T3_ARP_UPDOWN

# Provide gate for ping pong of arpeggio
[compare]
    input = _T3_ARP_DIRECTION
    compare = 1
    ifequal = 1
    else = 0
    output = _T3_ARP_PINGPONG

# Seperate octave switch for the arpeggio
[motorfader]
    select = _GLOBAL_FADER_SELECTION * _T3_ARP
    selectat = 13
    loadpreset = _T3_LOAD_PRESET
    savepreset = _T3_SAVE_PRESET
    fader = 3
    notches = 5
    ledcolor = 0.2
    startvalue = 2
    ledvalue = 1
    output = _T3_ARP_OCTAVE_SWITCH

# Enable butterfly mode
[motorfader]
    select = _GLOBAL_FADER_SELECTION * _T3_ARP
    selectat = 13
    loadpreset = _T3_LOAD_PRESET
    savepreset = _T3_SAVE_PRESET
    fader = 4
    notches = 2
    ledcolor = 1.15
    ledvalue = 1
    output = _T3_ARP_BUTTERFLY

# Select one of three octaving patterns
[motorfader]
    select = _GLOBAL_FADER_SELECTION * _T3_ARP
    selectat = 13
    loadpreset = _T3_LOAD_PRESET
    savepreset = _T3_SAVE_PRESET
    fader = 5
    notches = 3
    ledcolor = 0.4
    ledvalue = 1
    output = _T3_ARP_OCTAVING

# Compute octave offset pitches from fader setting
[copy]
    input = _T3_ARP_OCTAVE_SWITCH * 1V - 2V
    output = _T3_ARP_OCTAVE

# Select the arpeggio pattern
[motorfader]
    select = _GLOBAL_FADER_SELECTION * _T3_ARP
    selectat = 13
    loadpreset = _T3_LOAD_PRESET
    savepreset = _T3_SAVE_PRESET
    fader = 6
    notches = 7
    ledcolor = 0.3
    ledvalue = 1
    output = _T3_ARP_PATTERN

# Select one of four different drop patterns
[motorfader]
    select = _GLOBAL_FADER_SELECTION * _T3_ARP
    selectat = 13
    loadpreset = _T3_LOAD_PRESET
    savepreset = _T3_SAVE_PRESET
    fader = 7
    notches = 4
    ledcolor = 0.5
    ledvalue = 1
    output = _T3_ARP_DROP

# Select one of several clocking patterns
[motorfader]
    select = _GLOBAL_FADER_SELECTION * _T3_ARP
    selectat = 13
    loadpreset = _T3_LOAD_PRESET
    savepreset = _T3_SAVE_PRESET
    fader = 8
    notches = 8
    ledcolor = 1.1
    ledvalue = 1
    output = _T3_ARP_CLOCKING

# Button for loading preset 1 on track 3
[button]
    select = _SELECTED_TRACK
    selectat = 3
    button = B2.1 * _CONTROL
    states = 1
    onvalue = 1
    output = _T3_P1_LOAD

# Button for saving preset 1 on track 3
[button]
    select = _SELECTED_TRACK
    selectat = 3
    button = B2.1
    states = 1
    onvalue = 1
    longpress = _T3_P1_SAVE

# Five blinks for the LED of the preset button on saving
[burst]
    trigger = _T3_P1_SAVE
    hz = 5
    count = 5
    output = _T3_P1_SAVE_LED

# Write the save LED animation to the LED if the track is still selected.
[select]
    select = _SELECTED_TRACK
    selectat = 3
    input = _T3_P1_SAVE_LED + 0.3
    output = L2.1

# Button for loading preset 2 on track 3
[button]
    select = _SELECTED_TRACK
    selectat = 3
    button = B2.2 * _CONTROL
    states = 1
    onvalue = 2
    output = _T3_P2_LOAD

# Button for saving preset 2 on track 3
[button]
    select = _SELECTED_TRACK
    selectat = 3
    button = B2.2
    states = 1
    onvalue = 2
    longpress = _T3_P2_SAVE

# Five blinks for the LED of the preset button on saving
[burst]
    trigger = _T3_P2_SAVE
    hz = 5
    count = 5
    output = _T3_P2_SAVE_LED

# Write the save LED animation to the LED if the track is still selected.
[select]
    select = _SELECTED_TRACK
    selectat = 3
    input = _T3_P2_SAVE_LED + 0.3
    output = L2.2

# Button for loading preset 3 on track 3
[button]
    select = _SELECTED_TRACK
    selectat = 3
    button = B2.3 * _CONTROL
    states = 1
    onvalue = 3
    output = _T3_P3_LOAD

# Button for saving preset 3 on track 3
[button]
    select = _SELECTED_TRACK
    selectat = 3
    button = B2.3
    states = 1
    onvalue = 3
    longpress = _T3_P3_SAVE

# Five blinks for the LED of the preset button on saving
[burst]
    trigger = _T3_P3_SAVE
    hz = 5
    count = 5
    output = _T3_P3_SAVE_LED

# Write the save LED animation to the LED if the track is still selected.
[select]
    select = _SELECTED_TRACK
    selectat = 3
    input = _T3_P3_SAVE_LED + 0.3
    output = L2.3

# Create a trigger with the preset as value when any of the presets is loaded
[mixer]
    input1 = _T3_P1_LOAD
    input2 = _T3_P2_LOAD
    input3 = _T3_P3_LOAD
    output = _T3_LOAD_PRESET

# Create a trigger with the preset as value when any of the presets is saved
[mixer]
    input1 = _T3_P1_SAVE * 1
    input2 = _T3_P2_SAVE * 2
    input3 = _T3_P3_SAVE * 3
    output = _T3_SAVE_PRESET

# Select interval note 1 from the current scale
[button]
    select = _SELECTED_TRACK
    selectat = 3
    button = B1.1
    led = L1.1
    startvalue = 1
    output = _T3_SEL_1

# Select interval note 2 from the current scale
[button]
    select = _SELECTED_TRACK
    selectat = 3
    button = B1.2
    led = L1.2
    startvalue = 1
    output = _T3_SEL_2

# Select interval note 3 from the current scale
[button]
    select = _SELECTED_TRACK
    selectat = 3
    button = B1.3
    led = L1.3
    startvalue = 1
    output = _T3_SEL_3

# Select interval note 4 from the current scale
[button]
    select = _SELECTED_TRACK
    selectat = 3
    button = B1.4
    led = L1.4
    startvalue = 1
    output = _T3_SEL_4

# Select interval note 5 from the current scale
[button]
    select = _SELECTED_TRACK
    selectat = 3
    button = B1.5
    led = L1.5
    startvalue = 1
    output = _T3_SEL_5

# Select interval note 6 from the current scale
[button]
    select = _SELECTED_TRACK
    selectat = 3
    button = B1.6
    led = L1.6
    startvalue = 1
    output = _T3_SEL_6

# Select interval note 7 from the current scale
[button]
    select = _SELECTED_TRACK
    selectat = 3
    button = B1.7
    led = L1.7
    startvalue = 1
    output = _T3_SEL_7

# Clock divider for track 3
[clocktool]
    clock = _CLOCK
    reset = _RESET
    divide = _T3_CD_EVEN_DIV * _T3_CD_ODD_DIV
    multiply = _T3_CD_EVEN_MULT * _T3_CD_ODD_MULT
    output = _T3_CLOCK

# Mix global and per-track diatonic transposition
[mixer]
    input1 = -1 * 7 + _T3_NOTESHIFT_CLOCKED
    input2 = -1 * 7 + _GLOBAL_NOTESHIFT_CLOCKED
    output = _T3_NOTESHIFT_SUM

# collect different sources of resetting the start/end point
[logic]
    input1 = _T3_CLEAR + _T3_PEACE
    input2 = _RANGE * _CONTROL
    or = _T3_CLEARSTARTEND

# Note shift for each step repetition
[pot]
    select = _T3_ARP * 100 + _SELECTED_TRACK
    selectat = 3
    pot = P1.1
    discrete = 15
    startvalue = 7
    output = _T3_REPEATSHIFT

# Note shift for each ratchet
[pot]
    select = _T3_ARP * 100 + _SELECTED_TRACK
    selectat = 3
    pot = P1.2
    discrete = 15
    startvalue = 7
    output = _T3_RATCHETSHIFT

# Sequencer for pitch and gate of track 3
[motoquencer]
    select = _GLOBAL_FADER_SELECTION
    selectat = 3
    page = _PAGE
    copy = _T3_COPY_BUTTON * _NO_CONTROL
    paste = _T3_COPY_BUTTON * _CONTROL
    loadpreset = _T3_LOAD_PRESET
    savepreset = _T3_SAVE_PRESET
    clear = _T3_CLEAR
    select1 = _T3_SEL_1 + _T3_12TONE
    select3 = _T3_SEL_2 + _T3_12TONE
    select5 = _T3_SEL_3 + _T3_12TONE
    select7 = _T3_SEL_4 + _T3_12TONE
    select9 = _T3_SEL_5 + _T3_12TONE
    select11 = _T3_SEL_6 + _T3_12TONE
    select13 = _T3_SEL_7 + _T3_12TONE
    selectfill1 = _T3_12TONE
    selectfill2 = _T3_12TONE
    selectfill3 = _T3_12TONE
    selectfill4 = _T3_12TONE
    selectfill5 = _T3_12TONE
    degree = _DEGREE
    clock = _T3_CLOCK * _RUNNING
    taptempo = _T3_CLOCK
    reset = _RESET + _T3_RESET
    cv = _T3_PITCH
    cvrange = _T3_CVRANGE * 1V + 1V
    defaultgate = 0
    gate = _T3_SEQUENCER_GATE
    numfaders = 8
    numsteps = 16
    linktonext = 1
    autoreset = _T3_AUTORESET
    shiftsteps = _T3_SHIFTSTEPS
    pattern = -1 * 2 + _T3_PATTERN_FADER
    direction = _T3_DIRECTION
    pingpong = _T3_PINGPONG
    selectnoteshift = _T3_NOTESHIFT_SUM
    tuningmode = _TUNINGMODE
    tuningpitch = _GLOBAL_OCTAVE_SWITCH * 1V
    accumulatorrange = _T3_ACCU_RANGE
    root = _ROOT * _T3_TBR_OFF
    transpose = _T3_TBR
    luckybuttons = _T3_LUCKY * _CONTROL
    luckyfaders = _T3_LUCKY * _NO_CONTROL
    luckychance = 1 / 8
    luckyamount = 0.5 * _NO_CONTROL + 0.5
    luckyscope = 3
    fadermode = _FADERMODE
    mute = -1 * _RUNNING + 1
    form = _T3_FORM
    invert = _T3_INVERT
    buttonmode = _BUTTONMODE
    composemode = _COMPOSEMODE
    clearskips = _T3_CLEAR + _T3_PEACE
    clearrepeats = _T3_CLEAR + _T3_PEACE
    clearstartend = _T3_CLEARSTARTEND
    repeatshift = -1 * 7 + _T3_REPEATSHIFT
    ratchetshift = -1 * 7 + _T3_RATCHETSHIFT

# Sequencer for velocity
[motoquencer]
    select = _GLOBAL_FADER_SELECTION
    selectat = 3
    page = _PAGE
    copy = _T3_COPY_BUTTON * _NO_CONTROL
    paste = _T3_COPY_BUTTON * _CONTROL
    clear = _T3_CLEAR
    loadpreset = _T3_LOAD_PRESET
    savepreset = _T3_SAVE_PRESET
    cv = _T3_VELOCITY
    cvrange = 1
    defaultcv = 0.5
    quantize = 0
    linktonext = 1

# Copy velocity to CV output
[copy]
    input = _T3_VELOCITY
    output = O6

# Sequencer for gate length and glide
[motoquencer]
    select = _GLOBAL_FADER_SELECTION
    selectat = 3
    page = _PAGE
    copy = _T3_COPY_BUTTON * _NO_CONTROL
    paste = _T3_COPY_BUTTON * _CONTROL
    clear = _T3_CLEAR
    loadpreset = _T3_LOAD_PRESET
    savepreset = _T3_SAVE_PRESET
    cv = _T3_GATELENGTH
    cvrange = 1
    defaultcv = 0.5
    defaultgate = 0
    quantize = 0
    buttoncolor = 0.5
    linktonext = 1
    gate = _T3_GLIDE

# Sequencer for activity level per step
[motoquencer]
    select = _GLOBAL_FADER_SELECTION
    selectat = 3
    clear = _T3_CLEAR
    loadpreset = _T3_LOAD_PRESET
    savepreset = _T3_SAVE_PRESET
    cv = _T3_STEP_ACTIVITY
    quantize = 0
    defaultcv = 7
    cvnotches = 8

# Silence the gate if the step does not reach the required activity
[compare]
    input = _T3_STEP_ACTIVITY
    compare = _T3_ACTIVITY
    ifless = 0
    else = _T3_SEQUENCER_GATE
    output = _T3_ACT_GATE

# Switch gate to arpeggiator if active
[compare]
    input = _T3_ARP
    compare = 0
    ifequal = _T3_ACT_GATE
    else = _T3_ARP_CLOCK
    output = _T3_SWITCHED_GATE

# Logic for make the gate silent if the track is muted
[logic]
    input1 = -1 * _T3_MUTED + 1
    input2 = _T3_SWITCHED_GATE
    and = _T3_MUTED_GATE

# Select clock for arpeggiator of track 3
[switch]
    input1 = _CLOCK
    input2 = _T3_SEQUENCER_GATE
    input3 = _CLOCK_3_2
    input4 = _CLOCK_2_1
    input5 = _CLOCK_3_1
    input6 = _CLOCK_4_1
    input7 = _CLOCK_6_1
    input8 = _CLOCK_8_1
    output1 = _T3_ARP_CLOCK
    offset = _T3_ARP_CLOCKING

# Create taptempo for arp clock to be used for gate length
[switch]
    input1 = _CLOCK
    input2 = _T3_CLOCK
    input3 = _CLOCK_3_2
    input4 = _CLOCK_2_1
    input5 = _CLOCK_3_1
    input6 = _CLOCK_4_1
    input7 = _CLOCK_6_1
    input8 = _CLOCK_8_1
    output1 = _T3_ARP_TAPTEMPO
    offset = _T3_ARP_CLOCKING * _T3_ARP

# Virtual pot for base pitch of arpeggiator
[pot]
    select = _T3_ARP * 100 + _SELECTED_TRACK
    selectat = 103
    pot = P1.2
    outputscale = 3V
    output = _T3_ARP_PITCH

# Virtual pot for pitch range of arpeggiator
[pot]
    select = _T3_ARP * 100 + _SELECTED_TRACK
    selectat = 103
    pot = P1.1
    outputscale = 4V
    output = _T3_ARP_RANGE

# Arpeggiator for track 3
[arpeggio]
    select1 = _T3_SEL_1
    select3 = _T3_SEL_2
    select5 = _T3_SEL_3
    select7 = _T3_SEL_4
    select9 = _T3_SEL_5
    select11 = _T3_SEL_6
    select13 = _T3_SEL_7
    selectfill1 = 0
    selectfill2 = 0
    selectfill3 = 0
    selectfill4 = 0
    selectfill5 = 0
    root = _ROOT
    degree = _DEGREE
    clock = _T3_ARP_CLOCK * _RUNNING
    reset = _RESET
    tuningmode = _TUNINGMODE
    direction = _T3_ARP_UPDOWN
    pingpong = _T3_ARP_PINGPONG
    butterfly = _T3_ARP_BUTTERFLY
    octaves = _T3_ARP_OCTAVING
    drop = _T3_ARP_DROP
    pattern = _T3_ARP_PATTERN
    autoreset = _T3_ARP_AUTORESET
    pitch = _T3_ARP_PITCH
    range = _T3_ARP_RANGE
    output = _T3_ARP_OUTPUT

# Switch pitch output between sequencer and arpeggiator
[compare]
    input = _T3_ARP
    compare = 0
    ifequal = _T3_PITCH
    else = _T3_ARP_OUTPUT + _T3_ARP_OCTAVE
    output = _T3_SWITCHED_PITCH

# Mix all sources of pitch together
[mixer]
    input1 = _T3_OCTAVE * 1V - 2V
    input2 = _T3_SWITCHED_PITCH
    input3 = _GLOBAL_OCTAVE_SWITCH * 1V - 2V
    input4 = _GLOBAL_TRANSPOSITION * 0.00833333 - 1V
    output = _T3_FINAL_PITCH

# Slew limiter for glides
[slew]
    input = _T3_FINAL_PITCH
    exponential = O5
    slew = _T3_GLIDE * _T3_GLIDE_LENGTH

# Provide steady tap tempo for arpeggiator's gate length
[compare]
    input = _T3_ARP
    compare = 0
    ifequal = _T3_CLOCK
    else = _T3_ARP_TAPTEMPO
    output = _T3_GATEREF

# Compute gate length for arpeggiator output
[gatetool]
    taptempo = _T3_GATEREF
    inputgate = _T3_MUTED_GATE * _RUNNING
    outputgate = _T3_FINAL_GATE
    gatelength = _T3_GATELENGTH * 0.9 + 0.05

# Copy the gate signal to the CV jack
[copy]
    input = _T3_FINAL_GATE
    output = G1.3

# -------------------------------------------------
# Track 4
# -------------------------------------------------

# Provide trigger when the clear button is pressed (no Ctrl needed)
[button]
    select = _SELECTED_TRACK
    selectat = 4
    button = B2.4
    shortpress = _T4_CLEAR_BUTTON
    states = 1

# Clear button with control together clears the track
[copy]
    input = _T4_CLEAR_BUTTON * _CONTROL
    output = _T4_CLEAR

# Clear button without control removes all mode that alter the pattern length
[logic]
    input1 = _T4_CLEAR_BUTTON
    input2 = -1 * _CONTROL + 1
    and = _T4_PEACE

# Five blinks for the clear LED
[burst]
    trigger = _T4_CLEAR + _FACTORY_RESET
    hz = 5
    count = 5
    output = _T4_CLEAR_LED

# LED only blinks when track is selected
[select]
    select = _SELECTED_TRACK
    selectat = 4
    input = _T4_CLEAR_LED + 0.3
    output = L2.4

# Reset track immediately
[button]
    select = _SELECTED_TRACK
    selectat = 4
    button = B2.11 * _NO_CONTROL
    states = 1
    output = _T4_RESET

# Button for copying the current page of the current track
[button]
    select = _SELECTED_TRACK
    selectat = 4
    button = B2.24
    states = 1
    output = _T4_COPY_BUTTON

# This button cycles between the song froms A, AAAB and AABB
[button]
    select = _SELECTED_TRACK
    selectat = 4
    clear = _T4_CLEAR
    loadpreset = _T4_LOAD_PRESET
    savepreset = _T4_SAVE_PRESET
    button = B2.7
    led = L2.7
    output = _T4_FORM
    states = 3

# Disables scale and note selection if on
[button]
    select = _SELECTED_TRACK
    selectat = 4
    loadpreset = _T4_LOAD_PRESET
    savepreset = _T4_SAVE_PRESET
    button = B2.8
    led = L2.8
    output = _T4_12TONE

# Enable transposing the melody by the difference of the root note to C
[button]
    select = _SELECTED_TRACK
    selectat = 4
    loadpreset = _T4_LOAD_PRESET
    savepreset = _T4_SAVE_PRESET
    button = B2.5
    led = L2.5
    output = _T4_TBR_ON
    inverted = _T4_TBR_OFF

# Provide the amount of 'transpose by root' in a patch cable
[compare]
    input = _T4_TBR_ON
    compare = 1
    ifequal = _ROOT / 120
    output = _T4_TBR

# Hold to contiously step faders to random positions, hold with Ctrl to change buttons
[button]
    select = _SELECTED_TRACK
    selectat = 4
    button = B2.10
    states = 1
    onvalue = _LUCKY_CLOCK
    output = _T4_LUCKY

# Selects melodic inversion (swap low and high notes)
[button]
    select = _SELECTED_TRACK
    selectat = 4
    clear = _T4_CLEAR
    loadpreset = _T4_LOAD_PRESET
    savepreset = _T4_SAVE_PRESET
    button = B2.6
    led = L2.6
    output = _T4_INVERT

# Make LED brighter if the fader is in its default position
[compare]
    input = _T4_AUTORESET
    compare = 0
    ifequal = 1
    else = 0.3
    output = _T4_AUTORESET_LED

# Combine all triggers for clearing
[mixer]
    input1 = _T4_CLEAR + _T4_PEACE
    input2 = _T4_BPAR_1
    output = _T4_CLEAR_PAR

# Reset track after X clock ticks
[motorfader]
    select = _GLOBAL_FADER_SELECTION * _T4_ARP_OFF
    selectat = 14
    loadpreset = _T4_LOAD_PRESET
    savepreset = _T4_SAVE_PRESET
    notches = 17
    startvalue = 0
    ledcolor = 0.8
    ledvalue = _T4_AUTORESET_LED
    clear = _T4_CLEAR_PAR
    output = _T4_AUTORESET
    fader = 1
    button = _T4_BPAR_1

# Make LED brighter if the fader is in its default position
[compare]
    input = _T4_SHIFTSTEPS
    compare = 0
    ifequal = 1
    else = 0.3
    output = _T4_SHIFTSTEPS_LED

# Combine all triggers for clearing
[mixer]
    input1 = _T4_CLEAR + _T4_PEACE
    input2 = _T4_BPSS_1
    output = _T4_CLEAR_PSS

# Shift the steps of the sequence by this number
[motorfader]
    select = _GLOBAL_FADER_SELECTION * _T4_ARP_OFF
    selectat = 14
    loadpreset = _T4_LOAD_PRESET
    savepreset = _T4_SAVE_PRESET
    notches = 17
    startvalue = 0
    ledcolor = 0.6
    ledvalue = _T4_SHIFTSTEPS_LED
    clear = _T4_CLEAR_PSS
    output = _T4_SHIFTSTEPS
    fader = 2
    button = _T4_BPSS_1

# Make LED brighter if the fader is in its default position
[compare]
    input = _T4_OCTAVE
    compare = 2
    ifequal = 1
    else = 0.3
    output = _T4_OCTAVE_LED

# Combine all triggers for clearing
[mixer]
    input1 = _T4_CLEAR
    input2 = _T4_BPOCT_1 + _T4_BPOCT_2
    output = _T4_CLEAR_POCT

# Octave switch: -2, -1, 0, +1, +2
[motorfader]
    select = _GLOBAL_FADER_SELECTION * _T4_ARP_OFF
    selectat = 14
    loadpreset = _T4_LOAD_PRESET
    savepreset = _T4_SAVE_PRESET
    notches = 5
    startvalue = 2
    ledcolor = 0.2
    ledvalue = _T4_OCTAVE_LED
    clear = _T4_CLEAR_POCT
    sharewithnext = 1
    fader = 3
    button = _T4_BPOCT_1

# Octave switch: -2, -1, 0, +1, +2
[motorfader]
    loadpreset = _T4_LOAD_PRESET
    savepreset = _T4_SAVE_PRESET
    notches = 5
    startvalue = 2
    ledcolor = 0.2
    ledvalue = _T4_OCTAVE_LED
    clear = _T4_CLEAR_POCT
    select = _GLOBAL_FADER_SELECTION
    selectat = 98
    fader = 7
    button = _T4_BPOCT_2
    output = _T4_OCTAVE

# Make LED brighter if the fader is in its default position
[compare]
    input = _T4_NOTESHIFT
    compare = 7
    ifequal = 1
    else = 0.3
    output = _T4_NOTESHIFT_LED

# Combine all triggers for clearing
[mixer]
    input1 = _T4_CLEAR
    input2 = _T4_BPDT_1 + _T4_BPDT_2
    output = _T4_CLEAR_PDT

# Diatonic transposition
[motorfader]
    select = _GLOBAL_FADER_SELECTION * _T4_ARP_OFF
    selectat = 14
    loadpreset = _T4_LOAD_PRESET
    savepreset = _T4_SAVE_PRESET
    notches = 15
    startvalue = 7
    ledcolor = 0.4
    ledvalue = _T4_NOTESHIFT_LED
    clear = _T4_CLEAR_PDT
    sharewithnext = 1
    fader = 4
    button = _T4_BPDT_1

# Diatonic transposition
[motorfader]
    loadpreset = _T4_LOAD_PRESET
    savepreset = _T4_SAVE_PRESET
    notches = 15
    startvalue = 7
    ledcolor = 0.4
    ledvalue = _T4_NOTESHIFT_LED
    clear = _T4_CLEAR_PDT
    select = _GLOBAL_FADER_SELECTION
    selectat = 98
    fader = 8
    button = _T4_BPDT_2
    output = _T4_NOTESHIFT

# Prevent diatonic transposition from happending between clock ticks
[sample]
    input = _T4_NOTESHIFT
    sample = _CLOCK
    output = _T4_NOTESHIFT_CLOCKED

# Make LED brighter if the fader is in its default position
[compare]
    input = _T4_ACTIVITY
    compare = 0
    ifequal = 1
    else = 0.3
    output = _T4_ACTIVITY_LED

# Combine all triggers for clearing
[mixer]
    input1 = _T4_CLEAR
    input2 = _T4_BPACT_1
    output = _T4_CLEAR_PACT

# Minimum activity a step needs to have to be played
[motorfader]
    select = _GLOBAL_FADER_SELECTION * _T4_ARP_OFF
    selectat = 14
    loadpreset = _T4_LOAD_PRESET
    savepreset = _T4_SAVE_PRESET
    notches = 8
    startvalue = 0
    ledcolor = 0.55
    ledvalue = _T4_ACTIVITY_LED
    clear = _T4_CLEAR_PACT
    output = _T4_ACTIVITY
    fader = 5
    button = _T4_BPACT_1

# Make LED brighter if the fader is in its default position
[compare]
    input = _T4_PATTERN_FADER
    compare = 0
    ifequal = 1
    else = 0.3
    output = _T4_PATTERN_FADER_LED

# Combine all triggers for clearing
[mixer]
    input1 = _T4_CLEAR + _T4_PEACE
    input2 = _T4_BPPAT_1
    output = _T4_CLEAR_PPAT

# Movement pattern: normal, reverse, ping pong, >><, 2><, etc.
[motorfader]
    select = _GLOBAL_FADER_SELECTION * _T4_ARP_OFF
    selectat = 14
    loadpreset = _T4_LOAD_PRESET
    savepreset = _T4_SAVE_PRESET
    notches = 10
    startvalue = 0
    ledcolor = 0.3
    ledvalue = _T4_PATTERN_FADER_LED
    clear = _T4_CLEAR_PPAT
    output = _T4_PATTERN_FADER
    fader = 6
    button = _T4_BPPAT_1

# Set the direction to reverse on fader position 1
[compare]
    input = _T4_PATTERN_FADER
    compare = 1
    ifequal = 1
    output = _T4_DIRECTION

# Enable ping pong on fader position 2
[compare]
    input = _T4_PATTERN_FADER
    compare = 2
    ifequal = 1
    output = _T4_PINGPONG

# Make LED brighter if the fader is in its default position
[compare]
    input = _T4_CD_EVEN
    compare = 3
    ifequal = 1
    else = 0.3
    output = _T4_CD_EVEN_LED

# Combine all triggers for clearing
[mixer]
    input1 = _T4_CLEAR
    input2 = _T4_BPCDEVEN_1
    output = _T4_CLEAR_PCDEVEN

# Clock division for even divides/multipliers
[motorfader]
    select = _GLOBAL_FADER_SELECTION * _T4_ARP_OFF
    selectat = 14
    loadpreset = _T4_LOAD_PRESET
    savepreset = _T4_SAVE_PRESET
    notches = 7
    startvalue = 3
    ledcolor = 1.1
    ledvalue = _T4_CD_EVEN_LED
    clear = _T4_CLEAR_PCDEVEN
    output = _T4_CD_EVEN
    fader = 7
    button = _T4_BPCDEVEN_1

# Select the multiplier based on the fader position
[switch]
    input1 = 1
    input2 = 1
    input3 = 1
    input4 = 1
    input5 = 2
    input6 = 4
    input7 = 8
    offset = _T4_CD_EVEN
    output1 = _T4_CD_EVEN_MULT

# Select the division based on the fader position
[switch]
    input1 = 8
    input2 = 4
    input3 = 2
    input4 = 1
    input5 = 1
    input6 = 1
    input7 = 1
    offset = _T4_CD_EVEN
    output1 = _T4_CD_EVEN_DIV

# Make LED brighter if the fader is in its default position
[compare]
    input = _T4_CD_ODD
    compare = 3
    ifequal = 1
    else = 0.3
    output = _T4_CD_ODD_LED

# Combine all triggers for clearing
[mixer]
    input1 = _T4_CLEAR
    input2 = _T4_BPCDODD_1
    output = _T4_CLEAR_PCDODD

# Clock division for odd divides/multipliers
[motorfader]
    select = _GLOBAL_FADER_SELECTION * _T4_ARP_OFF
    selectat = 14
    loadpreset = _T4_LOAD_PRESET
    savepreset = _T4_SAVE_PRESET
    notches = 7
    startvalue = 3
    ledcolor = 1.2
    ledvalue = _T4_CD_ODD_LED
    clear = _T4_CLEAR_PCDODD
    output = _T4_CD_ODD
    fader = 8
    button = _T4_BPCDODD_1

# Select the multiplier based on the fader position
[switch]
    input1 = 1
    input2 = 1
    input3 = 1
    input4 = 1
    input5 = 3
    input6 = 5
    input7 = 7
    offset = _T4_CD_ODD
    output1 = _T4_CD_ODD_MULT

# Select the division based on the fader position
[switch]
    input1 = 7
    input2 = 5
    input3 = 3
    input4 = 1
    input5 = 1
    input6 = 1
    input7 = 1
    offset = _T4_CD_ODD
    output1 = _T4_CD_ODD_DIV

# Switch on/off arpeggio mode for track 4
[button]
    select = _SELECTED_TRACK
    selectat = 4
    button = B2.9
    led = L2.9
    output = _T4_ARP
    negated = _T4_ARP_OFF

# Auto reset arpeggiator after N number of steps
[motorfader]
    select = _GLOBAL_FADER_SELECTION * _T4_ARP
    selectat = 14
    loadpreset = _T4_LOAD_PRESET
    savepreset = _T4_SAVE_PRESET
    fader = 1
    notches = 17
    ledcolor = 0.8
    ledvalue = 1
    output = _T4_ARP_AUTORESET

# Enable ping pong mode
[motorfader]
    select = _GLOBAL_FADER_SELECTION * _T4_ARP
    selectat = 14
    loadpreset = _T4_LOAD_PRESET
    savepreset = _T4_SAVE_PRESET
    fader = 2
    notches = 3
    ledcolor = 0.85
    ledvalue = 1
    output = _T4_ARP_DIRECTION

# Provide gate for reversed direction of arpeggio
[compare]
    input = _T4_ARP_DIRECTION
    compare = 2
    ifequal = 1
    else = 0
    output = _T4_ARP_UPDOWN

# Provide gate for ping pong of arpeggio
[compare]
    input = _T4_ARP_DIRECTION
    compare = 1
    ifequal = 1
    else = 0
    output = _T4_ARP_PINGPONG

# Seperate octave switch for the arpeggio
[motorfader]
    select = _GLOBAL_FADER_SELECTION * _T4_ARP
    selectat = 14
    loadpreset = _T4_LOAD_PRESET
    savepreset = _T4_SAVE_PRESET
    fader = 3
    notches = 5
    ledcolor = 0.2
    startvalue = 2
    ledvalue = 1
    output = _T4_ARP_OCTAVE_SWITCH

# Enable butterfly mode
[motorfader]
    select = _GLOBAL_FADER_SELECTION * _T4_ARP
    selectat = 14
    loadpreset = _T4_LOAD_PRESET
    savepreset = _T4_SAVE_PRESET
    fader = 4
    notches = 2
    ledcolor = 1.15
    ledvalue = 1
    output = _T4_ARP_BUTTERFLY

# Select one of three octaving patterns
[motorfader]
    select = _GLOBAL_FADER_SELECTION * _T4_ARP
    selectat = 14
    loadpreset = _T4_LOAD_PRESET
    savepreset = _T4_SAVE_PRESET
    fader = 5
    notches = 3
    ledcolor = 0.4
    ledvalue = 1
    output = _T4_ARP_OCTAVING

# Compute octave offset pitches from fader setting
[copy]
    input = _T4_ARP_OCTAVE_SWITCH * 1V - 2V
    output = _T4_ARP_OCTAVE

# Select the arpeggio pattern
[motorfader]
    select = _GLOBAL_FADER_SELECTION * _T4_ARP
    selectat = 14
    loadpreset = _T4_LOAD_PRESET
    savepreset = _T4_SAVE_PRESET
    fader = 6
    notches = 7
    ledcolor = 0.3
    ledvalue = 1
    output = _T4_ARP_PATTERN

# Select one of four different drop patterns
[motorfader]
    select = _GLOBAL_FADER_SELECTION * _T4_ARP
    selectat = 14
    loadpreset = _T4_LOAD_PRESET
    savepreset = _T4_SAVE_PRESET
    fader = 7
    notches = 4
    ledcolor = 0.5
    ledvalue = 1
    output = _T4_ARP_DROP

# Select one of several clocking patterns
[motorfader]
    select = _GLOBAL_FADER_SELECTION * _T4_ARP
    selectat = 14
    loadpreset = _T4_LOAD_PRESET
    savepreset = _T4_SAVE_PRESET
    fader = 8
    notches = 8
    ledcolor = 1.1
    ledvalue = 1
    output = _T4_ARP_CLOCKING

# Button for loading preset 1 on track 4
[button]
    select = _SELECTED_TRACK
    selectat = 4
    button = B2.1 * _CONTROL
    states = 1
    onvalue = 1
    output = _T4_P1_LOAD

# Button for saving preset 1 on track 4
[button]
    select = _SELECTED_TRACK
    selectat = 4
    button = B2.1
    states = 1
    onvalue = 1
    longpress = _T4_P1_SAVE

# Five blinks for the LED of the preset button on saving
[burst]
    trigger = _T4_P1_SAVE
    hz = 5
    count = 5
    output = _T4_P1_SAVE_LED

# Write the save LED animation to the LED if the track is still selected.
[select]
    select = _SELECTED_TRACK
    selectat = 4
    input = _T4_P1_SAVE_LED + 0.3
    output = L2.1

# Button for loading preset 2 on track 4
[button]
    select = _SELECTED_TRACK
    selectat = 4
    button = B2.2 * _CONTROL
    states = 1
    onvalue = 2
    output = _T4_P2_LOAD

# Button for saving preset 2 on track 4
[button]
    select = _SELECTED_TRACK
    selectat = 4
    button = B2.2
    states = 1
    onvalue = 2
    longpress = _T4_P2_SAVE

# Five blinks for the LED of the preset button on saving
[burst]
    trigger = _T4_P2_SAVE
    hz = 5
    count = 5
    output = _T4_P2_SAVE_LED

# Write the save LED animation to the LED if the track is still selected.
[select]
    select = _SELECTED_TRACK
    selectat = 4
    input = _T4_P2_SAVE_LED + 0.3
    output = L2.2

# Button for loading preset 3 on track 4
[button]
    select = _SELECTED_TRACK
    selectat = 4
    button = B2.3 * _CONTROL
    states = 1
    onvalue = 3
    output = _T4_P3_LOAD

# Button for saving preset 3 on track 4
[button]
    select = _SELECTED_TRACK
    selectat = 4
    button = B2.3
    states = 1
    onvalue = 3
    longpress = _T4_P3_SAVE

# Five blinks for the LED of the preset button on saving
[burst]
    trigger = _T4_P3_SAVE
    hz = 5
    count = 5
    output = _T4_P3_SAVE_LED

# Write the save LED animation to the LED if the track is still selected.
[select]
    select = _SELECTED_TRACK
    selectat = 4
    input = _T4_P3_SAVE_LED + 0.3
    output = L2.3

# Create a trigger with the preset as value when any of the presets is loaded
[mixer]
    input1 = _T4_P1_LOAD
    input2 = _T4_P2_LOAD
    input3 = _T4_P3_LOAD
    output = _T4_LOAD_PRESET

# Create a trigger with the preset as value when any of the presets is saved
[mixer]
    input1 = _T4_P1_SAVE * 1
    input2 = _T4_P2_SAVE * 2
    input3 = _T4_P3_SAVE * 3
    output = _T4_SAVE_PRESET

# Select interval note 1 from the current scale
[button]
    select = _SELECTED_TRACK
    selectat = 4
    button = B1.1
    led = L1.1
    startvalue = 1
    output = _T4_SEL_1

# Select interval note 2 from the current scale
[button]
    select = _SELECTED_TRACK
    selectat = 4
    button = B1.2
    led = L1.2
    startvalue = 1
    output = _T4_SEL_2

# Select interval note 3 from the current scale
[button]
    select = _SELECTED_TRACK
    selectat = 4
    button = B1.3
    led = L1.3
    startvalue = 1
    output = _T4_SEL_3

# Select interval note 4 from the current scale
[button]
    select = _SELECTED_TRACK
    selectat = 4
    button = B1.4
    led = L1.4
    startvalue = 1
    output = _T4_SEL_4

# Select interval note 5 from the current scale
[button]
    select = _SELECTED_TRACK
    selectat = 4
    button = B1.5
    led = L1.5
    startvalue = 1
    output = _T4_SEL_5

# Select interval note 6 from the current scale
[button]
    select = _SELECTED_TRACK
    selectat = 4
    button = B1.6
    led = L1.6
    startvalue = 1
    output = _T4_SEL_6

# Select interval note 7 from the current scale
[button]
    select = _SELECTED_TRACK
    selectat = 4
    button = B1.7
    led = L1.7
    startvalue = 1
    output = _T4_SEL_7

# Clock divider for track 4
[clocktool]
    clock = _CLOCK
    reset = _RESET
    divide = _T4_CD_EVEN_DIV * _T4_CD_ODD_DIV
    multiply = _T4_CD_EVEN_MULT * _T4_CD_ODD_MULT
    output = _T4_CLOCK

# Mix global and per-track diatonic transposition
[mixer]
    input1 = -1 * 7 + _T4_NOTESHIFT_CLOCKED
    input2 = -1 * 7 + _GLOBAL_NOTESHIFT_CLOCKED
    output = _T4_NOTESHIFT_SUM

# collect different sources of resetting the start/end point
[logic]
    input1 = _T4_CLEAR + _T4_PEACE
    input2 = _RANGE * _CONTROL
    or = _T4_CLEARSTARTEND

# Note shift for each step repetition
[pot]
    select = _T4_ARP * 100 + _SELECTED_TRACK
    selectat = 4
    pot = P1.1
    discrete = 15
    startvalue = 7
    output = _T4_REPEATSHIFT

# Note shift for each ratchet
[pot]
    select = _T4_ARP * 100 + _SELECTED_TRACK
    selectat = 4
    pot = P1.2
    discrete = 15
    startvalue = 7
    output = _T4_RATCHETSHIFT

# Sequencer for pitch and gate of track 4
[motoquencer]
    select = _GLOBAL_FADER_SELECTION
    selectat = 4
    page = _PAGE
    copy = _T4_COPY_BUTTON * _NO_CONTROL
    paste = _T4_COPY_BUTTON * _CONTROL
    loadpreset = _T4_LOAD_PRESET
    savepreset = _T4_SAVE_PRESET
    clear = _T4_CLEAR
    select1 = _T4_SEL_1 + _T4_12TONE
    select3 = _T4_SEL_2 + _T4_12TONE
    select5 = _T4_SEL_3 + _T4_12TONE
    select7 = _T4_SEL_4 + _T4_12TONE
    select9 = _T4_SEL_5 + _T4_12TONE
    select11 = _T4_SEL_6 + _T4_12TONE
    select13 = _T4_SEL_7 + _T4_12TONE
    selectfill1 = _T4_12TONE
    selectfill2 = _T4_12TONE
    selectfill3 = _T4_12TONE
    selectfill4 = _T4_12TONE
    selectfill5 = _T4_12TONE
    degree = _DEGREE
    clock = _T4_CLOCK * _RUNNING
    taptempo = _T4_CLOCK
    reset = _RESET + _T4_RESET
    cv = _T4_PITCH
    cvrange = _T4_CVRANGE * 1V + 1V
    defaultgate = 0
    gate = _T4_SEQUENCER_GATE
    numfaders = 8
    numsteps = 16
    linktonext = 1
    autoreset = _T4_AUTORESET
    shiftsteps = _T4_SHIFTSTEPS
    pattern = -1 * 2 + _T4_PATTERN_FADER
    direction = _T4_DIRECTION
    pingpong = _T4_PINGPONG
    selectnoteshift = _T4_NOTESHIFT_SUM
    tuningmode = _TUNINGMODE
    tuningpitch = _GLOBAL_OCTAVE_SWITCH * 1V
    accumulatorrange = _T4_ACCU_RANGE
    root = _ROOT * _T4_TBR_OFF
    transpose = _T4_TBR
    luckybuttons = _T4_LUCKY * _CONTROL
    luckyfaders = _T4_LUCKY * _NO_CONTROL
    luckychance = 1 / 8
    luckyamount = 0.5 * _NO_CONTROL + 0.5
    luckyscope = 3
    fadermode = _FADERMODE
    mute = -1 * _RUNNING + 1
    form = _T4_FORM
    invert = _T4_INVERT
    buttonmode = _BUTTONMODE
    composemode = _COMPOSEMODE
    clearskips = _T4_CLEAR + _T4_PEACE
    clearrepeats = _T4_CLEAR + _T4_PEACE
    clearstartend = _T4_CLEARSTARTEND
    repeatshift = -1 * 7 + _T4_REPEATSHIFT
    ratchetshift = -1 * 7 + _T4_RATCHETSHIFT

# Sequencer for velocity
[motoquencer]
    select = _GLOBAL_FADER_SELECTION
    selectat = 4
    page = _PAGE
    copy = _T4_COPY_BUTTON * _NO_CONTROL
    paste = _T4_COPY_BUTTON * _CONTROL
    clear = _T4_CLEAR
    loadpreset = _T4_LOAD_PRESET
    savepreset = _T4_SAVE_PRESET
    cv = _T4_VELOCITY
    cvrange = 1
    defaultcv = 0.5
    quantize = 0
    linktonext = 1

# Copy velocity to CV output
[copy]
    input = _T4_VELOCITY
    output = O8

# Sequencer for gate length and glide
[motoquencer]
    select = _GLOBAL_FADER_SELECTION
    selectat = 4
    page = _PAGE
    copy = _T4_COPY_BUTTON * _NO_CONTROL
    paste = _T4_COPY_BUTTON * _CONTROL
    clear = _T4_CLEAR
    loadpreset = _T4_LOAD_PRESET
    savepreset = _T4_SAVE_PRESET
    cv = _T4_GATELENGTH
    cvrange = 1
    defaultcv = 0.5
    defaultgate = 0
    quantize = 0
    buttoncolor = 0.5
    linktonext = 1
    gate = _T4_GLIDE

# Sequencer for activity level per step
[motoquencer]
    select = _GLOBAL_FADER_SELECTION
    selectat = 4
    clear = _T4_CLEAR
    loadpreset = _T4_LOAD_PRESET
    savepreset = _T4_SAVE_PRESET
    cv = _T4_STEP_ACTIVITY
    quantize = 0
    defaultcv = 7
    cvnotches = 8

# Silence the gate if the step does not reach the required activity
[compare]
    input = _T4_STEP_ACTIVITY
    compare = _T4_ACTIVITY
    ifless = 0
    else = _T4_SEQUENCER_GATE
    output = _T4_ACT_GATE

# Switch gate to arpeggiator if active
[compare]
    input = _T4_ARP
    compare = 0
    ifequal = _T4_ACT_GATE
    else = _T4_ARP_CLOCK
    output = _T4_SWITCHED_GATE

# Logic for make the gate silent if the track is muted
[logic]
    input1 = -1 * _T4_MUTED + 1
    input2 = _T4_SWITCHED_GATE
    and = _T4_MUTED_GATE

# Select clock for arpeggiator of track 4
[switch]
    input1 = _CLOCK
    input2 = _T4_SEQUENCER_GATE
    input3 = _CLOCK_3_2
    input4 = _CLOCK_2_1
    input5 = _CLOCK_3_1
    input6 = _CLOCK_4_1
    input7 = _CLOCK_6_1
    input8 = _CLOCK_8_1
    output1 = _T4_ARP_CLOCK
    offset = _T4_ARP_CLOCKING

# Create taptempo for arp clock to be used for gate length
[switch]
    input1 = _CLOCK
    input2 = _T4_CLOCK
    input3 = _CLOCK_3_2
    input4 = _CLOCK_2_1
    input5 = _CLOCK_3_1
    input6 = _CLOCK_4_1
    input7 = _CLOCK_6_1
    input8 = _CLOCK_8_1
    output1 = _T4_ARP_TAPTEMPO
    offset = _T4_ARP_CLOCKING * _T4_ARP

# Virtual pot for base pitch of arpeggiator
[pot]
    select = _T4_ARP * 100 + _SELECTED_TRACK
    selectat = 104
    pot = P1.2
    outputscale = 3V
    output = _T4_ARP_PITCH

# Virtual pot for pitch range of arpeggiator
[pot]
    select = _T4_ARP * 100 + _SELECTED_TRACK
    selectat = 104
    pot = P1.1
    outputscale = 4V
    output = _T4_ARP_RANGE

# Arpeggiator for track 4
[arpeggio]
    select1 = _T4_SEL_1
    select3 = _T4_SEL_2
    select5 = _T4_SEL_3
    select7 = _T4_SEL_4
    select9 = _T4_SEL_5
    select11 = _T4_SEL_6
    select13 = _T4_SEL_7
    selectfill1 = 0
    selectfill2 = 0
    selectfill3 = 0
    selectfill4 = 0
    selectfill5 = 0
    root = _ROOT
    degree = _DEGREE
    clock = _T4_ARP_CLOCK * _RUNNING
    reset = _RESET
    tuningmode = _TUNINGMODE
    direction = _T4_ARP_UPDOWN
    pingpong = _T4_ARP_PINGPONG
    butterfly = _T4_ARP_BUTTERFLY
    octaves = _T4_ARP_OCTAVING
    drop = _T4_ARP_DROP
    pattern = _T4_ARP_PATTERN
    autoreset = _T4_ARP_AUTORESET
    pitch = _T4_ARP_PITCH
    range = _T4_ARP_RANGE
    output = _T4_ARP_OUTPUT

# Switch pitch output between sequencer and arpeggiator
[compare]
    input = _T4_ARP
    compare = 0
    ifequal = _T4_PITCH
    else = _T4_ARP_OUTPUT + _T4_ARP_OCTAVE
    output = _T4_SWITCHED_PITCH

# Mix all sources of pitch together
[mixer]
    input1 = _T4_OCTAVE * 1V - 2V
    input2 = _T4_SWITCHED_PITCH
    input3 = _GLOBAL_OCTAVE_SWITCH * 1V - 2V
    input4 = _GLOBAL_TRANSPOSITION * 0.00833333 - 1V
    output = _T4_FINAL_PITCH

# Slew limiter for glides
[slew]
    input = _T4_FINAL_PITCH
    exponential = O7
    slew = _T4_GLIDE * _T4_GLIDE_LENGTH

# Provide steady tap tempo for arpeggiator's gate length
[compare]
    input = _T4_ARP
    compare = 0
    ifequal = _T4_CLOCK
    else = _T4_ARP_TAPTEMPO
    output = _T4_GATEREF

# Compute gate length for arpeggiator output
[gatetool]
    taptempo = _T4_GATEREF
    inputgate = _T4_MUTED_GATE * _RUNNING
    outputgate = _T4_FINAL_GATE
    gatelength = _T4_GATELENGTH * 0.9 + 0.05

# Copy the gate signal to the CV jack
[copy]
    input = _T4_FINAL_GATE
    output = G1.4

# -------------------------------------------------
# MIDI Output
# -------------------------------------------------

# MIDI output for track 1
[midiout]
    trs = 1
    velocity1 = _T1_VELOCITY * 0.9 + 0.1
    channel = 1
    pitch1 = _T1_FINAL_PITCH
    gate1 = _T1_FINAL_GATE
    activesensing = 0

# MIDI output for track 2
[midiout]
    trs = 2
    velocity1 = _T2_VELOCITY * 0.9 + 0.1
    channel = 2
    pitch1 = _T2_FINAL_PITCH
    gate1 = _T2_FINAL_GATE
    activesensing = 0

# MIDI output for track 3
[midiout]
    usb = 1
    velocity1 = _T3_VELOCITY * 0.9 + 0.1
    channel = 3
    pitch1 = _T3_FINAL_PITCH
    gate1 = _T3_FINAL_GATE
    activesensing = 0

# MIDI output for track 4
[midiout]
    usb = 1
    velocity1 = _T4_VELOCITY * 0.9 + 0.1
    channel = 4
    pitch1 = _T4_FINAL_PITCH
    gate1 = _T4_FINAL_GATE
    activesensing = 0
