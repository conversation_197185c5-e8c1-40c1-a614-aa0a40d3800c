# DROID Tools

A comprehensive command-line toolkit for DROID devices, featuring device management, configuration upload, circuit library, and validation tools.

## Features

### 🎛️ **Device Management**
- **Smart Device Detection**: Lists only active MIDI devices by default
- **Visual Status Indicators**: 🟢 for active devices, 🔴 for offline devices
- **Comprehensive Discovery**: `--all` flag shows all known devices including offline
- **Pattern Filtering**: Find devices by name pattern (e.g., `droid device list elektron`)

### 📤 **Configuration Upload**
- **DROID SysEx Protocol**: Native support for DROID configuration upload via MIDI
- **Dry Run Testing**: Validate configurations without sending to device
- **Verbose Output**: Detailed logging for troubleshooting
- **Empty File Support**: Wipe DROID configurations with empty uploads

### 🔌 **Circuit Library**
- **Built-in Circuit Database**: 100+ pre-defined DROID circuits
- **Search & Discovery**: Find circuits by name, category, or description
- **Circuit Documentation**: Detailed examples with parameters and usage
- **Template Generation**: Create starter configurations from circuit templates

### ✅ **Validation & Quality**
- **Configuration Validation**: Parse and validate DROID INI files
- **Error Reporting**: Detailed syntax and semantic error messages
- **Template System**: Generate valid configurations from templates
- **Comprehensive Testing**: 25+ unit tests ensuring reliability

## Installation

### Prerequisites

- **macOS** (required for CoreMIDI framework)
- **Go 1.19+** (for the main CLI application)
- **Clang** (for the C library backend)
- **Make** (for build automation)

### Quick Start

```bash
# Clone the repository
git clone https://github.com/t6d/droid-tools.git
cd droid-tools

# Build the application
make

# Install to ~/bin (optional)
make install

# Run tests
make test
```

### Build Commands

```bash
make                 # Build the droid CLI tool
make test            # Run all tests (unit + smoke)
make test-unit       # Run unit tests only
make test-smoke      # Run smoke tests only
make fmt             # Format Go code
make install         # Install to ~/bin
make clean           # Clean build artifacts
make help            # Show all available targets
```

## Usage

### Device Management

```bash
# List active MIDI devices (default)
droid device list

# List all devices including offline ones
droid device list --all

# Find specific devices by pattern
droid device list droid
droid device list elektron --all

# JSON output for scripting
droid device list --json
```

### Configuration Upload

```bash
# Upload configuration to DROID device
droid upload config.ini

# Upload with verbose output
droid upload --verbose config.ini

# Test upload without sending (dry run)
droid upload --dry-run config.ini

# Upload to specific device
droid upload --device "DROID X7" config.ini
```

### Circuit Library

```bash
# List all available circuits
droid circuit list

# Search for circuits
droid circuit search lfo
droid circuit search "MIDI controller"

# Show circuit details and examples
droid circuit show lfo
droid circuit show sequencer

# List circuits by category
droid circuit list --category Generators
```

### Configuration Management

```bash
# Validate a configuration file
droid validate config.ini

# Generate template configuration
droid config init basic
droid config init sequencer

# Show available templates
droid config templates
```

## Examples

### Quick Start Workflow

```bash
# 1. Check what devices are connected
droid device list
# 🟢 DROID X7 MIDI (I/O) (ID: 0)

# 2. Create a basic configuration
droid config init basic > my-config.ini

# 3. Validate the configuration
droid validate my-config.ini

# 4. Upload to DROID (dry run first)
droid upload --dry-run my-config.ini
droid upload my-config.ini
```

### Working with Circuits

```bash
# Find LFO circuits
droid circuit search lfo
# lfo - Low Frequency Oscillator with multiple waveforms
# lfo2 - Dual LFO with phase offset
# lfoclock - LFO synchronized to clock

# Get detailed circuit information
droid circuit show lfo
# Shows parameters, examples, and usage

# Create configuration with LFO
droid config init lfo > lfo-config.ini
```

### Device Management

```bash
# See all devices (including offline)
droid device list --all
# 🟢 DROID X7 MIDI (I/O) (ID: 0)
# 🔴 Elektron Digitone (I/O) (ID: 1)
# 🔴 Network (I/O) (ID: 2)

# Find specific manufacturer devices
droid device list elektron --all
# 🔴 Elektron Digitone (I/O) (ID: 1)
# 🔴 Elektron Syntakt (I/O) (ID: 3)
```

## Architecture

DROID Tools uses a hybrid architecture combining Go for the CLI interface with a C library for Core MIDI integration.

### Go Application (`cmd/droid/`)

- **`main.go`**: CLI entry point and command routing
- **`commands/`**: Cobra-based command implementations
  - `device.go`: Device listing and management
  - `upload.go`: Configuration upload functionality
  - `circuit.go`: Circuit library and search
  - `validate.go`: Configuration validation
  - `config.go`: Template and configuration management
  - `helpers.go`: Shared CLI formatting utilities

### Go Packages (`pkg/`)

- **`droid/`**: Core DROID functionality and C library bindings
- **`circuits/`**: Circuit registry and embedded circuit database
- **`errors/`**: Structured error types and error handling

### C Library (`lib/`)

- **`libdroid.c/h`**: Main library interface and device enumeration
- **`file_utils.c/h`**: File I/O operations with error handling
- **`sysex.c/h`**: SysEx message encoding and DROID protocol
- **`midi.c/h`**: Core MIDI client management and communication

### Key Design Principles

1. **Hybrid Architecture**: Go for CLI/UX, C for Core MIDI performance
2. **Comprehensive Testing**: 25+ unit tests covering all major functionality
3. **Structured Errors**: Type-safe error handling with proper error wrapping
4. **Modular Design**: Clean separation between CLI, business logic, and MIDI
5. **User Experience**: Emoji indicators, intuitive commands, helpful output
6. **Code Quality**: Automatic formatting, comprehensive tests, no dead code

## DROID SysEx Protocol

DROID Tools implements the DROID-specific SysEx protocol for configuration upload:

### Protocol Details

1. **Header**: `F0 00 66 66 50` (SysEx start + DROID manufacturer ID)
2. **Data Combination**: Header and configuration data are combined
3. **Padding Logic**: Space bytes (0x20) inserted after every 255 bytes including header
4. **Footer**: `F7` (SysEx end byte)

### Important Features

- **Empty File Support**: Upload empty configurations to wipe DROID memory
- **Exact Compatibility**: Padding logic matches original DROID implementation
- **Error Detection**: Comprehensive validation before transmission

## Development

### Project Structure

```
droid-tools/
├── cmd/droid/           # CLI application
├── pkg/                 # Go packages
│   ├── droid/          # Core functionality
│   ├── circuits/       # Circuit library
│   └── errors/         # Error types
├── lib/                # C library
├── docs/               # Documentation
└── Makefile           # Build automation
```

### Adding New Features

1. **Go Code**: Add to appropriate package in `pkg/`
2. **CLI Commands**: Add to `cmd/droid/commands/`
3. **C Library**: Extend `lib/` for MIDI functionality
4. **Tests**: Add unit tests for all new functionality
5. **Documentation**: Update README and help text

### Testing

```bash
# Run all tests
make test

# Run specific test suites
make test-unit          # Unit tests only
make test-smoke         # Smoke tests only

# Test specific packages
go test ./pkg/droid -v
go test ./pkg/circuits -v
```

### Code Quality

```bash
# Format code (automatic on build)
make fmt

# Build with formatting
make droid

# Clean build
make clean && make
```

## Contributing

1. **Fork** the repository
2. **Create** a feature branch
3. **Add tests** for new functionality
4. **Run** `make test` to ensure all tests pass
5. **Submit** a pull request

## License

This project is provided as-is for educational and personal use.
