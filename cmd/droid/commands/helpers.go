package commands

import (
	"fmt"

	"github.com/t6d/droid-tools/pkg/droid"
	"github.com/t6d/droid-tools/pkg/logger"
)

// FormatDeviceStatus returns the appropriate emoji status indicator for a device
func FormatDeviceStatus(device droid.MIDIDevice) string {
	if device.IsActive {
		return "🟢" // Green circle for active devices
	}
	return "🔴" // Red circle for offline devices
}

// FormatDeviceDirection returns the direction indicator for a device
func FormatDeviceDirection(device droid.MIDIDevice) string {
	if device.IsInput && device.IsOutput {
		return " (I/O)"
	} else if device.IsInput {
		return " (Input)"
	} else if device.IsOutput {
		return " (Output)"
	}
	return ""
}

// FormatDeviceList formats a list of devices for display
func FormatDeviceList(devices []droid.MIDIDevice, deviceType string) {
	logger.WithFields(map[string]interface{}{
		"device_count": len(devices),
		"device_type":  deviceType,
	}).Debug("Formatting device list for display")

	if len(devices) == 0 {
		fmt.Printf("No %s found\n", deviceType)
		return
	}

	fmt.Printf("Available %s (%d):\n", deviceType, len(devices))
	for _, device := range devices {
		statusIcon := FormatDeviceStatus(device)
		direction := FormatDeviceDirection(device)
		fmt.Printf("  %s %s%s (ID: %d)\n", statusIcon, device.Name, direction, device.ID)
	}
}

// GetDeviceTypeDescription returns a human-readable description of device filters
func GetDeviceTypeDescription(inputOnly, outputOnly bool) string {
	if inputOnly {
		return "MIDI input devices"
	} else if outputOnly {
		return "MIDI output devices"
	}
	return "MIDI devices"
}
