package commands

import (
	"fmt"
	"strings"

	"github.com/spf13/cobra"
	"github.com/t6d/droid-tools/pkg/circuits"
)

var (
	flatList  bool
	showCount bool
	category  string
	useColor  bool
)

// circuitCmd represents the circuit command
var circuitCmd = &cobra.Command{
	Use:   "circuit",
	Short: "Circuit documentation and search tools",
	Long: `Circuit documentation and search tools for DROID circuits.

The circuit command provides access to comprehensive documentation for all
DROID circuits, including input/output parameters, examples, and search
functionality.

Examples:
  droid circuit show lfo
  droid circuit list
  droid circuit list Generators
  droid circuit search random`,
}

// showCmd represents the circuit show command
var showCmd = &cobra.Command{
	Use:   "show <circuit-name>",
	Short: "Show detailed documentation for a circuit",
	Long: `Show detailed documentation for a specific DROID circuit.

This displays comprehensive information including:
- Circuit description and category
- Input parameters with types and defaults
- Output parameters with types
- Usage examples with descriptions

Examples:
  droid circuit show lfo
  droid circuit show contour
  droid circuit show algoquencer
  droid circuit show lfo --color       # Beautiful colorized output`,
	Args: cobra.ExactArgs(1),
	RunE: func(cmd *cobra.Command, args []string) error {
		circuitName := strings.ToLower(args[0])

		// Load circuit registry
		registry, err := circuits.GetDefaultRegistry()
		if err != nil {
			return fmt.Errorf("failed to load circuit definitions: %v", err)
		}

		// Get circuit
		circuit, exists := registry.GetCircuit(circuitName)
		if !exists {
			return fmt.Errorf("circuit '%s' not found\n\nUse 'droid circuit list' to see available circuits.\nUse 'droid circuit search <term>' to search for circuits.", circuitName)
		}

		// Display circuit documentation
		if useColor {
			fmt.Print(circuit.FormatColorizedDocumentation())
		} else {
			fmt.Print(circuit.FormatDocumentation())
		}
		return nil
	},
}

// listCmd represents the circuit list command
var listCmd = &cobra.Command{
	Use:   "list [category]",
	Short: "List circuits, optionally filtered by category",
	Long: `List DROID circuits, optionally filtered by category.

Without arguments, lists all circuits organized by category.
With a category argument, lists only circuits in that category.

Examples:
  droid circuit list                    # All circuits by category
  droid circuit list Generators        # Only Generator circuits
  droid circuit list --flat            # Flat list without categories
  droid circuit list --count           # Show counts only`,
	Args: cobra.MaximumNArgs(1),
	RunE: func(cmd *cobra.Command, args []string) error {
		registry, err := circuits.GetDefaultRegistry()
		if err != nil {
			return fmt.Errorf("failed to load circuit definitions: %v", err)
		}

		categories := registry.ListCircuitsByCategory()

		if showCount {
			// Show counts only
			total := 0
			for category, circuits := range categories {
				count := len(circuits)
				total += count
				fmt.Printf("%-15s %d circuits\n", category+":", count)
			}
			fmt.Printf("%-15s %d circuits\n", "Total:", total)
			return nil
		}

		if len(args) > 0 {
			// List circuits by specific category
			targetCategory := args[0]
			if circuits, exists := categories[targetCategory]; exists {
				if flatList {
					for _, name := range circuits {
						fmt.Printf("%s\n", name)
					}
				} else {
					fmt.Printf("%s:\n", targetCategory)
					for _, name := range circuits {
						circuit, _ := registry.GetCircuit(name)
						fmt.Printf("  %-15s - %s\n", name, circuit.Description)
					}
				}
			} else {
				fmt.Printf("Category '%s' not found.\n\nAvailable categories:\n", targetCategory)
				for cat := range categories {
					fmt.Printf("  %s\n", cat)
				}
				return fmt.Errorf("invalid category")
			}
		} else {
			// List all circuits by category
			if flatList {
				for _, circuits := range categories {
					for _, name := range circuits {
						fmt.Printf("%s\n", name)
					}
				}
			} else {
				for category, circuits := range categories {
					fmt.Printf("%s:\n", category)
					for _, name := range circuits {
						circuit, _ := registry.GetCircuit(name)
						fmt.Printf("  %-15s - %s\n", name, circuit.Description)
					}
					fmt.Println()
				}
			}
		}
		return nil
	},
}

// searchCmd represents the circuit search command
var searchCmd = &cobra.Command{
	Use:   "search <term>",
	Short: "Search circuits by name or description",
	Long: `Search DROID circuits by name or description.

The search is case-insensitive and matches both circuit names and descriptions.
Use quotes for multi-word search terms.

Examples:
  droid circuit search random
  droid circuit search "envelope generator"
  droid circuit search MIDI`,
	Args: cobra.ExactArgs(1),
	RunE: func(cmd *cobra.Command, args []string) error {
		registry, err := circuits.GetDefaultRegistry()
		if err != nil {
			return fmt.Errorf("failed to load circuit definitions: %v", err)
		}

		searchTerm := args[0]
		results := registry.SearchCircuits(searchTerm)

		if len(results) == 0 {
			fmt.Printf("No circuits found matching '%s'\n", searchTerm)
			fmt.Printf("\nUse 'droid circuit list' to see all available circuits.\n")
		} else {
			fmt.Printf("Circuits matching '%s':\n", searchTerm)
			for _, circuit := range results {
				fmt.Printf("  %-15s - %s\n", circuit.Name, circuit.Description)
			}
		}
		return nil
	},
}

func init() {
	// Add subcommands
	circuitCmd.AddCommand(showCmd)
	circuitCmd.AddCommand(listCmd)
	circuitCmd.AddCommand(searchCmd)

	// Flags for show command
	showCmd.Flags().BoolVar(&useColor, "color", false, "show colorized output")

	// Flags for list command
	listCmd.Flags().BoolVar(&flatList, "flat", false, "flat list without categories")
	listCmd.Flags().BoolVar(&showCount, "count", false, "show circuit counts only")
}
