package commands

import (
	"fmt"
	"os"
	"strings"

	"github.com/spf13/cobra"
	"github.com/t6d/droid-tools/pkg/logger"
)

const (
	appName    = "droid"
	appVersion = "2.0.0"
	appDesc    = "Comprehensive toolkit for DROID modular synthesizer"
)

var (
	verbose  bool
	quiet    bool
	logLevel string
)

// rootCmd represents the base command when called without any subcommands
var rootCmd = &cobra.Command{
	Use:   appName,
	Short: appDesc,
	Long: fmt.Sprintf(`%s - %s

DROID Tools provides comprehensive functionality for working with DROID modular
synthesizer configurations, including uploading configurations, circuit
documentation, validation, and device management.

Examples:
  %s upload my-config.ini
  %s circuit show lfo
  %s circuit list Generators
  %s device list`, appName, appDesc, appName, appName, appName, appName),
	Version: appVersion,
	PersistentPreRunE: func(cmd *cobra.Command, args []string) error {
		// Initialize logger with user-specified settings
		effectiveLogLevel := logLevel

		// Handle legacy verbose/quiet flags for backward compatibility
		if verbose && logLevel == "info" {
			effectiveLogLevel = "debug"
		}

		if err := logger.InitLogger(effectiveLogLevel, quiet); err != nil {
			return fmt.Errorf("failed to initialize logger: %v", err)
		}

		logger.Debugf("Initialized %s v%s", appName, appVersion)
		logger.Debugf("Log level: %s, Quiet: %v", effectiveLogLevel, quiet)

		return nil
	},
}

// Execute adds all child commands to the root command and sets flags appropriately.
// This is called by main.main(). It only needs to happen once to the rootCmd.
func Execute() error {
	return rootCmd.Execute()
}

func init() {
	// Global flags
	rootCmd.PersistentFlags().BoolVarP(&verbose, "verbose", "v", false, "enable verbose output (equivalent to --log-level debug)")
	rootCmd.PersistentFlags().BoolVarP(&quiet, "quiet", "q", false, "suppress non-essential output")
	rootCmd.PersistentFlags().StringVar(&logLevel, "log-level", "info",
		fmt.Sprintf("set log level (%s)", strings.Join(logger.ValidLogLevels(), ", ")))

	// Add subcommands
	rootCmd.AddCommand(uploadCmd)
	rootCmd.AddCommand(circuitCmd)
	rootCmd.AddCommand(deviceCmd)
	rootCmd.AddCommand(validateCmd)
	rootCmd.AddCommand(configCmd)

	// Custom help command
	rootCmd.SetHelpCommand(&cobra.Command{
		Use:   "help [command]",
		Short: "Help about any command",
		Long: `Help provides help for any command in the application.
Simply type ` + appName + ` help [path to command] for full details.`,
		Run: func(cmd *cobra.Command, args []string) {
			if len(args) == 0 {
				rootCmd.Help()
				return
			}

			// Find the command
			targetCmd, _, err := rootCmd.Find(args)
			if err != nil {
				fmt.Fprintf(os.Stderr, "Unknown command: %s\n", args[0])
				os.Exit(1)
			}
			targetCmd.Help()
		},
	})
}

// Helper functions for subcommands
func IsVerbose() bool {
	return verbose
}

func IsQuiet() bool {
	return quiet
}

func GetAppInfo() (string, string, string) {
	return appName, appVersion, appDesc
}
