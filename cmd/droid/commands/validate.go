package commands

import (
	"encoding/json"
	"fmt"
	"os"

	"github.com/spf13/cobra"
	"github.com/t6d/droid-tools/pkg/droid"
	"github.com/t6d/droid-tools/pkg/logger"
)

var (
	strictValidation bool
	outputFormat     string
)

// validateCmd represents the validate command
var validateCmd = &cobra.Command{
	Use:   "validate [flags] <config-file>",
	Short: "Validate configuration files",
	Long: `Validate DROID configuration files for syntax and semantic correctness.

The validate command checks configuration files for:
- Syntax errors (malformed INI format)
- Unknown circuits or parameters
- Type mismatches
- Missing required parameters
- Invalid parameter values

Examples:
  droid validate my-config.ini
  droid validate --strict my-config.ini
  droid validate --format json my-config.ini`,
	Args: cobra.ExactArgs(1),
	RunE: func(cmd *cobra.Command, args []string) error {
		filename := args[0]

		logger.WithFields(map[string]interface{}{
			"file":   filename,
			"strict": strictValidation,
			"format": outputFormat,
		}).Info("Starting configuration validation")

		// Create validation options
		opts := droid.ValidationOptions{
			Strict:  strictValidation,
			Verbose: IsVerbose(),
		}

		result, err := droid.ValidateFileWithOptions(filename, opts)
		if err != nil {
			return fmt.Errorf("validation failed: %v", err)
		}

		// Output results based on format
		switch outputFormat {
		case "json":
			output, err := json.MarshalIndent(result, "", "  ")
			if err != nil {
				return fmt.Errorf("failed to marshal JSON: %v", err)
			}
			fmt.Println(string(output))
		default:
			// Human-readable format
			if result.IsValid {
				logger.WithFields(map[string]interface{}{
					"circuits":   result.CircuitCount,
					"parameters": result.ParameterCount,
				}).Info("Configuration file is valid")
			} else {
				logger.Error("Configuration file has errors:")
				for _, err := range result.Errors {
					logger.Errorf("  - %s", err)
				}
				if len(result.Warnings) > 0 {
					logger.Warn("Warnings found:")
					for _, warning := range result.Warnings {
						logger.Warnf("  - %s", warning)
					}
				}
				os.Exit(1)
			}
		}

		return nil
	},
}

func init() {
	validateCmd.Flags().BoolVar(&strictValidation, "strict", false, "enable strict validation mode")
	validateCmd.Flags().StringVar(&outputFormat, "format", "text", "output format (text, json)")
}
