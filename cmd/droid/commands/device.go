package commands

import (
	"encoding/json"
	"fmt"
	"strings"

	"github.com/spf13/cobra"
	"github.com/t6d/droid-tools/pkg/droid"
	"github.com/t6d/droid-tools/pkg/logger"
)

var (
	jsonOutput bool
	inputOnly  bool
	outputOnly bool
	activeOnly bool
	showAll    bool
)

// deviceCmd represents the device command
var deviceCmd = &cobra.Command{
	Use:   "device",
	Short: "MIDI device management",
	Long: `MIDI device management for DROID devices.

The device command provides functionality to list and search available
MIDI devices with flexible filtering options.

Examples:
  droid device list
  droid device list "DROID"
  droid device list --input --all`,
}

// deviceListCmd represents the device list command
var deviceListCmd = &cobra.Command{
	Use:   "list [pattern]",
	Short: "List available MIDI devices",
	Long: `List available MIDI devices on the system with optional pattern filtering.

This command shows currently active MIDI devices by default. Use --all to
include known devices that may be offline.

Examples:
  droid device list                     # Active devices only (default)
  droid device list "DROID"            # Active devices matching "DROID"
  droid device list --all              # All devices (active + offline)
  droid device list --input            # Input devices only
  droid device list --json             # JSON format`,
	Args: cobra.MaximumNArgs(1),
	RunE: func(cmd *cobra.Command, args []string) error {
		var pattern string
		if len(args) > 0 {
			pattern = args[0]
		}
		devices, err := droid.ListDevicesWithOptions(droid.DeviceListOptions{
			ActiveOnly: activeOnly,
			ShowAll:    showAll,
		})
		if err != nil {
			return fmt.Errorf("failed to list devices: %v", err)
		}

		// Apply filters
		var filtered []droid.MIDIDevice
		for _, device := range devices {
			// Pattern filter
			if pattern != "" && !strings.Contains(strings.ToLower(device.Name), strings.ToLower(pattern)) {
				continue
			}

			// Direction filters
			if inputOnly && !device.IsInput {
				continue
			}
			if outputOnly && !device.IsOutput {
				continue
			}

			filtered = append(filtered, device)
		}
		devices = filtered

		logger.WithFields(map[string]interface{}{
			"total_devices": len(devices),
			"pattern":       pattern,
			"input_only":    inputOnly,
			"output_only":   outputOnly,
			"active_only":   activeOnly,
		}).Debug("Device listing completed")

		if jsonOutput {
			output, err := json.MarshalIndent(devices, "", "  ")
			if err != nil {
				return fmt.Errorf("failed to marshal JSON: %v", err)
			}
			fmt.Println(string(output))
		} else {
			deviceType := GetDeviceTypeDescription(inputOnly, outputOnly)
			FormatDeviceList(devices, deviceType)
		}

		return nil
	},
}

func init() {
	// Add subcommands
	deviceCmd.AddCommand(deviceListCmd)

	// Flags for list command
	deviceListCmd.Flags().BoolVar(&jsonOutput, "json", false, "output in JSON format")
	deviceListCmd.Flags().BoolVar(&inputOnly, "input", false, "show input devices only")
	deviceListCmd.Flags().BoolVar(&outputOnly, "output", false, "show output devices only")
	deviceListCmd.Flags().BoolVar(&activeOnly, "active-only", false, "show only currently connected devices (default behavior)")
	deviceListCmd.Flags().BoolVar(&showAll, "all", false, "show all devices including offline/disconnected ones")
}
