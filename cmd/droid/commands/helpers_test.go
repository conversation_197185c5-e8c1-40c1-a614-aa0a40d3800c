package commands

import (
	"testing"

	"github.com/t6d/droid-tools/pkg/droid"
)

func TestFormatDeviceStatus(t *testing.T) {
	// Test active device
	activeDevice := droid.MIDIDevice{
		Name:     "Test Device",
		ID:       0,
		IsActive: true,
	}
	status := FormatDeviceStatus(activeDevice)
	if status != "🟢" {
		t.<PERSON><PERSON>("Expected green circle for active device, got %s", status)
	}

	// Test inactive device
	inactiveDevice := droid.MIDIDevice{
		Name:     "Test Device",
		ID:       0,
		IsActive: false,
	}
	status = FormatDeviceStatus(inactiveDevice)
	if status != "🔴" {
		t.<PERSON><PERSON><PERSON>("Expected red circle for inactive device, got %s", status)
	}
}

func TestFormatDeviceDirection(t *testing.T) {
	// Test I/O device
	ioDevice := droid.MIDIDevice{
		Name:     "Test Device",
		IsInput:  true,
		IsOutput: true,
	}
	direction := FormatDeviceDirection(ioDevice)
	if direction != " (I/O)" {
		t.<PERSON><PERSON><PERSON>("Expected ' (I/O)' for I/O device, got '%s'", direction)
	}

	// Test input only device
	inputDevice := droid.MIDIDevice{
		Name:     "Test Device",
		IsInput:  true,
		IsOutput: false,
	}
	direction = FormatDeviceDirection(inputDevice)
	if direction != " (Input)" {
		t.Errorf("Expected ' (Input)' for input device, got '%s'", direction)
	}

	// Test output only device
	outputDevice := droid.MIDIDevice{
		Name:     "Test Device",
		IsInput:  false,
		IsOutput: true,
	}
	direction = FormatDeviceDirection(outputDevice)
	if direction != " (Output)" {
		t.Errorf("Expected ' (Output)' for output device, got '%s'", direction)
	}

	// Test device with no direction
	noDirectionDevice := droid.MIDIDevice{
		Name:     "Test Device",
		IsInput:  false,
		IsOutput: false,
	}
	direction = FormatDeviceDirection(noDirectionDevice)
	if direction != "" {
		t.Errorf("Expected empty string for device with no direction, got '%s'", direction)
	}
}

func TestGetDeviceTypeDescription(t *testing.T) {
	// Test input only
	desc := GetDeviceTypeDescription(true, false)
	if desc != "MIDI input devices" {
		t.Errorf("Expected 'MIDI input devices', got '%s'", desc)
	}

	// Test output only
	desc = GetDeviceTypeDescription(false, true)
	if desc != "MIDI output devices" {
		t.Errorf("Expected 'MIDI output devices', got '%s'", desc)
	}

	// Test both (default)
	desc = GetDeviceTypeDescription(false, false)
	if desc != "MIDI devices" {
		t.Errorf("Expected 'MIDI devices', got '%s'", desc)
	}

	// Test both true (should be input only)
	desc = GetDeviceTypeDescription(true, true)
	if desc != "MIDI input devices" {
		t.Errorf("Expected 'MIDI input devices' when both are true, got '%s'", desc)
	}
}
