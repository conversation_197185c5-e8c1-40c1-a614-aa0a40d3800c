package commands

import (
	"fmt"

	"github.com/spf13/cobra"
	"github.com/t6d/droid-tools/pkg/droid"
	"github.com/t6d/droid-tools/pkg/logger"
)

var (
	templateType string
	overwrite    bool
)

// configCmd represents the config command
var configCmd = &cobra.Command{
	Use:   "config",
	Short: "Configuration file utilities",
	Long: `Configuration file utilities for DROID configurations.

The config command provides tools for creating, formatting, and managing
DROID configuration files.

Examples:
  droid config init my-config.ini
  droid config format my-config.ini
  droid config template drums > drums.ini`,
}

// configInitCmd represents the config init command
var configInitCmd = &cobra.Command{
	Use:   "init [flags] <config-file>",
	Short: "Initialize a new configuration file",
	Long: `Initialize a new DROID configuration file with basic structure.

This creates a new configuration file with common sections and helpful
comments to get you started.

Examples:
  droid config init my-config.ini
  droid config init --template basic my-config.ini
  droid config init --overwrite existing-config.ini`,
	Args: cobra.ExactArgs(1),
	RunE: func(cmd *cobra.Command, args []string) error {
		filename := args[0]

		logger.WithFields(map[string]interface{}{
			"file":      filename,
			"template":  templateType,
			"overwrite": overwrite,
		}).Info("Creating configuration file")

		opts := droid.ConfigInitOptions{
			Template:  templateType,
			Overwrite: overwrite,
			Verbose:   IsVerbose(),
		}

		err := droid.InitConfigFile(filename, opts)
		if err != nil {
			return fmt.Errorf("failed to initialize config: %v", err)
		}

		logger.WithField("file", filename).Info("Configuration file created successfully")

		return nil
	},
}

// configFormatCmd represents the config format command
var configFormatCmd = &cobra.Command{
	Use:   "format <config-file>",
	Short: "Format a configuration file",
	Long: `Format a DROID configuration file for consistent style.

This command reformats the configuration file with consistent indentation,
spacing, and organization while preserving all functionality.

Examples:
  droid config format my-config.ini
  droid config format --backup my-config.ini`,
	Args: cobra.ExactArgs(1),
	RunE: func(cmd *cobra.Command, args []string) error {
		filename := args[0]

		logger.WithField("file", filename).Debug("Formatting configuration file")

		opts := droid.FormatOptions{
			CreateBackup: overwrite, // Reuse overwrite flag as backup flag
			Verbose:      IsVerbose(),
		}

		err := droid.FormatConfigFile(filename, opts)
		if err != nil {
			return fmt.Errorf("failed to format config: %v", err)
		}

		logger.WithField("file", filename).Info("Configuration file formatted successfully")

		return nil
	},
}

// configTemplateCmd represents the config template command
var configTemplateCmd = &cobra.Command{
	Use:   "template <template-name>",
	Short: "Generate configuration templates",
	Long: `Generate DROID configuration templates for common use cases.

Available templates:
  basic     - Basic configuration with common circuits
  drums     - Drum machine configuration
  sequencer - Sequencer-focused configuration
  midi      - MIDI-focused configuration

Examples:
  droid config template basic
  droid config template drums > drums.ini
  droid config template sequencer > seq.ini`,
	Args: cobra.ExactArgs(1),
	RunE: func(cmd *cobra.Command, args []string) error {
		templateName := args[0]

		template, err := droid.GetConfigTemplate(templateName)
		if err != nil {
			return fmt.Errorf("failed to get template: %v", err)
		}

		fmt.Print(template)
		return nil
	},
}

func init() {
	// Add subcommands
	configCmd.AddCommand(configInitCmd)
	configCmd.AddCommand(configFormatCmd)
	configCmd.AddCommand(configTemplateCmd)

	// Flags for init command
	configInitCmd.Flags().StringVar(&templateType, "template", "basic", "template type (basic, drums, sequencer, midi)")
	configInitCmd.Flags().BoolVar(&overwrite, "overwrite", false, "overwrite existing file")

	// Flags for format command
	configFormatCmd.Flags().BoolVar(&overwrite, "backup", false, "create backup before formatting")
}
