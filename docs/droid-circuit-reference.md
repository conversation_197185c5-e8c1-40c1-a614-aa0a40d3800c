# DROID blue-6 Circuit Reference

---

## adc – AD Converter with 12 bits

This circuit converts an input value into a binary representation of up to 12 bits.

**Example:**
```ini
[adc]
input = I1
minimum = 0.1 # 1V
maximum = 0.5 # 4V
bit1 = O1
bit2 = O2
bit3 = O3
```

**Inputs**

| Input        | Default | Description |
|--------------|---------|-------------|
| input (i)    | 0.0     | Input signal to convert to binary representation. |
| minimum (m)  | 0.0     | The lowest assumed input value. This value and all lower values will be converted to the bit sequence 000000000000. |
| maximum (x)  | 1.0     | The highest assumed input value. This value and all higher values will be converted to the bit sequence 111111111111. |

**Outputs**

| Output         | Description |
|----------------|-------------|
| bit1 ... bit12 | The 12 bit outputs. bit1 is the MSB – the most significant bit. The LSB (least significant bit) is the highest output that you actually patch. If you do not need the full resolution of 12 bits, simply just use the first couple of outputs. |

---

## algoquencer – Algorithmic sequencer

A versatile performance sequencer combining a trigger sequencer, Turing machine, and more.

**Features:**
-  Up to 16 step buttons
-  Change pattern length on the fly
-  Editable accents, ratchets, fills, deterministic & chaotic randomization, fractal sequencing

**Example:**
```ini
[algoquencer]
clock = G1
pitch = O1
length = 12
pattern = 5
```

**Inputs** (partial)

| Input         | Default | Description |
|---------------|---------|-------------|
| clock (c)     |         | Clock input. Mandatory. |
| reset (r)     |         | Reset input. |
| button1...16  |         | 1st ... 16th step button. |
| length (l)    | ☞       | Sets the length of the pattern. |
| pattern (pt)  | 0       | Selects a pattern of pseudo random values. |
| nextpattern (np) |      | Switches to next pattern. |
| prevpattern (pp) |      | Switches to previous pattern. |
| pitchlow (pl) | 0.0     | Lower voltage boundary for pitch output. |
| pitchhigh (ph)| 0.3     | Upper voltage boundary for pitch output. |
| activity (a)  | 0.5     | Controls how busy the sequencer is playing. |
| variation (v) | 0.0     | Controls how strictly Algoquencer will stick to the programmed pattern. |
| ...           | ...     | ... |

**Outputs** (partial)

| Output           | Description |
|------------------|-------------|
| trigger (t)      | Trigger output. Patch to drum/synth voice trigger input. |
| gate (g)         | Variable length gate output. |
| pitch (p)        | Outputs the (pseudo-)random voltage at each step. |
| accent (ac)      | Accent output for beats with accent. |
| led1...led16     | 1st ... 16th LEDs of the step buttons. |
| barled1...barled4| LEDs for current bar in the sequence. |
| rollvelocity (rv)| Velocity output if rolls are enabled. |
| startofbar (sb)  | Trigger at start of every bar. |
| ...              | ... |

---

## arpeggio – Arpeggiator – pattern based melody generator

Creates melodic patterns based on rules and configuration settings.

**Example:**
```ini
[arpeggio]
clock = I1
output = O1
root = 2
degree = 7
pitch = 2/120
range = 1V
pattern = 1
```

**Inputs** (partial)

| Input        | Default | Description |
|--------------|---------|-------------|
| clock (c)    |         | Each trigger advances the arpeggio. |
| reset (r)    |         | Resets to the first step. |
| pattern (pt) | 0       | Selects one of a list of arpeggio patterns. |
| direction (d)| 0       | Sets the general direction (up/down). |
| drop (dr)    | 0       | Selects a scheme of skipping some notes. |
| clock (c)    |         | Clock input. |
| root (ro)    | 0       | Set the root note. |
| degree (dg)  | 0       | Set the musical scale. |
| select1...select13 | ☞ | Gate inputs for selecting intervals of the scale. |
| ...          | ...     | ... |

**Outputs**

| Output  | Description |
|---------|-------------|
| output (o) | Pitch CV for the current arpeggio note. |

---

## bernoulli – Random gate distributor

Implements a “bernoulli gate”. For each gate or trigger received at input, a random decision is made whether to forward that gate to output1 or output2.

**Example:**
```ini
[bernoulli]
input = G1
distribution = P1.1
output1 = G2
output2 = G4
```

**Inputs**

| Input         | Default | Description |
|---------------|---------|-------------|
| input (i)     | 0       | Send gate or trigger signals here. |
| distribution  | 0.5     | Controls the probability of a gate to be forwarded to output1. |

**Outputs**

| Output    | Description |
|-----------|-------------|
| output1   | Gates forwarded here if random favours output 1. |
| output2   | Gates forwarded here if random favours output 2. |

---

## burst – Generate burst of pulses

Produces – when triggered – a number of pulses.

**Example:**
```ini
[burst]
trigger = I1
hz = 10
count = 5
output = O1
```

**Inputs**

| Input      | Default | Description |
|------------|---------|-------------|
| rate (ra)  | 0.0     | Frequency control. |
| taptempo   |         | Reference clock. |
| hz         | 1.0     | Frequency in Hz. |
| trigger    |         | Start burst. |
| count      | 1       | Number of triggers to send in one burst. |
| skip       | 0       | Number of time slots to wait before starting. |

**Outputs**

| Output  | Description |
|---------|-------------|
| output  | Triggers are output here. |

---

## button – Does all sorts of useful things with buttons

Utility circuit for working with controller buttons. Can implement toggle buttons, detect long presses, double clicks, and more.

**Example:**
```ini
[button]
button = B1.4
led = L1.4
[cvlooper]
loop = L1.4
```

**Inputs** (partial)

| Input         | Default | Description |
|---------------|---------|-------------|
| button (b)    |         | The actual push button. |
| onvalue (ov)  | 1.0     | Value sent to output when on. |
| offvalue (fv) | 0.0     | Value sent to output when off. |
| value1...4    | 0,1,2,3 | Values for up to 4 states. |
| states (st)   | 2       | Number of states (toggle, momentary, etc). |
| ...           | ...     | ... |

**Outputs** (partial)

| Output     | Description |
|------------|-------------|
| led (l)    | LED output for the button state. |
| output (o) | Current button state. |
| inverted   | Inverted output. |
| longpress  | Goes high on long press. |
| shortpress | Trigger on short press. |

---

## buttongroup – Connected buttons

Combines a number of push buttons into a group that behave as a unit (like radio buttons).

**Example:**
```ini
[buttongroup]
button1 = B2.1
button2 = B2.2
button3 = B2.3
button4 = B2.4
led1 = L2.1
led2 = L2.2
led3 = L2.3
led4 = L2.4
value1 = 0V
value2 = 1V
value3 = 2V
value4 = -1V
output = O1
```

**Inputs** (partial)

| Input         | Default | Description |
|---------------|---------|-------------|
| minactive (ma)| 1       | Minimum number of active buttons. |
| maxactive (xa)| 1       | Maximum number of active buttons. |
| button1...32  |         | 1st ... 32nd button. |
| value1...32   |         | Value sent to the output when the button is active. |
| startbutton   | 1       | Button selected at start. |
| ...           | ...     | ... |

**Outputs** (partial)

| Output         | Description |
|----------------|-------------|
| led1...led32   | LED outputs for each button. |
| output (o)     | Sum of values of all active buttons. |
| buttonpress    | Trigger if any button is pressed. |
| longpress      | Trigger if any button is held for long. |

---

## calibrator – VCO Calibrator

Precisely compensate for decalibrated or imperfectly tracking VCOs by applying one specific adaptation value per octave.

**Example:**
```ini
[calibrator]
input = I1
output = O1
nudgeup = B1.1
nudgedown = B1.3
ledup = L1.1
leddown = L1.3
```

**Inputs**

| Input        | Default | Description |
|--------------|---------|-------------|
| input (i)    | 0V      | Patch your V/Oct pitch input here. |
| nudgeup (nu) |         | Trigger to nudge tuning up. |
| nudgedown (nd)|        | Trigger to nudge tuning down. |
| clearhere (ch)|        | Trigger to reset correction of current note. |
| nudgeamount (na)|0.01  | Amount each button press detunes. |
| tune0...tune8| 0.0     | Explicit tuning per octave. |
| ...          | ...     | ... |

**Outputs**

| Output      | Description |
|-------------|-------------|
| output (o)  | Calibrated pitch (1V/Oct). |
| ledup (lu)  | Indicates upward adjustment. |
| leddown (ld)| Indicates downward adjustment. |
| correction (c)| Current pitch correction (in semitones). |

---

## case – Switch choosing from inputs via conditions

Selects one of several inputs and routes its signal to the output based on which of several conditions is true.

**Example:**
```ini
[case]
case1 = _INTERNAL_CLOCK_PRESENT
value1 = _INTERNAL_CLOCK
case2 = _EXTERNAL_CLOCK_PRESENT
value2 = _EXTERNAL_CLOCK
case3 = _MIDI_CLOCK_PRESENT
value3 = _MIDI_CLOCK
output = _CLOCK
```

**Inputs**

| Input         | Default | Description |
|---------------|---------|-------------|
| case1...16    |         | 1st ... 16th case input. |
| value1...16   |         | 1st ... 16th value input. |
| else (e)      | 0       | Fallback value if no case input is non-zero. |

**Outputs**

| Output     | Description |
|------------|-------------|
| output (o) | The selected value input. |

---

## chord – Chord generator

Creates the pitch information for up to four voices of a musical chord.

**Example:**
```ini
[chord]
output1 = O1
output2 = O2
output3 = O3
output4 = O4
root = 2
degree = 7
```

**Inputs** (partial)

| Input        | Default | Description |
|--------------|---------|-------------|
| pitch (p)    | 0V      | Minimum pitch of the lowest chord note. |
| spread (s)   | 0V      | Range between lowest and highest note. |
| inversion (iv)| 0      | Chord inversion. |
| root (ro)    | 0       | Set the root note. |
| degree (dg)  | 0       | Set the musical scale. |
| select1...select13 | ☞ | Gate inputs for selecting intervals. |
| ...          | ...     | ... |

**Outputs**

| Output         | Description |
|----------------|-------------|
| output1...4 (o)| 1st ... 4th pitch output. |

---

## clocktool – Clock divider / multiplier / shifter

Implements various clock modifications, such as division, multiplication, gate length, and time shift.

**Example:**
```ini
[clocktool]
clock = I1 # patch a clock here
output = O1
divide = 7
```

**Inputs**

| Input        | Default | Description |
|--------------|---------|-------------|
| clock (c)    |         | Patch a steady clock here. |
| reset (r)    |         | Reset internal counters. |
| divide (d)   | 1       | Number to divide the clock through. |
| multiply (m) | 1       | Number to multiply the clock with. |
| dutycycle (dc)| ☞      | Output duty cycle of the clock (0.0–1.0). |
| gatelength (gl)| ☞     | Sets length of each output pulse (overrides dutycycle). |
| delay (dl)   | 0.0     | Shifts the input clock beat in time (fraction of a cycle). |

**Outputs**

| Output      | Description |
|-------------|-------------|
| output (o)  | Modified clock. |
| inputpitch (ip) | Input clock's pitch as 1V/oct. |
| outputpitch (op)| Output clock's pitch as 1V/oct. |

---

## compare – Compare two values

Allows you to make a decision by comparing an input value against a reference value.

**Example:**
```ini
[compare]
input = P1.1
compare = 0.5
ifless = 1
output = O1
```

**Inputs**

| Input        | Default | Description |
|--------------|---------|-------------|
| input (i)    | 0.0     | Value to compare. |
| compare (c)  | 0.0     | Reference value. |
| ifgreater (g)|         | Output if input > compare. |
| ifequal (q)  |         | Output if input == compare (within precision). |
| ifless (l)   |         | Output if input < compare. |
| else (e)     | 0.0     | Output if no condition is met. |
| precision (pc)| 0.0    | Allowed range for equality. |

**Outputs**

| Output      | Description |
|-------------|-------------|
| output (o)  | Output based on comparison. |

---

## contour – Contour generator

Enhanced ADSR envelope generator with six phases: predelay, attack, hold, decay, sustain, release.

**Example:**
```ini
[contour]
gate = I1
output = O1
```

**Inputs** (partial)

| Input        | Default | Description |
|--------------|---------|-------------|
| gate (g)     | ☞       | Patch a gate signal to trigger the envelope. |
| trigger (t)  |         | Alternative to gate. |
| predelay (pd)| 0.0     | Delay before envelope starts. |
| attack (a)   | 0.0     | Length of attack phase. |
| hold (h)     | 0.0     | Time at max level after attack. |
| decay (d)    | 0.2     | Time of decay phase. |
| sustain (s)  | 0.5     | Sustain level. |
| release (r)  | 0.2     | Timing of release phase. |
| level (l)    | 1.0     | Maximum level/scaling. |
| ...          | ...     | ... |

**Outputs**

| Output         | Description |
|----------------|-------------|
| output (o)     | Main envelope output. |
| negated (n)    | Negative envelope output. |
| inverted (iv)  | Inverted output (positive). |
| endofattack (ea)| Trigger at end of attack. |
| ...            | ... |

---

## copy – Copy a signal, while applying attenuation and offset

Simple utility that copies a signal from input to output, optionally scaling and offsetting.

**Example:**
```ini
[copy]
input = I1 * 0.5
output = _INPUT_CV
```

**Inputs**

| Input        | Default | Description |
|--------------|---------|-------------|
| input (i)    | 0.0     | Signal to copy. |

**Outputs**

| Output      | Description |
|-------------|-------------|
| output (o)  | The resulting signal. |

---

## crossfader – Morph between 8 inputs

Creates a CV controlled mix of two out of up to eight inputs.

**Example:**
```ini
[crossfader]
input1 = I1
input2 = I2
fade = P1.1
output = O1
```

**Inputs**

| Input            | Default | Description |
|------------------|---------|-------------|
| input1...input8  | 0.0     | Signals to crossfade between. |
| fade (f)         | 0.5     | Decides which inputs are mixed and in what proportion. |

**Outputs**

| Output      | Description |
|-------------|-------------|
| output (o)  | Output of the mix. |

---

## cvlooper – Clocked CV looper

Clocked CV looper that also loops a gate, can do overlay and overdub.

**Example:**
```ini
[cvlooper]
cvin = I1
clock = I8
cvout = O1
length = 16
loopswitch = L1.1
```

**Inputs**

| Input         | Default | Description |
|---------------|---------|-------------|
| cvin (ci)     | 0.0     | Input CV to loop. |
| gatein (gi)   | 1       | Optional input gate. |
| clock (c)     |         | Mandatory input clock. |
| length (l)    | 16      | Length of the loop in clock ticks. |
| tapespeed (s) | 1.0     | Relative tape speed. |
| loopswitch (ls)| ☞      | While off, looper is in bypass mode. |
| overlay (ov)  | off     | Overlay mode. |
| overdub (od)  | off     | Overdub mode. |
| ...           | ...     | ... |

**Outputs**

| Output      | Description |
|-------------|-------------|
| cvout (co)  | Output of the looped CV. |
| gateout (go)| Output of the looped gate. |

---

## dac – DA Converter with 12 bits

Converts a binary representation of up to 12 bits into an output value in a given range.

**Example:**
```ini
[dac]
bit1 = I1
bit2 = I2
bit3 = I3
output = O1
```

**Inputs**

| Input         | Default | Description |
|---------------|---------|-------------|
| bit1...bit12  | ☞       | 12 bit input bits. |
| minimum (m)   | 0.0     | Lower bound of output range. |
| maximum (x)   | 1.0     | Upper bound of output range. |

**Outputs**

| Output      | Description |
|-------------|-------------|
| output (o)  | Output signal. |

---

## delay – A tape delay for CVs, gates and numbers

Delays the movement of CVs, gates, or integer numbers in time.

**Example:**
```ini
[delay]
cvin = I1
cvout = O1
delay = 0.5
```

**Inputs**

| Input         | Default | Description |
|---------------|---------|-------------|
| delay (dl)    | 1.0     | Amount of delay (seconds or clock ticks). |
| cvin (ci)     | 0.0     | Continuous input CV. |
| numberin (ni) |         | Discrete input number. |
| gatein1...8   |         | Input gates. |
| clock (c)     |         | Optional clock for tick-based delay. |
| ...           | ...     | ... |

**Outputs**

| Output         | Description |
|----------------|-------------|
| cvout (co)     | Output of the delayed CV. |
| numberout (no) | Output of the delayed number. |
| gateout1...8   | Output of the delayed gates. |
| overflow (ov)  | Indicates if memory was exceeded. |

---

## detune – Detune multiple voices in a most disharmonic way

Detune up to eight voices in a disharmonic way, as in the Sinfonion.

**Example:**
```ini
[detune]
detune = P1.1
input1 = _PITCH1
input2 = _PITCH2
output1 = O1
output2 = O2
```

**Inputs**

| Input         | Default | Description |
|---------------|---------|-------------|
| input1...8    |         | Pitch inputs. |
| detune (d)    | 0.0     | Amount of detuning. |
| tuningmode (tm)| 0      | While 1, outputs tuning pitch. |
| tuningpitch (tp)| 0V    | Pitch CV for tuning mode. |

**Outputs**

| Output         | Description |
|----------------|-------------|
| output1...8    | Detuned pitch outputs. |

---

## droid – General DROID controls

Access to general configuration settings for the system.

**Inputs** (partial)

| Input            | Default | Description |
|------------------|---------|-------------|
| ledbrightness (l)| 1.0     | Dims all 24 LEDs of master and G8. |
| maxslope1...8    | 0.25    | Threshold for voltage change smoothing. |
| lpfilter1...8    | 0.25    | Digital lowpass filter on output. |
| m4faderspeed     | 0       | Motor fader speed for M4. |
| m4notchpower     | 0       | Force feedback power for M4 notches. |
| calibrate        |         | Enter calibration procedure. |
| clear            |         | Sends clear to all circuits. |
| clearall         |         | Sends clearall to all circuits. |
| uislowdown       | 1       | UI slowdown toggle. |
| ...              | ...     | ... |

---

## encoderbank – Create bank of up to 8 virtual input knobs from E4 encoders

Control up to 8 encoders as a group, reducing the number of encoder circuits needed.

**Example:**
```ini
[encoderbank]
firstencoder = E2.1
output1 = O1
output2 = O2
output3 = O3
outputscale = 2V
```

**Inputs** (partial)

| Input           | Default | Description |
|-----------------|---------|-------------|
| firstencoder (e)| 1       | First encoder to use (E8.2, or just a number). |
| led1...led8 (l) |         | LED ring for each encoder. |
| startvalue (sv) | 0.0     | Initial value after clear or first start. |
| notch (no)      | 0.0     | Notch at center position. |
| outputscale (os)| 1.0     | Multiplies output value. |
| outputoffset (oo)| 0.0    | Adds offset after scaling. |
| mode (m)        | 1       | Range mode (normal, bipolar, infinity, etc.). |
| smooth (sm)     | 0.5     | Amount of smoothing. |
| discrete (d)    | 0       | Use as rotary switch if ≥2. |
| snapto (sn)     |         | Snap to position. |
| snapforce (sf)  | 0.5     | Speed of return to snapto. |
| sensivity (se)  | 1.0     | How far to turn for value change. |
| autozoom (a)    | 0.0     | Fine adjust when slow, coarse when fast. |
| color (co)      | ☞       | Color of pointer in LED ring. |
| negativecolor (nc)| ☞    | Color for negative range. |
| ledfill (lf)    | 1       | Fill LEDs or just a dot. |
| select (s)      | ☞       | Overlay with select. |
| ...             | ...     | ... |

**Outputs**

| Output             | Description |
|--------------------|-------------|
| output1...output8  | Current value for each encoder. |
| button1...button8  | State of encoder push button (if selected). |

---

## encoder – Provide access to a knob on the E4 controller

Access to one of the encoders of an E4 controller, allowing virtual value control.

**Example:**
```ini
[encoder]
encoder = E1.1
output = O1
```

**Inputs** (partial)

| Input         | Default | Description |
|---------------|---------|-------------|
| encoder (e)   | 1       | Which encoder to use (E8.2 or a number). |
| override (or) |         | Use as display only, ignore knob. |
| sharewithnext | 0       | If 1, shares output with next encoder circuit. |
| movementticks | 5       | Steps per trigger for movedup/moveddown. |
| led (l)       |         | Use LED ring as virtual LED. |
| startvalue (sv)| 0.0    | Initial value after clear/first start. |
| notch (no)    | 0.0     | Notch at center. |
| outputscale (os)| 1.0   | Multiplies output. |
| outputoffset (oo)| 0.0  | Adds offset after scaling. |
| mode (m)      | 1       | Range mode. |
| smooth (sm)   | 0.5     | Smoothing. |
| discrete (d)  | 0       | Rotary switch mode if ≥2. |
| snapto (sn)   |         | Snap to position. |
| snapforce (sf)| 0.5     | Snap back speed. |
| sensivity (se)| 1.0     | Sensitivity. |
| autozoom (a)  | 0.0     | Fine/coarse adjust. |
| color (co)    | ☞       | Pointer color. |
| negativecolor (nc)| ☞  | Color for negative range. |
| ledfill (lf)  | 1       | Fill LEDs or dot. |
| select (s)    | ☞       | Overlay with select. |
| ...           | ...     | ... |

**Outputs**

| Output         | Description |
|----------------|-------------|
| output (o)     | Current value. |
| button (b)     | State of push button (if selected). |
| moveddown (md) | Trigger when turned left by movementticks. |
| movedup (mu)   | Trigger when turned right by movementticks. |
| valuechanged (vc)| Trigger when value changes. |

---

## encoquencer – Performance sequencer using E4 encoders

Performance sequencer using encoders of the E4 controller, exact replica of motoquencer but with encoders.

**Example:**
```ini
[encoquencer]
clock = I1
cv = O1
gate = O2
```

**Inputs** (partial)

| Input         | Default | Description |
|---------------|---------|-------------|
| zorder (z)    | 0       | Order of encoders in the sequence. |
| nume4s (n4)   |         | Number of E4 modules if zorder=1/3. |
| ledpreview (pv)| 0      | Show all possible values in LED ring. |
| firstfader (f)| 1       | First encoder to use. |
| numfaders (n) |         | Number of encoders. |
| numsteps (ns) |         | Number of sequence steps. |
| page (p)      | 0       | Page for paging if more steps than encoders. |
| clock (c)     |         | Clock input. |
| ...           | ...     | ... |

**Outputs**

| Output         | Description |
|----------------|-------------|
| cv             | CV output (pitch or other). |
| gate           | Gate output. |
| startofsequence| Trigger at start of sequence. |
| ...            | ... |

---

## euklid – Euclidean rhythm generator

Creates trigger patterns according to Euclidean rhythms, fully CV controllable.

**Example:**
```ini
[euklid]
clock = G1
reset = G2
length = 16
beats = 5
offset = 0
output = G3
```

**Inputs**

| Input         | Default | Description |
|---------------|---------|-------------|
| clock (c)     | ☞       | Clock signal (gate). |
| reset (r)     |         | Reset pattern to start. |
| outputsignal (os)| ☞   | Alternative to passing through input clock. |
| length (l)    | 16      | Pattern length. |
| beats (b)     | 5       | Number of active beats. |
| offset (of)   | 0       | Shifts pattern by steps. |

**Outputs**

| Output      | Description |
|-------------|-------------|
| output (o)  | Output of beats in the pattern. |
| offbeats (ob)| Output where there is no beat. |

---

## explin – Exponential to linear converter

Converts an exponential input curve into a linear output curve.

**Example:**
```ini
[explin]
input = I1
output = O2
startvalue = 8V
endvalue = 0.5V
```

**Inputs**

| Input        | Default | Description |
|--------------|---------|-------------|
| input (i)    | 0.0     | Exponential envelope or similar. |
| startvalue (sv)| 1.0   | Maximum value of input. |
| endvalue (ev)| 0.01    | Value at which input is considered zero. |
| mix (m)      | 1.0     | Mix between dry and wet signal. |

**Outputs**

| Output      | Description |
|-------------|-------------|
| output (o)  | Linearized output. |

---

## faderbank – Create multiple virtual faders in M4 controller

Control up to 16 faders at once, reducing the number of motorfader circuits.

**Example:**
```ini
[faderbank]
preset = P1.1 * 6
output1 = O1
output2 = O2
output3 = O3
```

**Inputs**

| Input            | Default | Description |
|------------------|---------|-------------|
| firstfader (f)   | 1       | First M4 fader of the bank. |
| notches (n)      | 0       | Number of artificial notches. |
| startvalue (sv)  | 0.0     | Initial value after clear/start. |
| ledcolor (lc)    |         | Color of LEDs below faders. |
| ledvalue1...16   |         | Brightness for each LED. |
| select (s)       | ☞       | Overlay with select. |
| ...              | ...     | ... |

**Outputs**

| Output           | Description |
|------------------|-------------|
| output1...16 (o) | Current value of each fader. |
| button1...16 (b) | State of touch button below each fader (if selected). |

---

## fadermatrix – Matrix of up to 4x4 virtual motor faders

Control a 4x4 matrix of parameters, allowing row or column selection.

**Example:**
```ini
[fadermatrix]
rowcolumn = _ROWCOLUMN
output11 = _ATTACK_1
output12 = _DECAY_1
output13 = _SUSTAIN_1
output14 = _RELEASE_1
...
```

**Inputs**

| Input           | Default | Description |
|-----------------|---------|-------------|
| firstfader (f)  | 1       | First M4 fader of the matrix. |
| rowcolumn (rc)  | 0       | Select row or column. |
| notches1...4 (n)| 0       | Notches per column. |
| startvalue1...4 (sv)|     | Start value per column. |
| ledvalueXY      |         | LED brightness for each output. |
| ledcolor1...4   |         | LED color per column. |
| select (s)      | ☞       | Overlay with select. |
| ...             | ...     | ... |

**Outputs**

| Output             | Description |
|--------------------|-------------|
| output11...44 (o)  | CV values of each matrix position. |
| button11...44 (b)  | State of touch button for each position (if selected). |

---

## firefacecontrol – Control a RME Fireface interface (experimental)

Experimental circuit for controlling RME Fireface volumes and mixes via MIDI.

**Inputs** (partial)

| Input           | Default | Description |
|-----------------|---------|-------------|
| trs ()          | 1       | Select TRS port. |
| outputlevel1...16| 0      | Output levels. |
| phonesoutput1,2 | 1       | Phones output. |
| mainoutput (mo) | 1       | Main output. |
| outputmixXinY   | 0       | Output mix for each channel. |
| pan1...16 (p)   | 0       | Panning. |
| unmute1...16 (u)| 0       | Unmute. |
| ...             | ...     | ... |
| select (s)      | ☞       | Overlay with select. |

---

## flipflop – Simple flip flop

Implements a flip flop that stores one bit, manipulated with various triggers.

**Example:**
```ini
[flipflop]
toggle = I1
output = O1
```

**Inputs**

| Input        | Default | Description |
|--------------|---------|-------------|
| toggle (t)   |         | Trigger to invert state. |
| set (s)      |         | Set to 1. |
| reset (r)    |         | Set to 0. |
| clear (cl)   |         | Set to startvalue. |
| startvalue (sv)| 0     | Initial value after clear/start. |
| load (ld)    |         | Loads value from loadvalue. |
| loadvalue (lv)| 1     | Value to set on load. |

**Outputs**

| Output      | Description |
|-------------|-------------|
| output (o)  | Current value (0 or 1). |

---

## fold – CV folder – keep (pitch) CV within certain bounds

Keeps an incoming CV within defined bounds by folding it if it exceeds those bounds. Especially useful for keeping pitches in range.

**Example:**
```ini
[fold]
input = I1
output = O1
foldby = 1V
minimum = 1.2V
maximum = 2.5V
```

**Inputs**

| Input        | Default | Description |
|--------------|---------|-------------|
| input (i)    | 0.0     | Input CV to be folded. |
| foldby (f)   | 0.1     | Amount to be added/subtracted if out of range. Must be positive. |
| minimum (m)  |         | Lower bound. If unpatched, no lower bound. |
| maximum (x)  |         | Upper bound. If unpatched, no upper bound. |

**Outputs**

| Output      | Description |
|-------------|-------------|
| output (o)  | Folded output voltage. |

---

## fourstatebutton – Button switching through 4 states (OBSOLETE)

Superseded by the `button` circuit. Converts a push button into a button that switches through up to four states.

**Example:**
```ini
[fourstatebutton]
button = B1.1
led = L1.1
value1 = I1 + 0V
value2 = I1 + 1V
value3 = I1 + 2V
value4 = I1 + 3V
output = O1
```

**Inputs**

| Input        | Default | Description |
|--------------|---------|-------------|
| button (b)   |         | The button. |
| reset (r)    |         | Resets to first state. |
| value1...4   |         | Output values for each state. |
| startvalue (sv)|       | Initial state at power up. |

**Outputs**

| Output      | Description |
|-------------|-------------|
| output (o)  | Value for the current state. |
| led (l)     | LED in the button. |

---

## gatetool – Operate on triggers and gates, modify gatelength

Utility for working with triggers, gates, and edge-triggers, and for changing gate lengths.

**Example:**
```ini
[gatetool]
inputtrigger = I1
outputgate = O1
gatelength = 0.5
```

**Inputs**

| Input        | Default | Description |
|--------------|---------|-------------|
| inputgate (ig)|        | Input gate, length is relevant. |
| inputtrigger (it)|     | Input trigger, length ignored. |
| inputedge (ie)|        | Edge detection (each transition). |
| gatelength (gl)|       | Length of output gate (seconds or fraction of clock). |
| gatestretch (s)| 0.0  | Makes output gate longer by percentage. |
| mingatelength (m)|0.01| Minimum output gate length. |
| maxgatelength (x)|    | Maximum output gate length. |
| taptempo (tt)|        | Reference clock for fractions. |

**Outputs**

| Output         | Description |
|----------------|-------------|
| outputgate (og)| Gate with controllable length. |
| outputtrigger (ot)| 10 ms trigger. |
| outputedge (oe)| Toggles at every event. |

---

## ifequal – Check if two values are equal

Simplified version of `compare`, just checks if two values are equal.

**Example:**
```ini
[ifequal]
input1 = _TRACK
input2 = 2
ifequal = _TRACK2_GATES
else = 0
output = O1
```

**Inputs**

| Input        | Default | Description |
|--------------|---------|-------------|
| input1 (i1)  | 0.0     | First value. |
| input2 (i2)  | 1.0     | Second value. |
| ifequal (q)  | 1.0     | Output if equal. |
| else (e)     | 0.0     | Output otherwise. |

**Outputs**

| Output      | Description |
|-------------|-------------|
| output (o)  | Result. |

---

## lfo – Low frequency oscillator (LFO)

Flexible LFO with seven waveforms, morphing, sync, randomization, etc.

**Example:**
```ini
[lfo]
hz = 4
triangle = O1
```

**Inputs** (partial)

| Input        | Default | Description |
|--------------|---------|-------------|
| rate (ra)    | 0.0     | 1V/octave frequency control. |
| taptempo (tt)|         | Reference clock. |
| hz           | 1.0     | Frequency in Hz. |
| level (l)    | 1.0     | Max output level. |
| offset (of)  | 0.0     | Adds offset. |
| bipolar (b)  | 0       | Enable bipolar output. |
| randomize (r)| 0       | Randomize each "hill". |
| phase (p)    | 0.0     | Phase shift. |
| pulsewidth (pw)|0.5    | Pulse width for square. |
| skew (sk)    | 0.5     | Symmetry for triangle. |
| sync (sy)    |         | Resets LFO phase. |
| waveform (w) | 0.0     | Selects/morphs waveforms for output (o). |

**Outputs**

| Output     | Description |
|------------|-------------|
| output (o) | Main output (morphs between waveforms). |
| square (q) | Square waveform. |
| sawtooth (st)| Sawtooth waveform. |
| triangle (t)| Triangle waveform. |
| ramp (rp)  | Falling ramp. |
| paraboloid (pb)| Paraboloid waveform. |
| sine (si)  | Sine waveform. |
| cosine (cs)| Cosine waveform. |

---

## logic – Logic operations utility

Logic operations (AND, OR, XOR, etc.) on up to 8 gate signals.

**Example:**
```ini
[logic]
input1 = I1
input2 = I2
and = O1
```

**Inputs**

| Input        | Default | Description |
|--------------|---------|-------------|
| input1...8 (i)| ☞      | Inputs for logic. |
| threshold (t)| 0.1     | Threshold for "high". |
| lowvalue (l) | 0.0     | Output for logic low. |
| highvalue (h)| 1.0     | Output for logic high. |
| countvalue (cv)|0.1    | Value added per high input for count outputs. |

**Outputs**

| Output    | Description |
|-----------|-------------|
| and (a)   | AND of all inputs. |
| or (o)    | OR of all inputs. |
| xor (x)   | XOR of all inputs. |
| nand (na) | NAND of all inputs. |
| nor (no)  | NOR of all inputs. |
| negated (n)| NOT of input1. |
| count (c) | Sum of countvalue for each high input. |
| countlow (cl)| Sum for each low input. |

---

## math – Math utility circuit

Mathematic operations: sum, difference, product, quotient, modulo, power, average, max, min, etc.

**Example:**
```ini
[math]
input1 = I1
input2 = I2
quotient = O1
```

**Inputs**

| Input        | Description |
|--------------|-------------|
| input1, input2 (i)| Inputs for math operations. |

**Outputs** (partial)

| Output      | Description |
|-------------|-------------|
| sum (s)     | input1 + input2 |
| difference (d)| input1 - input2 |
| product (p) | input1 × input2 |
| quotient (qu)| input1 / input2 |
| modulo (md) | input1 modulo input2 |
| power (pw)  | input1 to the power of input2 |
| average (a) | Average of input1 and input2 |
| maximum (x) | Maximum of input1 and input2 |
| minimum (m) | Minimum of input1 and input2 |
| negation (n)| -input1 |
| reciprocal (rc)| 1/input1 |
| amount (am) | Absolute value |
| sine (si)   | Sine of input1 |
| cosine (cs) | Cosine of input1 |
| square (q)  | input1 squared |
| root (ro)   | Square root of input1 |
| logarithm (l)| Natural logarithm |
| round (rd)  | Nearest integer |
| floor (f)   | Largest integer ≤ input1 |
| ceil (c)    | Smallest integer ≥ input1 |

---

## matrixmixer – Matrix mixer for CVs

A 4×4 matrix mixer with four inputs and four outputs, operated by push buttons.

**Example:**
```ini
[matrixmixer]
input1 = I1
input2 = I2
input3 = I3
input4 = I4
output1 = O1
output2 = O2
output3 = O3
output4 = O4
button11 = B1.1
button12 = B1.2
...
led11 = L1.1
led12 = L1.2
...
```

**Inputs**

| Input          | Default | Description |
|----------------|---------|-------------|
| input1...4 (i) | 0.0     | Inputs to mix. |
| auxin1...4 (a) | 0.0     | For cascading mixers. |
| mixmax (m)     | 0.0     | 0: sum, 1: max, between: weighted. |
| startvalue (sv)| 1       | Initial matrix state. |
| buttonXY (b)   |         | Buttons for each matrix node. |
| select (s)     | ☞       | Overlay with select. |

**Outputs**

| Output         | Description |
|----------------|-------------|
| output1...4 (o)| Mixed outputs. |
| led11...led44  | LEDs in the buttons. |

---

## midifileplayer – MIDI file player

Reads MIDI files from SD card and "plays" them by creating CVs for gate, pitch, velocity, etc.

**Example:**
```ini
[midifileplayer]
pitch = O1
gate = O2
```

**Inputs** (partial)

| Input        | Default | Description |
|--------------|---------|-------------|
| file (f)     | 1       | Number of MIDI file to play (midi1.mid, etc.). |
| track (tc)   | 1       | Track number. |
| clock (c)    |         | External clock. |
| reset (r)    |         | Reset playback. |
| loop (lo)    | 1       | Loop mode. |
| speed (sp)   | 1.0     | Relative playback speed. |
| pitch1...8 (p)|         | Output pitches. |
| gate1...8 (g)|         | Output gates. |
| ...          | ...     | ... |

**Outputs** (partial)

| Output         | Description |
|----------------|-------------|
| pitch1...8     | Pitch outputs. |
| gate1...8      | Gate outputs. |
| velocity1...8  | Velocity outputs. |
| endoftrack (et)| Trigger at end of track. |
| error (er)     | Error status (for LED display). |
| clockout (co)  | 16th note clock. |
| midiclock (mc) | MIDI clock (24 PPQN). |

---

## midiin – MIDI to CV converter

Converts incoming MIDI data into CV, gate, and trigger signals. Requires the X7 expander.

**Example:**
```ini
[midiin]
pitch = O1
gate = O2
```

**Inputs** (partial)

| Input            | Default | Description |
|------------------|---------|-------------|
| trs ()           | ☞       | Selects TRS (3.5 mm) port. |
| usb ()           | ☞       | Selects USB port. |
| initialrunning   | 2       | Initial running state (0=stopped, 1=running, 2=auto). |
| systemreset      |         | Resets MIDI state. |
| channel (ch)     | ☞       | Only use commands from this MIDI channel. |
| tuningmode (tm)  | off     | If 1, outputs tuningpitch. |
| tuningpitch (tp) | 2V      | Pitch CV for tuning mode. |
| transpose (tr)   | 0V      | Transposes all output pitches. |
| holdvelocity (hv)| 0       | If 1, velocity is kept after note ends. |
| pitchbendrange  | 1/6V    | Maximum pitchbend range. |
| bendpitch       | 1       | If 1, pitchbend is applied to outputs. |
| roundrobin      | 0       | If 1, output voices are assigned round-robin. |
| voiceallocation | 0       | Output voice allocation mode. |
| notegap         | 0.0     | Gate gap in ms between notes. |
| ccnumber1...4   | 0       | Listen to up to four CCs. |
| lowestnote      | 0       | Lower bound for played notes. |
| highestnote     | 127     | Upper bound for played notes. |
| note1...16      | ☞       | For notegate outputs. |

**Outputs** (partial)

| Output           | Description |
|------------------|-------------|
| clock (c)        | 16th note clock. |
| clock8 (c8)      | 8th note clock. |
| clock4 (c4)      | Quarter note clock. |
| midiclock (mc)   | MIDI clock (24 PPQN). |
| start (st)       | Trigger on MIDI START. |
| stop (sp)        | Trigger on MIDI STOP. |
| running (ru)     | 1 when running. |
| pitch1...8 (p)   | Pitch outputs. |
| gate1...8 (g)    | Gate outputs. |
| velocity1...8    | Velocity outputs. |
| pressure1...8    | Aftertouch/pressure. |
| cc1...cc4        | CC values. |
| notegate1...16   | Gate for each note. |
| pitchbend        | Pitch bend as CV. |
| programchange    | Trigger on program change. |
| ...              | ... |

---

## midiout – CV to MIDI converter

Converts CV/gate signals to MIDI notes, CCs, and more. Requires the X7 expander.

**Example:**
```ini
[midiout]
pitch = I1
gate = I2
ccnumber1 = 17
cc1 = I3
```

**Inputs** (partial)

| Input            | Default | Description |
|------------------|---------|-------------|
| channel (ch)     | 1       | MIDI channel. |
| usb ()           | ☞       | Send to USB port. |
| trs ()           | ☞       | Send to TRS port. |
| pitch1...8 (p)   |         | 1V/oct pitch input(s). |
| gate1...8 (g)    |         | Gate input(s). |
| velocity1...8    | 1.0     | Velocity for each note. |
| notegate1...16   |         | Triggers for specific notes. |
| note1...16       | ☞       | MIDI note number for notegate. |
| modwheel (w)     | 0.0     | Mod wheel (CC#1). |
| volume (vo)      | 1.0     | Volume (CC#7). |
| pitchbend (pb)   | 0.0     | Pitch bend. |
| pitchtracking (pt)| 0      | Pitch tracking mode. |
| pitchbendrange  | 1/6V    | Pitch bend range. |
| ccnumber1...8    | 0       | CC numbers. |
| cc1...cc8        | 0.0     | CC values. |
| cctrigger1...8   |         | Triggers for CC updates. |
| updateccs        |         | Trigger to force CC update. |
| delayinitialccs  | 1.0     | Delay before sending initial CCs. |
| bank (ba)        | ☞       | Bank select. |
| program (pm)     | ☞       | Program select. |
| programchange    |         | Trigger to send program change. |
| start, stop, running |     | MIDI START/STOP. |
| clock (c)        |         | 16th note clock for MIDI clock. |
| midiclock (mc)   |         | MIDI clock (24 PPQN). |
| ...              | ...     | ... |

**Outputs**
None (all output is MIDI).

---

## midithrough – Forward MIDI events from input to one or more outputs

Forwards MIDI data from input ports to output ports.

**Example:**
```ini
[midithrough]
fromusb = 1
totrs = 2
```

**Inputs**

| Input        | Default | Description |
|--------------|---------|-------------|
| fromtrs (ft) | ☞       | TRS port to use as input. |
| fromusb (fu) | ☞       | USB port to use as input. |
| totrs (tt)   | ☞       | TRS port to use as output. |
| tousb (tu)   | ☞       | USB port to use as output. |

---

## minifonion – Musical quantizer

Musical quantizer that moves any input CV into selected notes of a scale.

**Example:**
```ini
[minifonion]
input = I1
output = O2
root = 2
degree = 7
```

**Inputs**

| Input        | Default | Description |
|--------------|---------|-------------|
| input (i)    | 0V      | Unquantized input voltage. |
| trigger (t)  |         | Triggered mode. |
| bypass (b)   | off     | If on, quantization is bypassed. |
| root (ro)    | 0       | Root note (C=0, D=2, etc). |
| degree (dg)  | 0       | Scale number. |
| select1...13 | ☞       | Select intervals. |
| selectfill1...5 | off  | Select alternative intervals. |
| harmonicshift| 0       | Reduce harmonic complexity. |
| noteshift    | 0       | Shift output note by scale notes. |
| selectnoteshift| 0     | Shift by selected scale notes. |
| tuningmode   | off     | If on, outputs tuningpitch. |
| tuningpitch  | 0V      | Pitch for tuning mode. |
| transpose    | 0V      | Adds to output pitch. |

**Outputs**

| Output         | Description |
|----------------|-------------|
| output (o)     | Quantized output voltage. |
| notechange (n) | Trigger when quantization changes. |

---

## mixer – CV mixer

Adds up to eight inputs, can also do min, max, and average.

**Example:**
```ini
[mixer]
input1 = I1
input2 = I2
output = O1
```

**Inputs**

| Input        | Default | Description |
|--------------|---------|-------------|
| input1...8 (i)| 0.0    | Mixer inputs. |

**Outputs**

| Output      | Description |
|-------------|-------------|
| output (o)  | Sum of all inputs. |
| maximum (x) | Maximum of all inputs. |
| minimum (m) | Minimum of all inputs. |
| average (a) | Average of all inputs. |

---

## motoquencer – Motor fader sequencer

Performance sequencer based on motorized faders, up to 32 steps, many features.

**Example:**
```ini
[motoquencer]
clock = I1
cv = O1
gate = O2
```

**Inputs** (partial)

| Input        | Default | Description |
|--------------|---------|-------------|
| firstfader (f)| 1      | First M4 fader to use. |
| numfaders (n)|         | Number of faders. |
| numsteps (ns)|         | Number of steps. |
| page (p)     | 0       | Page for paging. |
| clock (c)    |         | Clock input. |
| taptempo (tt)|         | Reference clock. |
| reset (r)    |         | Reset sequencer. |
| mute (m)     |         | Mute output gates. |
| cvbase (cb)  | 0.0     | Lowest CV. |
| cvrange (cr) | 0.2     | CV range. |
| quantize (q) | 2       | Quantization mode. |
| fadermode (fm)| 0      | Fader mode (CV, random, repeats, etc). |
| buttonmode (bm)| 0     | Button mode (gates, start/end, etc). |
| ...          | ...     | ... |

**Outputs** (partial)

| Output         | Description |
|----------------|-------------|
| cv             | CV output. |
| gate           | Gate output. |
| startofsequence| Trigger at sequence start. |
| ...            | ... |

---

## motorfader – Create virtual fader in M4 controller

Basic access to motor faders, supports presets, overlay, force feedback.

**Example:**
```ini
[motorfader]
fader = 1
preset = _PRESET
output = O1
```

**Inputs**

| Input        | Default | Description |
|--------------|---------|-------------|
| fader (f)    | 1       | Which motor fader to use. |
| startvalue (sv)| 0.0   | Initial value. |
| notches (n)  | 0       | Number of notches. |
| ledvalue (lv)|         | Override LED brightness. |
| ledcolor (lc)|         | LED color. |
| sharewithnext| 0       | Share with next motorfader circuit. |
| select (s)   | ☞       | Overlay with select. |
| ...          | ...     | ... |

**Outputs**

| Output      | Description |
|-------------|-------------|
| output (o)  | Current value. |
| button (b)  | State of touch button if selected. |

---

## multicompare – Compare input with up to eight possible values

For routing one of up to eight signals based on the input value.

**Example:**
```ini
[multicompare]
input = _WAVEFORMSWITCH
compare1 = 5
ifequal1 = _SAWTOOTH
compare2 = 7
ifequal2 = _TRIANGLE
compare3 = 98
ifequal3 = _SQUARE
else = _SINE
output = O1
```

**Inputs**

| Input        | Default | Description |
|--------------|---------|-------------|
| input (i)    | 0.0     | Value to compare. |
| compare1...8 (c)|      | Up to 8 reference values. |
| ifequal1...8 (if)|     | Output for each comparison. |
| else (e)     | 0.0     | Output if no match. |

**Outputs**

| Output      | Description |
|-------------|-------------|
| output (o)  | Output for matching or else. |

---

## queue – Clocked CV shift register

Implements a shift register (queue) with 64 cells for CVs, with up to 8 outputs at arbitrary positions.

**Example:**
```ini
[queue]
input = I1
clock = I2
output4 = O4
```

**Inputs**

| Input                 | Default | Description |
|-----------------------|---------|-------------|
| input (i)             | 0.0     | CV to be pushed into the first cell at each clock. |
| clock (c)             |         | Each clock moves all cells forward. |
| outputpos1...outputpos8 (op) | ☞ | Position of each output (1–64). Defaults to 1–8. |

**Outputs**

| Output                | Description |
|-----------------------|-------------|
| output1...output8 (o) | Output at the specified cell. |

---

## random – Random number generator

Generates random voltages, either clocked (sample & hold) or free-running, with optional quantization.

**Example:**
```ini
[random]
clock = I1
output = O1
minimum = 1V
maximum = 3V
```

**Inputs**

| Input        | Default | Description |
|--------------|---------|-------------|
| clock (c)    |         | If patched, outputs new value on each clock. |
| minimum (m)  | 0.0     | Minimum output value. |
| maximum (x)  | 1.0     | Maximum output value. |
| steps (s)    | 0       | Number of discrete steps (0 = fully analog). |

**Outputs**

| Output      | Description |
|-------------|-------------|
| output (o)  | Random output value. |

---

## recorder – Record and playback CVs and gates

Records and plays back one CV, eight gates, and one integer number, with permanent SD card storage.

**Example:**
```ini
[recorder]
cvin = I1
cvout = O1
recordbutton = B1.1
playbutton = B1.2
stopbutton = B1.3
```

**Inputs** (partial)

| Input         | Default | Description |
|---------------|---------|-------------|
| playbutton (pb)|        | Trigger to start playback. |
| recordbutton (rb)|      | Trigger to start/stop recording. |
| stopbutton (sb)|        | Trigger to stop playback/recording. |
| loop (lo)     | off     | Loop playback. |
| pause (p)     | off     | Pause tape. |
| mode (m)      |         | Directly set mode (idle/play/record). |
| playbackspeed (ps)| 1.0 | Playback speed (negative = reverse). |
| scrub (sc)    | off     | Enable scrubbing. |
| scrubposition (sp)| 0.0 | Position for scrubbing (0=start, 1=end). |
| trimstart (ts)| 0.0     | Fraction of start to omit. |
| trimend (te)  | 1.0     | Fraction of end to omit. |
| cvin (ci)     | 0.0     | CV to record. |
| gatein1...8 (gi)|       | Gates to record. |
| numberin (ni) |         | Integer to record (0–255). |
| clock (c)     |         | Use clocked mode. |
| sample (sm)   |         | Enable triggered mode for stepped CVs. |
| save (sv)     |         | Save tape to SD card. |
| load (ld)     |         | Load tape from SD card. |
| filenumber (f)| 1       | File number to load/save. |

**Outputs** (partial)

| Output         | Description |
|----------------|-------------|
| recordled (rl) | 1 while recording. |
| playled (pl)   | 1 while playing or scrubbing. |
| stopled (sl)   | 1 when idle. |
| cvout (co)     | Recorded or played-back CV. |
| gateout1...8 (go)| Recorded or played-back gates. |
| numberout (no)| Recorded or played-back integer. |
| overflow (ov)  | 1 if memory is exceeded. |

---

## sample – Sample & Hold Circuit

Samples input voltage on trigger, holds until next trigger or gate.

**Example:**
```ini
[sample]
input = I1
sample = I2
output = O1
```

**Inputs**

| Input        | Default | Description |
|--------------|---------|-------------|
| input (i)    | 0.0     | Input signal to be sampled. |
| sample (sm)  |         | Trigger to take a sample. |
| gate (g)     |         | Samples while gate is high. |
| timewindow (tw)| 0.0   | Time window after sample to allow CV to settle. |
| bypass (b)   |         | While high, output follows input. |

**Outputs**

| Output      | Description |
|-------------|-------------|
| output (o)  | Most recently sampled value. |

---

## select – Copy a signal if selected

Copies a value only when the circuit is selected via `select`.

**Example:**
```ini
[select]
select = _SELECT
input = _FLASH
output = L1.1
```

**Inputs**

| Input        | Default | Description |
|--------------|---------|-------------|
| input (i)    | 0.0     | Signal to copy. |
| select (s)   | ☞       | Only copy when selected. |
| selectat (sa)| ☞       | Value at which circuit is active. |

**Outputs**

| Output      | Description |
|-------------|-------------|
| output (o)  | Input copied if selected. |

---

## sequencer – Simple eight step sequencer

Metropolis-style 8-step sequencer, chainable for longer sequences.

**Example:**
```ini
[sequencer]
clock = I1
pitchoutput = O1
pitch1 = 1V
pitch2 = 3.5V
pitch3 = 8V
pitch4 = -2V
```

**Inputs**

| Input          | Default | Description |
|----------------|---------|-------------|
| clock (c)      |         | Advances the sequence. |
| reset (r)      |         | Resets to first step. |
| stages (sg)    | ☞       | Number of steps to use. |
| steps (s)      | 0       | Force restart after n clocks. |
| transpose (tr) | 0.0     | Transpose output. |
| outputscaling (os)| 1.0  | Scale pitch output. |
| gatelength (gl)| ☞       | Output gate length. |
| pitch1...8 (p) | 0.0     | Step pitches. |
| cv1...8        | 0.0     | Step CVs. |
| gate1...8 (g)  | 1       | Step gates (on/off). |
| slew1...8      | 0.0     | Slew limit per step. |
| repeat1...8    | 1.0     | Number of repeats per step. |
| chaintonext (cn)| ☞      | Chain to next sequencer. |

**Outputs**

| Output         | Description |
|----------------|-------------|
| pitchoutput (po)| Unquantized pitch. |
| cvoutput (co)  | Optional CV output. |
| gateoutput (go)| Gate output. |

---

## sinfonionlink – Sync harmonic state from Sinfonion

Syncs root, scale, transpose, detune, etc. from a Sinfonion to the MASTER18.

**Example:**
```ini
[sinfonionlink]
root = _ROOT
degree = _DEGREE
```

**Outputs**

| Output         | Description |
|----------------|-------------|
| root (ro)      | Current root note (int). |
| degree (dg)    | Current scale (int). |
| transpose (tr) | Global transposition (1V/oct). |
| chaoticdetune (ch)| Chaotic detune value. |
| harmonicshift (has)| Harmonic shift value. |
| linkstate (ls) | 1 if link is active. |
| clock (c)      | Sinfonion clock. |
| reset (r)      | Trigger on song reset. |
| step (s)       | Trigger on song step. |
| beat (b)       | Trigger on song beat. |

---

## slew – Slew limiter

CV-controllable slew limiter for CVs, with exponential, linear, and S-shaped algorithms.

**Example:**
```ini
[slew]
input = I1
slew = P1.1
exponential = O1
```

**Inputs**

| Input        | Default | Description |
|--------------|---------|-------------|
| input (i)    |         | CV to slew limit. |
| slew (sw)    | 1.0     | Slew rate. |
| slewup (u)   | 1.0     | Upwards slew rate. |
| slewdown (d) | 1.0     | Downwards slew rate. |
| gate (g)     | ☞       | Slew only when gate is high. |

**Outputs**

| Output         | Description |
|----------------|-------------|
| exponential (e)| Exponential slew-limited CV. |
| linear (l)     | Linear slew-limited CV. |
| scurve (c)     | S-curve slew-limited CV. |

---

## spring – Physical spring simulation

Simulates a mass on a spring, producing "bouncing" CV sources with damping, gravity, and shoving.

**Example:**
```ini
[spring]
position = O1
velocity = O2
```

**Inputs**

| Input           | Default | Description |
|-----------------|---------|-------------|
| mass (m)        | 1.0     | Mass of the object. |
| gravity (g)     | 1.0     | Gravity of the planet. |
| springforce (f) | 1.0     | Force per meter of stretch. |
| flowresistance (fr) | 0.0 | Damping proportional to velocity. |
| friction (fi)   | 0.0     | Constant friction. |
| speed (sp)      | 0.0     | 1V/oct speed scaling. |
| shove (sh)      | 0       | Gate to apply extra force. |
| shoveforce (sf) | 1.0     | Force applied when shove is active. |
| reset (r)       |         | Resets to start position. |
| startvelocity (sv) | 0.0  | Start velocity at reset. |
| startposition (spo) | 0.0 | Start position at reset. |

**Outputs**

| Output         | Description |
|----------------|-------------|
| velocity (v)   | Current velocity of the mass. |
| position (p)   | Current length of the spring. |

---

## superjust – Perfect intonation of up to eight voices

Automatically creates a perfect pure intonation for up to eight input pitches.

**Example:**
```ini
[superjust]
input1 = I1
input2 = I2
input3 = I3
output1 = O1
output2 = O2
output3 = O3
```

**Inputs**

| Input        | Default | Description |
|--------------|---------|-------------|
| input1...8 (i)| ☞      | Up to 8 pitch inputs (1V/oct). |
| tuningmode (tm)| 0     | If 1, outputs tuningpitch. |
| tuningpitch (tp)| 0V   | Pitch for tuning mode. |
| bypass (b)   | 0       | If 1, passes inputs through. |
| transpose (tr)| 0V     | Added to all outputs. |

**Outputs**

| Output      | Description |
|-------------|-------------|
| output1...8 | Perfectly tuned pitch outputs. |

---

## switch – Addressable/clockable switch

Switches several inputs to one output or vice versa, by address or stepping.

**Example:**
```ini
[switch]
input1 = I1
input2 = I2
input3 = I3
input4 = I4
output = O1
forward = I8
```

**Inputs**

| Input           | Default | Description |
|-----------------|---------|-------------|
| input1...16 (i) | 0.0     | Inputs to be switched. |
| forward (f)     |         | Trigger to step forward. |
| backward (b)    |         | Trigger to step backward. |
| reset (r)       |         | Reset to initial position. |
| offset (of)     | 0       | CV addressable offset. |

**Outputs**

| Output          | Description |
|-----------------|-------------|
| output1...16 (o)| Switched outputs. |

---

## switchedpot – Overlay pot with multiple functions (OBSOLETE)

Superseded by the `pot` circuit. Allows one pot to control up to 8 virtual pots, selected by switches.

**Example:**
```ini
[switchedpot]
pot = P1.1
switch1 = B1.1
switch2 = B1.2
output1 = _DECAY
output2 = _RELEASE
```

**Inputs**

| Input           | Description |
|-----------------|-------------|
| pot (p)         | Physical pot input. |
| switch1...8 (s) | Select which virtual pots are active. |
| bipolar (b)     | If 1, output is -1...+1. |

**Outputs**

| Output          | Description |
|-----------------|-------------|
| output1...8 (o) | Virtual pot outputs. |

---

## timing – Shuffle/swing and complex timing generator

Converts a steady input clock into a clock with flexible timing modifications (swing, shuffle, etc).

**Example:**
```ini
[timing]
clock = G1
output = G2
timing1 = 0.0
timing2 = 0.3
```

**Inputs**

| Input           | Default | Description |
|-----------------|---------|-------------|
| clock (c)       |         | Steady input clock. |
| reset (r)       |         | Resets internal step counter. |
| timing1...8 (t) | ☞       | Relative timing for each step (fraction of a clock cycle). |

**Outputs**

| Output      | Description |
|-------------|-------------|
| output (o)  | Modified output clock. |

---

## togglebutton – Create on/off buttons (OBSOLETE)

Superseded by the `button` circuit. Converts a push button into a toggle button with optional persistence.

**Example:**
```ini
[togglebutton]
button = B1.4
led = L1.4
```

**Inputs**

| Input        | Default | Description |
|--------------|---------|-------------|
| button (b)   |         | Push button input. |
| reset (r)    |         | Reset to off. |
| onvalue (ov) | 1.0     | Value when on. |
| offvalue (fv)| 0.0     | Value when off. |
| startvalue (sv)|       | Initial value. |

**Outputs**

| Output      | Description |
|-------------|-------------|
| led (l)     | LED output. |
| output (o)  | Output value (on/off). |
| inverted (iv)| Inverted output. |
| negated (n) | 1 when button is off, 0 when on. |

---

## transient – Transient generator

Creates a linear transient from a start value to an end value, with optional looping and clocking.

**Example:**
```ini
[transient]
start = 1V
end = 3V
duration = 600
output = O1
```

**Inputs**

| Input        | Default | Description |
|--------------|---------|-------------|
| start (st)   | 0.0     | Start value. |
| end (e)      | 1.0     | End value. |
| duration (d) | 1.0     | Duration (seconds or clock ticks). |
| loop (lo)    | 0       | If 1, restarts on reaching end. |
| pingpong (pp)| 0       | If 1, bounces back and forth. |
| freeze (f)   | 0       | While 1, freezes transient. |
| reset (r)    |         | Resets to start. |
| clock (c)    |         | If patched, duration in clock ticks. |

**Outputs**

| Output         | Description |
|----------------|-------------|
| output (o)     | Current value. |
| phase (p)      | Current phase (0–1). |
| endoftransient (et)| 1 at end (see docs). |

---

## triggerdelay – Trigger Delay with multi tap and optional clocking

Delays a trigger or gate by a time or clock ticks, with optional repeats and gate length control.

**Example:**
```ini
[triggerdelay]
input = G1
output = G2
delay = 0.1
gatelength = 0.05
```

**Inputs**

| Input        | Default | Description |
|--------------|---------|-------------|
| input (i)    |         | Trigger/gate input. |
| delay (dl)   | 1.0     | Delay time (seconds or clock ticks). |
| gatelength (gl)| ☞     | Output gate length. |
| repeats (rp) | 1       | Number of repeats. |
| mute (m)     | 0       | If high, suppresses output gates. |
| clock (c)    |         | If patched, operates in clocked mode. |

**Outputs**

| Output      | Description |
|-------------|-------------|
| output (o)  | Delayed trigger/gate output. |
| overflow (ov)| 1 if memory is exceeded. |

---

## unusedfaders – Declare unused motor faders

Disables unused motor faders, moves them to the bottom, and blanks their LEDs.

**Example:**
```ini
[unusedfaders]
select = _MENU_ACTIVE
firstfader = 6
numfaders = 3
```

**Inputs**

| Input        | Default | Description |
|--------------|---------|-------------|
| firstfader (f)| 1      | First unused fader. |
| numfaders (n)| 1       | Number of unused faders. |
| select (s)   | ☞       | Overlay with select. |
| selectat (sa)| ☞       | Value at which circuit is active. |

---

## vcotuner – Measure frequency and tuning of a VCO

Measures frequency of incoming signals, provides tuning and pitch-following functions (MASTER18 only).

**Example:**
```ini
[vcotuner]
ledsharp = L1.1
ledintune = L1.3
ledflat = L1.5
pitch = O1
```

**Inputs**

| Input            | Default | Description |
|------------------|---------|-------------|
| tuningnote (tn)  | -1      | Note to tune to (0=C, 1=C#, etc; -1=nearest semitone). |
| concertpitch (cp)| 440.0   | Frequency of A1. |
| basepitch (b)    | 0V      | Reference for pitch output (C0). |
| smooth (sm)      | 0.01    | Smoothing for frequency measurement. |
| precision (pc)   | 3.0     | Precision for "in tune" (in cents). |
| select (s)       | ☞       | Overlay with select. |

**Outputs**

| Output          | Description |
|-----------------|-------------|
| hz ()           | Measured frequency (Hz). |
| ledflat (lf)    | LED for flat tuning. |
| ledsharp (ls)   | LED for sharp tuning. |
| ledintune (lt)  | LED for in-tune. |
| intune (it)     | 1 if in tune. |
| tuning (t)      | Deviation in 1V/oct. |
| cents (c)       | Deviation in cents. |
| pitch (p)       | Measured pitch (1V/oct). |
| referencepitch (rp)| Reference pitch (1V/oct). |
| vcofound (vf)   | 1 if a valid signal is detected. |

---

Here are circuits 81–end from the DROID blue-6 circuit reference, each in its own markdown section:

---

## (81) — (End): [No additional native circuits in your provided text]

**Note:**
The supplied reference text ends with `vcotuner` as the last described circuit (number 80 in this enumeration). If you have additional circuit documentation or extensions, please provide that content and I’ll be happy to continue!

---

# Summary Table of All Circuits (for convenience)

| #  | Circuit Name         | Section Header                  |
|----|---------------------|---------------------------------|
| 1  | adc                 | adc – AD Converter with 12 bits |
| 2  | algoquencer         | algoquencer – Algorithmic sequencer |
| 3  | arpeggio            | arpeggio – Arpeggiator – pattern based melody generator |
| 4  | bernoulli           | bernoulli – Random gate distributor |
| 5  | burst               | burst – Generate burst of pulses |
| 6  | button              | button – Does all sorts of useful things with buttons |
| 7  | buttongroup         | buttongroup – Connected buttons |
| 8  | calibrator          | calibrator – VCO Calibrator     |
| 9  | case                | case – Switch choosing from inputs via conditions |
| 10 | chord               | chord – Chord generator         |
| 11 | clocktool           | clocktool – Clock divider / multiplier / shifter |
| 12 | compare             | compare – Compare two values    |
| 13 | contour             | contour – Contour generator     |
| 14 | copy                | copy – Copy a signal, while applying attenuation and offset |
| 15 | crossfader          | crossfader – Morph between 8 inputs |
| 16 | cvlooper            | cvlooper – Clocked CV looper    |
| 17 | dac                 | dac – DA Converter with 12 bits |
| 18 | delay               | delay – A tape delay for CVs, gates and numbers |
| 19 | detune              | detune – Detune multiple voices in a most disharmonic way |
| 20 | droid               | droid – General DROID controls  |
| ...| ...                 | ...                             |
| 71 | spring              | spring – Physical spring simulation |
| 72 | superjust           | superjust – Perfect intonation of up to eight voices |
| 73 | switch              | switch – Addressable/clockable switch |
| 74 | switchedpot         | switchedpot – Overlay pot with multiple functions (OBSOLETE) |
| 75 | timing              | timing – Shuffle/swing and complex timing generator |
| 76 | togglebutton        | togglebutton – Create on/off buttons (OBSOLETE) |
| 77 | transient           | transient – Transient generator |
| 78 | triggerdelay        | triggerdelay – Trigger Delay with multi tap and optional clocking |
| 79 | unusedfaders        | unusedfaders – Declare unused motor faders |
| 80 | vcotuner            | vcotuner – measure frequency and tuning of a VCO |
