package display

import (
	"fmt"
	"strings"
)

// DisplayStyle represents different documentation display styles
type DisplayStyle int

const (
	StylePlain DisplayStyle = iota
	StyleColorized
)

// DocumentationOptions configures how circuit documentation is formatted
type DocumentationOptions struct {
	UseColor     bool
	ShowDefaults bool
	Style        DisplayStyle
}

// Circuit represents a circuit for display purposes
type Circuit interface {
	GetName() string
	GetDescription() string
	GetCategory() string
	GetInputs() map[string]Parameter
	GetOutputs() map[string]Parameter
	GetExamples() []Example
}

// Example represents a code example
type Example interface {
	GetTitle() string
	GetDescription() string
	GetCode() string
}

// FormatCircuitDocumentation returns formatted documentation using the specified options
func FormatCircuitDocumentation(circuit Circuit, options DocumentationOptions) string {
	useColor := options.UseColor || options.Style == StyleColorized

	// Create header
	var title string
	if useColor {
		title = fmt.Sprintf("%s – %s", ColorizeElement(strings.ToUpper(circuit.GetName()), ElementCircuitTitle), circuit.GetDescription())
	} else {
		title = fmt.Sprintf("%s – %s", strings.ToLower(circuit.GetName()), circuit.GetDescription())
	}

	doc := title + "\n\n"

	// Add category
	if useColor {
		doc += fmt.Sprintf("Category: %s\n\n", ColorizeElement(circuit.GetCategory(), ElementCategory))
	} else {
		doc += fmt.Sprintf("Category: %s\n\n", circuit.GetCategory())
	}

	// Add inputs
	inputs := circuit.GetInputs()
	if len(inputs) > 0 {
		if useColor {
			doc += ColorizeElement("INPUTS", ElementSectionHeader) + "\n"
		} else {
			doc += "INPUTS:\n"
		}
		tableOptions := TableOptions{
			Style:        getTableStyle(useColor),
			ShowDefaults: options.ShowDefaults,
		}
		doc += FormatParameterTable(inputs, tableOptions)
		doc += "\n"
	}

	// Add outputs
	outputs := circuit.GetOutputs()
	if len(outputs) > 0 {
		if useColor {
			doc += ColorizeElement("OUTPUTS", ElementSectionHeader) + "\n"
		} else {
			doc += "OUTPUTS:\n"
		}
		tableOptions := TableOptions{
			Style:        getTableStyle(useColor),
			ShowDefaults: false, // Outputs typically don't have defaults
		}
		doc += FormatParameterTable(outputs, tableOptions)
		doc += "\n"
	}

	// Add examples
	examples := circuit.GetExamples()
	if len(examples) > 0 {
		if useColor {
			doc += ColorizeElement("EXAMPLES", ElementSectionHeader) + "\n"
		} else {
			doc += "EXAMPLES:\n"
		}
		for _, example := range examples {
			if useColor {
				doc += fmt.Sprintf("  %s\n", colorizeText(example.GetTitle(), "yellow"))
			} else {
				doc += fmt.Sprintf("  %s:\n\n", example.GetTitle())
			}
			if example.GetDescription() != "" {
				doc += fmt.Sprintf("     %s\n", example.GetDescription())
			}
			doc += "\n"
			// Indent code
			lines := strings.Split(example.GetCode(), "\n")
			for _, line := range lines {
				if line != "" {
					if useColor {
						doc += fmt.Sprintf("     %s\n", line)
					} else {
						doc += fmt.Sprintf("    %s\n", line)
					}
				}
			}
			doc += "\n"
		}
	}

	return doc
}

// getTableStyle returns the appropriate table style
func getTableStyle(useColor bool) TableStyle {
	if useColor {
		return TableStyleColorized
	}
	return TableStylePlain
}

// FormatCircuitSummary returns a concise summary of the circuit
func FormatCircuitSummary(circuit Circuit) string {
	inputCount := len(circuit.GetInputs())
	outputCount := len(circuit.GetOutputs())

	return fmt.Sprintf("%s - %s (%d inputs, %d outputs)",
		circuit.GetName(), circuit.GetDescription(), inputCount, outputCount)
}
