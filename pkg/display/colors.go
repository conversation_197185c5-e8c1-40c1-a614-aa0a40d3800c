package display

// ElementType represents different types of elements that can be colorized
type ElementType int

const (
	ElementParameterName ElementType = iota
	ElementParameterType
	ElementSectionHeader
	ElementCircuitTitle
	ElementCategory
)

// colorizeText adds ANSI color codes to text
func colorizeText(text, color string) string {
	colors := map[string]string{
		"red":     "\x1b[31m",
		"green":   "\x1b[32m",
		"yellow":  "\x1b[33m",
		"blue":    "\x1b[34m",
		"magenta": "\x1b[35m",
		"cyan":    "\x1b[36m",
		"white":   "\x1b[37m",
		"bold":    "\x1b[1m",
	}

	reset := "\x1b[0m"
	if colorCode, exists := colors[color]; exists {
		return colorCode + text + reset
	}
	return text
}

// ColorizeElement provides unified colorization for different element types
func ColorizeElement(text string, elementType ElementType) string {
	switch elementType {
	case ElementParameterName:
		return colorizeText(text, "blue")
	case ElementParameterType:
		return colorizeText(text, "green")
	case ElementSectionHeader:
		return colorizeText(text, "cyan")
	case ElementCircuitTitle:
		return colorizeText(text, "bold")
	case ElementCategory:
		return colorizeText(text, "yellow")
	default:
		return text
	}
}
