package display

import (
	"strings"
	"testing"
)

// Mock implementations for testing
type mockParameter struct {
	name        string
	paramType   string
	description string
	defaultVal  string
}

func (p *mockParameter) GetName() string        { return p.name }
func (p *mockParameter) GetType() string        { return p.paramType }
func (p *mockParameter) GetDescription() string { return p.description }
func (p *mockParameter) GetDefault() string     { return p.defaultVal }

type mockExample struct {
	title       string
	description string
	code        string
}

func (e *mockExample) GetTitle() string       { return e.title }
func (e *mockExample) GetDescription() string { return e.description }
func (e *mockExample) GetCode() string        { return e.code }

type mockCircuit struct {
	name        string
	description string
	category    string
	inputs      map[string]Parameter
	outputs     map[string]Parameter
	examples    []Example
}

func (c *mockCircuit) GetName() string                  { return c.name }
func (c *mockCircuit) GetDescription() string           { return c.description }
func (c *mockCircuit) GetCategory() string              { return c.category }
func (c *mockCircuit) GetInputs() map[string]Parameter  { return c.inputs }
func (c *mockCircuit) GetOutputs() map[string]Parameter { return c.outputs }
func (c *mockCircuit) GetExamples() []Example           { return c.examples }

func TestColorizeElement(t *testing.T) {
	tests := []struct {
		name        string
		text        string
		elementType ElementType
		expectColor bool
	}{
		{
			name:        "parameter name should be blue",
			text:        "frequency",
			elementType: ElementParameterName,
			expectColor: true,
		},
		{
			name:        "parameter type should be green",
			text:        "CV",
			elementType: ElementParameterType,
			expectColor: true,
		},
		{
			name:        "section header should be cyan",
			text:        "INPUTS",
			elementType: ElementSectionHeader,
			expectColor: true,
		},
		{
			name:        "circuit title should be bold",
			text:        "LFO",
			elementType: ElementCircuitTitle,
			expectColor: true,
		},
		{
			name:        "category should be yellow",
			text:        "modulation",
			elementType: ElementCategory,
			expectColor: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := ColorizeElement(tt.text, tt.elementType)

			// Should contain the original text
			if !strings.Contains(result, tt.text) {
				t.Errorf("ColorizeElement() should contain original text %q, got %q", tt.text, result)
			}

			// Should contain ANSI color codes if expectColor is true
			if tt.expectColor {
				if !strings.Contains(result, "\x1b[") {
					t.Errorf("ColorizeElement() should contain ANSI color codes, got %q", result)
				}
				if !strings.Contains(result, "\x1b[0m") {
					t.Errorf("ColorizeElement() should contain ANSI reset code, got %q", result)
				}
			}
		})
	}
}

func TestFormatParameterTable(t *testing.T) {
	params := map[string]Parameter{
		"zebra": &mockParameter{name: "zebra", paramType: "CV", description: "Last param", defaultVal: "0"},
		"alpha": &mockParameter{name: "alpha", paramType: "Gate", description: "First param", defaultVal: "1"},
		"beta":  &mockParameter{name: "beta", paramType: "Float", description: "Second param", defaultVal: ""},
	}

	// Test plain style with defaults
	plainOptions := TableOptions{
		Style:        TableStylePlain,
		ShowDefaults: true,
	}
	plainResult := FormatParameterTable(params, plainOptions)
	if plainResult == "" {
		t.Error("Plain table should not be empty")
	}
	if strings.Contains(plainResult, "\x1b[") {
		t.Error("Plain table should not contain ANSI color codes")
	}

	// Test colorized style
	colorizedOptions := TableOptions{
		Style:        TableStyleColorized,
		ShowDefaults: true,
	}
	colorizedResult := FormatParameterTable(params, colorizedOptions)
	if colorizedResult == "" {
		t.Error("Colorized table should not be empty")
	}
	if !strings.Contains(colorizedResult, "\x1b[") {
		t.Error("Colorized table should contain ANSI color codes")
	}

	// Results should be different
	if plainResult == colorizedResult {
		t.Error("Plain and colorized tables should be different")
	}

	// Test alphabetical sorting
	alphaIndex := strings.Index(plainResult, "alpha")
	betaIndex := strings.Index(plainResult, "beta")
	if alphaIndex == -1 || betaIndex == -1 || alphaIndex >= betaIndex {
		t.Error("Parameters should be sorted alphabetically (alpha before beta)")
	}
}

func TestFormatCircuitDocumentation(t *testing.T) {
	circuit := &mockCircuit{
		name:        "test",
		description: "Test circuit",
		category:    "modulation",
		inputs: map[string]Parameter{
			"input1": &mockParameter{name: "input1", paramType: "CV", description: "Test input", defaultVal: "0"},
		},
		outputs: map[string]Parameter{
			"output1": &mockParameter{name: "output1", paramType: "Gate", description: "Test output"},
		},
		examples: []Example{
			&mockExample{title: "Example 1", description: "Test example", code: "test = 1"},
		},
	}

	// Test plain style
	plainOptions := DocumentationOptions{
		UseColor:     false,
		ShowDefaults: true,
		Style:        StylePlain,
	}
	plainResult := FormatCircuitDocumentation(circuit, plainOptions)
	if plainResult == "" {
		t.Error("Plain documentation should not be empty")
	}
	if strings.Contains(plainResult, "\x1b[") {
		t.Error("Plain documentation should not contain ANSI color codes")
	}
	if !strings.Contains(plainResult, "test – Test circuit") {
		t.Error("Plain documentation should contain circuit title")
	}

	// Test colorized style
	colorizedOptions := DocumentationOptions{
		UseColor:     true,
		ShowDefaults: true,
		Style:        StyleColorized,
	}
	colorizedResult := FormatCircuitDocumentation(circuit, colorizedOptions)
	if colorizedResult == "" {
		t.Error("Colorized documentation should not be empty")
	}
	if !strings.Contains(colorizedResult, "\x1b[") {
		t.Error("Colorized documentation should contain ANSI color codes")
	}
	if !strings.Contains(colorizedResult, "TEST") { // Should be uppercase
		t.Error("Colorized documentation should contain uppercase circuit name")
	}

	// Results should be different
	if plainResult == colorizedResult {
		t.Error("Plain and colorized documentation should be different")
	}
}

func TestFormatCircuitSummary(t *testing.T) {
	circuit := &mockCircuit{
		name:        "lfo",
		description: "Low frequency oscillator",
		inputs: map[string]Parameter{
			"hz": &mockParameter{name: "hz", paramType: "Float", description: "Frequency"},
		},
		outputs: map[string]Parameter{
			"output": &mockParameter{name: "output", paramType: "CV", description: "Output"},
		},
	}

	result := FormatCircuitSummary(circuit)
	expected := "lfo - Low frequency oscillator (1 inputs, 1 outputs)"
	if result != expected {
		t.Errorf("FormatCircuitSummary() = %q, want %q", result, expected)
	}
}
