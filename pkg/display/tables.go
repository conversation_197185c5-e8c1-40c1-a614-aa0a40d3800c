package display

import (
	"sort"

	"github.com/jedib0t/go-pretty/v6/table"
	"github.com/jedib0t/go-pretty/v6/text"
)

// Parameter represents a circuit parameter for display purposes
type Parameter interface {
	GetName() string
	GetType() string
	GetDescription() string
	GetDefault() string
}

// TableStyle represents different table formatting styles
type TableStyle int

const (
	TableStylePlain TableStyle = iota
	TableStyleColorized
)

// TableOptions configures how parameter tables are formatted
type TableOptions struct {
	Style        TableStyle
	ShowDefaults bool
}

// FormatParameterTable formats parameters as a professional table
func FormatParameterTable(params map[string]Parameter, options TableOptions) string {
	if len(params) == 0 {
		return ""
	}

	// Create table writer
	t := table.NewWriter()

	// Configure table style
	t.SetStyle(table.Style{
		Name: "DroidParameters",
		Box: table.BoxStyle{
			Left:           "  ",
			Right:          "",
			MiddleVertical: " ",
			PaddingLeft:    "",
			PaddingRight:   " ",
		},
		Format: table.FormatOptions{
			Header: text.FormatUpper,
			Row:    text.FormatDefault,
		},
		Options: table.Options{
			DrawBorder:      false,
			SeparateColumns: true,
			SeparateHeader:  true,
			SeparateRows:    false,
		},
	})

	// Set up headers
	headers := getCleanTableHeaders(options.ShowDefaults)

	// Convert headers to table.Row
	headerRow := make(table.Row, len(headers))
	for i, header := range headers {
		headerRow[i] = header
	}
	t.AppendHeader(headerRow)

	// Set column configurations
	if options.ShowDefaults {
		t.SetColumnConfigs([]table.ColumnConfig{
			{Number: 1, WidthMax: 15, Align: text.AlignLeft},
			{Number: 2, WidthMax: 10, Align: text.AlignLeft},
			{Number: 3, WidthMax: 10, Align: text.AlignLeft},
			{Number: 4, WidthMax: 55, Align: text.AlignLeft, WidthMaxEnforcer: text.WrapSoft},
		})
	} else {
		t.SetColumnConfigs([]table.ColumnConfig{
			{Number: 1, WidthMax: 15, Align: text.AlignLeft},
			{Number: 2, WidthMax: 10, Align: text.AlignLeft},
			{Number: 3, WidthMax: 65, Align: text.AlignLeft, WidthMaxEnforcer: text.WrapSoft},
		})
	}

	// Add rows with appropriate formatting in alphabetical order
	sortedNames := getSortedParameterNames(params)
	for _, name := range sortedNames {
		param := params[name]
		var paramName, paramType string

		if options.Style == TableStyleColorized {
			paramName = ColorizeElement(name, ElementParameterName)
			paramType = ColorizeElement(param.GetType(), ElementParameterType)
		} else {
			// Default/plain style
			paramName = name
			paramType = param.GetType()
		}

		if options.ShowDefaults {
			defaultValue := param.GetDefault()
			if defaultValue == "" {
				defaultValue = "—"
			}
			t.AppendRow(table.Row{paramName, paramType, defaultValue, param.GetDescription()})
		} else {
			t.AppendRow(table.Row{paramName, paramType, param.GetDescription()})
		}
	}

	return t.Render()
}

// getCleanTableHeaders returns clean table headers without emojis
func getCleanTableHeaders(showDefaults bool) []string {
	if showDefaults {
		return []string{"Parameter", "Type", "Default", "Description"}
	}
	return []string{"Parameter", "Type", "Description"}
}

// getSortedParameterNames returns parameter names sorted alphabetically
func getSortedParameterNames(params map[string]Parameter) []string {
	names := make([]string, 0, len(params))
	for name := range params {
		names = append(names, name)
	}
	sort.Strings(names)
	return names
}
