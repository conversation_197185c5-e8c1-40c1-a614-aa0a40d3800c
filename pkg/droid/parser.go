package droid

import (
	"bufio"
	"fmt"
	"regexp"
	"strconv"
	"strings"
	"sync"

	"github.com/t6d/droid-tools/pkg/circuits"
)

var (
	expressionRegex *regexp.Regexp
	regexOnce       sync.Once
)

// DroidConfig represents a parsed DROID configuration
type DroidConfig struct {
	Circuits []Circuit
	Comments []Comment
}

// Circuit represents a single circuit block in the configuration
type Circuit struct {
	Name       string
	Type       string
	Parameters map[string]Parameter
	LineNumber int
}

// Parameter represents a parameter assignment in a circuit
type Parameter struct {
	Name       string
	Value      string
	LineNumber int
}

// Comment represents a comment line
type Comment struct {
	Text       string
	LineNumber int
}

// ParseError represents a parsing error
type ParseError struct {
	Line    int
	Message string
	Type    ParseErrorType
}

type ParseErrorType int

const (
	ParseErrorSyntax ParseErrorType = iota
	ParseErrorInvalidCircuit
	ParseErrorInvalidParameter
	ParseErrorDuplicateParameter
)

func (e ParseError) Error() string {
	return fmt.Sprintf("line %d: %s", e.Line, e.Message)
}

// ParseConfig parses a DROID configuration from a string
func ParseConfig(content string) (*DroidConfig, []ParseError) {
	config := &DroidConfig{
		Circuits: make([]Circuit, 0),
		Comments: make([]Comment, 0),
	}

	var errors []ParseError
	scanner := bufio.NewScanner(strings.NewReader(content))
	lineNumber := 0

	var currentCircuit *Circuit

	for scanner.Scan() {
		lineNumber++
		line := strings.TrimSpace(scanner.Text())

		// Skip empty lines
		if line == "" {
			continue
		}

		// Handle comments
		if strings.HasPrefix(line, "#") {
			config.Comments = append(config.Comments, Comment{
				Text:       line,
				LineNumber: lineNumber,
			})
			continue
		}

		// Handle circuit headers [circuitname]
		if strings.HasPrefix(line, "[") && strings.HasSuffix(line, "]") {
			// Save previous circuit if exists
			if currentCircuit != nil {
				config.Circuits = append(config.Circuits, *currentCircuit)
			}

			// Parse circuit header
			circuitType := strings.Trim(line, "[]")
			if circuitType == "" {
				errors = append(errors, ParseError{
					Line:    lineNumber,
					Message: "empty circuit name",
					Type:    ParseErrorSyntax,
				})
				continue
			}

			// Create new circuit
			currentCircuit = &Circuit{
				Name:       fmt.Sprintf("circuit_%d", len(config.Circuits)+1),
				Type:       circuitType,
				Parameters: make(map[string]Parameter),
				LineNumber: lineNumber,
			}
			continue
		}

		// Handle parameter assignments
		if currentCircuit != nil {
			if err := parseParameter(line, lineNumber, currentCircuit); err != nil {
				errors = append(errors, *err)
			}
		} else {
			// Parameter outside of circuit
			errors = append(errors, ParseError{
				Line:    lineNumber,
				Message: "parameter assignment outside of circuit block",
				Type:    ParseErrorSyntax,
			})
		}
	}

	// Add final circuit
	if currentCircuit != nil {
		config.Circuits = append(config.Circuits, *currentCircuit)
	}

	return config, errors
}

// parseParameter parses a parameter assignment line
func parseParameter(line string, lineNumber int, circuit *Circuit) *ParseError {
	// Remove inline comments
	if idx := strings.Index(line, "#"); idx != -1 {
		line = strings.TrimSpace(line[:idx])
	}

	// Skip empty lines after comment removal
	if line == "" {
		return nil
	}

	// Parse parameter assignment: name = value
	parts := strings.SplitN(line, "=", 2)
	if len(parts) != 2 {
		return &ParseError{
			Line:    lineNumber,
			Message: fmt.Sprintf("invalid parameter syntax: %s", line),
			Type:    ParseErrorSyntax,
		}
	}

	paramName := strings.TrimSpace(parts[0])
	paramValue := strings.TrimSpace(parts[1])

	if paramName == "" {
		return &ParseError{
			Line:    lineNumber,
			Message: "empty parameter name",
			Type:    ParseErrorSyntax,
		}
	}

	// Check for duplicate parameters
	if _, exists := circuit.Parameters[paramName]; exists {
		return &ParseError{
			Line:    lineNumber,
			Message: fmt.Sprintf("duplicate parameter '%s' in circuit '%s'", paramName, circuit.Type),
			Type:    ParseErrorDuplicateParameter,
		}
	}

	// Validate expression format
	if err := validateExpression(paramValue, lineNumber); err != nil {
		return err
	}

	// Add parameter
	circuit.Parameters[paramName] = Parameter{
		Name:       paramName,
		Value:      paramValue,
		LineNumber: lineNumber,
	}

	return nil
}

// ValidateCircuitParameters validates parameter names against circuit definitions
func ValidateCircuitParameters(config *DroidConfig) []*DroidError {
	var errors []*DroidError

	// Get registry once
	registry, err := getCircuitRegistry()
	if err != nil {
		errors = append(errors, NewDroidError(
			ErrorTypeInvalidConfig,
			fmt.Sprintf("failed to load circuit registry: %v", err),
			nil,
		))
		return errors
	}

	// Cache circuit definitions to avoid repeated lookups
	circuitDefCache := make(map[string]CircuitDefinition)

	for _, circuit := range config.Circuits {
		// Check if it's a controller first
		if registry.IsController(circuit.Type) {
			// Controllers don't have parameters to validate
			continue
		}

		// Get circuit definition from cache or registry
		circuitDef, exists := circuitDefCache[circuit.Type]
		if !exists {
			def, found := GetCircuitDefinition(circuit.Type)
			if !found {
				errors = append(errors, NewDroidError(
					ErrorTypeInvalidConfig,
					fmt.Sprintf("line %d: unknown circuit type: %s", circuit.LineNumber, circuit.Type),
					nil,
				))
				continue
			}
			circuitDef = def
			circuitDefCache[circuit.Type] = def
		}

		// Validate each parameter (only if there are parameters)
		for paramName, param := range circuit.Parameters {
			if !isValidParameter(circuitDef, paramName) {
				errors = append(errors, NewDroidError(
					ErrorTypeInvalidConfig,
					fmt.Sprintf("line %d: invalid parameter '%s' for circuit '%s'", param.LineNumber, paramName, circuit.Type),
					nil,
				))
			}
		}
	}

	return errors
}

// isValidParameter checks if a parameter is valid for a circuit type
func isValidParameter(circuitDef CircuitDefinition, paramName string) bool {
	// Check inputs
	for _, input := range circuitDef.Inputs {
		if input.Name == paramName {
			return true
		}
	}

	// Check outputs
	for _, output := range circuitDef.Outputs {
		if output.Name == paramName {
			return true
		}
	}

	return false
}

// CircuitDefinition represents the definition of a circuit type
type CircuitDefinition struct {
	Name        string
	Category    string
	Description string
	Inputs      []ParameterDefinition
	Outputs     []ParameterDefinition
}

// ParameterDefinition represents a parameter definition
type ParameterDefinition struct {
	Name        string
	Type        ParameterType
	Description string
	Default     string
	Required    bool
}

type ParameterType int

const (
	ParameterTypeCV ParameterType = iota
	ParameterTypeTrigger
	ParameterTypeGate
	ParameterTypeNumber
	ParameterTypeString
)

// GetCircuitDefinition returns the definition for a circuit type
func GetCircuitDefinition(circuitType string) (CircuitDefinition, bool) {
	// Use our existing circuit registry
	registry, err := getCircuitRegistry()
	if err != nil {
		return CircuitDefinition{}, false
	}

	circuit, exists := registry.GetCircuit(circuitType)
	if !exists {
		return CircuitDefinition{}, false
	}

	// Convert from circuits.Circuit to CircuitDefinition
	def := CircuitDefinition{
		Name:        circuit.Name,
		Category:    circuit.Category,
		Description: circuit.Description,
		Inputs:      make([]ParameterDefinition, 0),
		Outputs:     make([]ParameterDefinition, 0),
	}

	// Parse inputs from circuit data
	for paramName, paramInfo := range circuit.Inputs {
		paramType := convertParameterType(paramInfo.Type)
		def.Inputs = append(def.Inputs, ParameterDefinition{
			Name:        paramName,
			Type:        paramType,
			Description: paramInfo.Description,
			Default:     paramInfo.Default,
			Required:    paramInfo.Required,
		})
	}

	// Parse outputs from circuit data
	for paramName, paramInfo := range circuit.Outputs {
		paramType := convertParameterType(paramInfo.Type)
		def.Outputs = append(def.Outputs, ParameterDefinition{
			Name:        paramName,
			Type:        paramType,
			Description: paramInfo.Description,
		})
	}

	return def, true
}

// getCircuitRegistry returns the default circuit registry
func getCircuitRegistry() (*circuits.Registry, error) {
	return circuits.GetDefaultRegistry()
}

// convertParameterType converts circuits.ParameterType to parser ParameterType
func convertParameterType(typeStr circuits.ParameterType) ParameterType {
	switch typeStr {
	case circuits.TypeCV, circuits.TypeBipolar, circuits.TypePitch:
		return ParameterTypeCV
	case circuits.TypeTrigger:
		return ParameterTypeTrigger
	case circuits.TypeGate:
		return ParameterTypeGate
	case circuits.TypeInteger, circuits.TypeFloat:
		return ParameterTypeNumber
	case circuits.TypeButton, circuits.TypeLED, circuits.TypePot, circuits.TypeSwitch:
		return ParameterTypeString
	default:
		return ParameterTypeCV // Default fallback
	}
}

// ValidateCircuitCountLimits validates that no circuit type exceeds the 255 limit
func ValidateCircuitCountLimits(config *DroidConfig) []*DroidError {
	var errors []*DroidError

	// Count circuits by type
	circuitCounts := make(map[string]int)
	circuitFirstLine := make(map[string]int) // Track first occurrence for error reporting

	for _, circuit := range config.Circuits {
		circuitCounts[circuit.Type]++

		// Track first occurrence of each circuit type
		if _, exists := circuitFirstLine[circuit.Type]; !exists {
			circuitFirstLine[circuit.Type] = circuit.LineNumber
		}

		// Check if this circuit type exceeds the limit
		if circuitCounts[circuit.Type] > 255 {
			errors = append(errors, NewDroidError(
				ErrorTypeInvalidConfig,
				fmt.Sprintf("line %d: too many circuits of type '%s': %d > 255",
					circuit.LineNumber, circuit.Type, circuitCounts[circuit.Type]),
				nil,
			))
		}
	}

	return errors
}

// ValidateRegisterNumberRanges validates that all register numbers are >= 1
func ValidateRegisterNumberRanges(config *DroidConfig) []*DroidError {
	var errors []*DroidError

	for _, circuit := range config.Circuits {
		for _, param := range circuit.Parameters {
			// Extract registers from parameter value
			registers := extractRegisters(param.Value)

			for _, register := range registers {
				// Extract the number part from the register
				registerNumber := extractRegisterNumber(register)

				if registerNumber < 1 {
					errors = append(errors, NewDroidError(
						ErrorTypeInvalidConfig,
						fmt.Sprintf("line %d: register numbers must be >= 1, found '%s'",
							param.LineNumber, register),
						nil,
					))
				}
			}
		}
	}

	return errors
}

// extractRegisterNumber extracts the numeric part from a register reference
func extractRegisterNumber(register string) int {
	// Use regex to extract the number part from registers like I1, O2, B1.1, etc.
	numberPattern := regexp.MustCompile(`^[A-Za-z]+([0-9]+)`)

	matches := numberPattern.FindStringSubmatch(register)
	if len(matches) >= 2 {
		// Convert the number string to integer
		if num, err := strconv.Atoi(matches[1]); err == nil {
			return num
		}
	}

	// Return 0 if no valid number found (will trigger validation error)
	return 0
}

// extractRegisters extracts register references from a parameter value
func extractRegisters(value string) []string {
	var registers []string

	// Regex to match register patterns: I1, O2, R3, B1.1, L1.2, P1.3, G1.4, N1, etc.
	registerPattern := regexp.MustCompile(`\b([IONBLPGR])([0-9]+)(?:\.([0-9]+))?\b`)

	matches := registerPattern.FindAllStringSubmatch(value, -1)
	for _, match := range matches {
		if len(match) >= 3 {
			register := match[1] + match[2]
			if len(match) > 3 && match[3] != "" {
				register += "." + match[3]
			}
			registers = append(registers, register)
		}
	}

	return registers
}

// ValidateRegisterUsage validates register usage across all circuits
func ValidateRegisterUsage(config *DroidConfig) []*DroidError {
	var errors []*DroidError

	// Track used output registers to detect duplicates
	usedOutputRegisters := make(map[string]*Circuit)

	// Get circuit registry for parameter type information
	registry, err := getCircuitRegistry()
	if err != nil {
		errors = append(errors, NewDroidError(
			ErrorTypeInvalidConfig,
			fmt.Sprintf("failed to load circuit registry for register validation: %v", err),
			nil,
		))
		return errors
	}

	for _, circuit := range config.Circuits {
		// Skip controllers - they don't have register assignments
		if registry.IsController(circuit.Type) {
			continue
		}

		// Get circuit definition to determine parameter types
		circuitDef, exists := GetCircuitDefinition(circuit.Type)
		if !exists {
			// Circuit validation will catch this, skip register validation
			continue
		}

		// Validate each parameter for register usage
		for paramName, param := range circuit.Parameters {
			registerErrors := validateParameterRegisterUsage(
				&circuit, paramName, &param, circuitDef, usedOutputRegisters)
			errors = append(errors, registerErrors...)
		}
	}

	return errors
}

// validateParameterRegisterUsage validates register usage for a single parameter
func validateParameterRegisterUsage(circuit *Circuit, paramName string, param *Parameter,
	circuitDef CircuitDefinition, usedOutputRegisters map[string]*Circuit) []*DroidError {

	var errors []*DroidError

	// Extract registers from the parameter value
	registers := extractRegisters(param.Value)

	// Determine if this parameter is an output
	isOutput := isOutputParameter(circuitDef, paramName)

	for _, register := range registers {
		// Validate register format
		if !isValidRegisterFormat(register) {
			errors = append(errors, NewDroidError(
				ErrorTypeInvalidConfig,
				fmt.Sprintf("line %d: invalid register format '%s'", param.LineNumber, register),
				nil,
			))
			continue
		}

		// Check for duplicate output register usage (simplified - only flag obvious conflicts)
		if isOutput {
			if existingCircuit, exists := usedOutputRegisters[register]; exists {
				// For now, be very permissive and only flag conflicts between identical circuit types
				// This avoids false positives while still catching obvious errors
				if circuit.Type == existingCircuit.Type {
					errors = append(errors, NewDroidError(
						ErrorTypeInvalidConfig,
						fmt.Sprintf("line %d: register '%s' already used as output by circuit '%s' (line %d)",
							param.LineNumber, register, existingCircuit.Type, existingCircuit.LineNumber),
						nil,
					))
				}
			} else {
				usedOutputRegisters[register] = circuit
			}
		}

		// Check for output-only registers used as inputs
		if !isOutput && isOutputOnlyRegister(register) {
			errors = append(errors, NewDroidError(
				ErrorTypeInvalidConfig,
				fmt.Sprintf("line %d: cannot use output-only register '%s' as input",
					param.LineNumber, register),
				nil,
			))
		}
	}

	return errors
}

// isOutputParameter determines if a parameter is an output for the given circuit
func isOutputParameter(circuitDef CircuitDefinition, paramName string) bool {
	for _, output := range circuitDef.Outputs {
		if output.Name == paramName {
			return true
		}
	}
	return false
}

// isValidRegisterFormat validates the format of a register reference
func isValidRegisterFormat(register string) bool {
	// Match valid register patterns: I1, O2, R3, B1.1, L1.2, P1.3, G1.4, N1
	registerPattern := regexp.MustCompile(`^([IONBLPGR])([0-9]+)(?:\.([0-9]+))?$`)
	return registerPattern.MatchString(register)
}

// isOutputOnlyRegister determines if a register is output-only
func isOutputOnlyRegister(register string) bool {
	// Output-only registers in DROID:
	// - O (outputs): O1, O2, etc.
	// - G (gates): G1, G2, etc.
	// - L (LEDs): L1.1, L1.2, etc.
	// - R (registers): R1, R2, etc. (these are typically outputs)

	if len(register) == 0 {
		return false
	}

	registerType := register[0]
	switch registerType {
	case 'O', 'G', 'L', 'R':
		return true
	default:
		return false
	}
}

// hasSelectParameter checks if a circuit has a select parameter for conditional operation
func hasSelectParameter(circuit *Circuit) bool {
	_, hasSelect := circuit.Parameters["select"]
	return hasSelect
}

// ValidateRequiredInputs validates that all required inputs have values
func ValidateRequiredInputs(config *DroidConfig) []*DroidError {
	var errors []*DroidError

	// Get circuit registry for parameter information
	registry, err := getCircuitRegistry()
	if err != nil {
		errors = append(errors, NewDroidError(
			ErrorTypeInvalidConfig,
			fmt.Sprintf("failed to load circuit registry for required input validation: %v", err),
			nil,
		))
		return errors
	}

	for _, circuit := range config.Circuits {
		// Skip controllers - they don't have required inputs in the same way
		if registry.IsController(circuit.Type) {
			continue
		}

		// Get circuit definition to determine required inputs
		circuitDef, exists := GetCircuitDefinition(circuit.Type)
		if !exists {
			// Circuit validation will catch this, skip required input validation
			continue
		}

		// Check each input parameter to see if it's required
		for _, inputDef := range circuitDef.Inputs {
			if isRequiredInput(inputDef) {
				// Check if this required input has a value
				if _, hasValue := circuit.Parameters[inputDef.Name]; !hasValue {
					errors = append(errors, NewDroidError(
						ErrorTypeInvalidConfig,
						fmt.Sprintf("line %d: required input '%s' has no value for circuit '%s'",
							circuit.LineNumber, inputDef.Name, circuit.Type),
						nil,
					))
				}
			}
		}
	}

	return errors
}

// isRequiredInput determines if an input parameter is required
func isRequiredInput(inputDef ParameterDefinition) bool {
	// For now, be very conservative and only flag inputs that are truly essential
	// Most DROID inputs have sensible defaults or are optional
	// We'll implement a more sophisticated approach later based on circuit-specific knowledge

	// For now, don't flag any inputs as required to avoid false positives
	// This can be enhanced later with circuit-specific required input lists
	return false
}

// ValidateOutputDestinations validates that all outputs have destinations
func ValidateOutputDestinations(config *DroidConfig) []*DroidError {
	var errors []*DroidError

	// Get circuit registry for parameter information
	registry, err := getCircuitRegistry()
	if err != nil {
		errors = append(errors, NewDroidError(
			ErrorTypeInvalidConfig,
			fmt.Sprintf("failed to load circuit registry for output destination validation: %v", err),
			nil,
		))
		return errors
	}

	for _, circuit := range config.Circuits {
		// Skip controllers - they don't have outputs in the same way
		if registry.IsController(circuit.Type) {
			continue
		}

		// Get circuit definition to determine output parameters
		circuitDef, exists := GetCircuitDefinition(circuit.Type)
		if !exists {
			// Circuit validation will catch this, skip output validation
			continue
		}

		// Check each output parameter to see if it has a destination
		for _, outputDef := range circuitDef.Outputs {
			if param, hasOutput := circuit.Parameters[outputDef.Name]; hasOutput {
				// Check if the output has a destination
				if !hasValidDestination(param.Value) {
					errors = append(errors, NewDroidError(
						ErrorTypeInvalidConfig,
						fmt.Sprintf("line %d: output '%s' has no destination for circuit '%s'",
							param.LineNumber, outputDef.Name, circuit.Type),
						nil,
					))
				}
			}
		}
	}

	return errors
}

// hasValidDestination checks if an output parameter has a valid destination
func hasValidDestination(value string) bool {
	// An output has a valid destination if:
	// 1. It's not empty
	// 2. It's not just whitespace
	// 3. It contains some meaningful assignment

	trimmedValue := strings.TrimSpace(value)

	// Empty or whitespace-only values are invalid
	if trimmedValue == "" {
		return false
	}

	// For now, any non-empty value is considered a valid destination
	// This could be enhanced to validate specific destination formats
	return true
}

// ValidateCableConnections validates that cables follow DROID connection rules:
// - Each cable must be connected to exactly one output
// - Each cable must be connected to at least one input
func ValidateCableConnections(config *DroidConfig) []*DroidError {
	var errors []*DroidError

	// Get circuit registry for parameter information
	registry, err := getCircuitRegistry()
	if err != nil {
		errors = append(errors, NewDroidError(
			ErrorTypeInvalidConfig,
			fmt.Sprintf("failed to load circuit registry for cable validation: %v", err),
			nil,
		))
		return errors
	}

	// Track cable outputs (where cables are assigned) and inputs (where cables are used)
	cableOutputs := make(map[string]CableConnection)  // cable -> where it's output
	cableInputs := make(map[string][]CableConnection) // cable -> where it's used as input

	// Scan all circuits for cable usage
	for _, circuit := range config.Circuits {
		// Skip controllers - they don't have register assignments
		if registry.IsController(circuit.Type) {
			continue
		}

		// Get circuit definition to determine parameter types
		circuitDef, exists := GetCircuitDefinition(circuit.Type)
		if !exists {
			// Circuit validation will catch this, skip cable validation
			continue
		}

		// Check each parameter for cable usage
		for paramName, param := range circuit.Parameters {
			isOutput := isOutputParameter(circuitDef, paramName)

			if isOutput {
				// Check if this parameter assigns to a cable
				cables := extractCables(param.Value)
				for _, cable := range cables {
					if existingOutput, exists := cableOutputs[cable]; exists {
						// Cable already has an output - this is an error
						errors = append(errors, NewDroidError(
							ErrorTypeInvalidConfig,
							fmt.Sprintf("line %d: cable '%s' is assigned by multiple outputs: circuit '%s' (line %d) and circuit '%s' (line %d)",
								param.LineNumber, cable,
								existingOutput.CircuitType, existingOutput.LineNumber,
								circuit.Type, param.LineNumber),
							nil,
						))
					} else {
						cableOutputs[cable] = CableConnection{
							CircuitType: circuit.Type,
							ParamName:   paramName,
							LineNumber:  param.LineNumber,
						}
					}
				}
			} else {
				// Check if this parameter uses cables as inputs
				cables := extractCables(param.Value)
				for _, cable := range cables {
					cableInputs[cable] = append(cableInputs[cable], CableConnection{
						CircuitType: circuit.Type,
						ParamName:   paramName,
						LineNumber:  param.LineNumber,
					})
				}
			}
		}
	}

	// Validate cable connection rules
	for cable, output := range cableOutputs {
		// Check if cable has at least one input
		if inputs, hasInputs := cableInputs[cable]; !hasInputs || len(inputs) == 0 {
			errors = append(errors, NewDroidError(
				ErrorTypeInvalidConfig,
				fmt.Sprintf("line %d: cable '%s' is assigned by circuit '%s' but never used as input",
					output.LineNumber, cable, output.CircuitType),
				nil,
			))
		}
	}

	// Check for cables used as inputs but never assigned
	for cable, inputs := range cableInputs {
		if _, hasOutput := cableOutputs[cable]; !hasOutput {
			// Report the first usage location
			firstInput := inputs[0]
			errors = append(errors, NewDroidError(
				ErrorTypeInvalidConfig,
				fmt.Sprintf("line %d: cable '%s' is used as input by circuit '%s' but never assigned by any output",
					firstInput.LineNumber, cable, firstInput.CircuitType),
				nil,
			))
		}
	}

	return errors
}

// CableConnection represents where a cable is connected
type CableConnection struct {
	CircuitType string
	ParamName   string
	LineNumber  int
}

// extractCables extracts cable identifiers from a parameter value
// Cables are identifiers that start with underscore (_)
func extractCables(value string) []string {
	var cables []string

	// Use regex to find cable identifiers (starting with underscore, followed by alphanumeric/underscore)
	cableRegex := regexp.MustCompile(`_[A-Za-z][A-Za-z0-9_]*`)
	matches := cableRegex.FindAllString(value, -1)

	// Remove duplicates
	seen := make(map[string]bool)
	for _, match := range matches {
		if !seen[match] {
			cables = append(cables, match)
			seen[match] = true
		}
	}

	return cables
}

// isCable checks if an identifier is a cable (starts with underscore)
func isCable(identifier string) bool {
	return strings.HasPrefix(identifier, "_") && len(identifier) > 1
}

// getExpressionRegex returns a compiled regex for expression validation
func getExpressionRegex() *regexp.Regexp {
	regexOnce.Do(func() {
		// Define valid term patterns
		// A term can be: number, variable, input/output reference, voltage, or hardware reference
		// Hardware references include: I/O (inputs/outputs), N (notes), B (buttons), L (LEDs), P (pots), G (gates)
		termPattern := `[+-]?\d*\.?\d+[Vv]?|[A-Za-z_][A-Za-z0-9_]*|[IONBLPG][0-9]+(?:\.[0-9]+)?`

		// Define the complete expression pattern: A op B op C
		// Where A is required, operators can be *, /, +, -, and B/C are optional
		// Supports: A, A * B, A / B, A + B, A - B, A * B + C, A / B + C, etc.
		expressionPattern := fmt.Sprintf(
			`^(%s)(\s*[*/]\s*(%s))?(\s*[+-]\s*(%s))?$`,
			termPattern, termPattern, termPattern,
		)

		expressionRegex = regexp.MustCompile(expressionPattern)
	})
	return expressionRegex
}

// validateExpression validates DROID expressions in the format A * B + C
// where B and C are optional and can have unary minus operators
func validateExpression(value string, lineNumber int) *ParseError {
	// Trim whitespace
	value = strings.TrimSpace(value)

	// Empty values are not allowed
	if value == "" {
		return &ParseError{
			Line:    lineNumber,
			Message: "empty parameter value",
			Type:    ParseErrorSyntax,
		}
	}

	// Use pre-compiled regex for better performance
	regex := getExpressionRegex()
	if !regex.MatchString(value) {
		return &ParseError{
			Line:    lineNumber,
			Message: fmt.Sprintf("invalid expression format '%s' - must be in format 'A op B op C' where operators are *, /, +, - and B/C are optional", value),
			Type:    ParseErrorSyntax,
		}
	}

	return nil
}
