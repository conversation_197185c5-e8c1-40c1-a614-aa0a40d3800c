package droid

import (
	"errors"
	"fmt"
)

// DroidError represents different types of errors that can occur
type DroidError struct {
	Type    ErrorType
	Message string
	Cause   error
}

// ErrorType represents the category of error
type ErrorType int

const (
	ErrorTypeUnknown ErrorType = iota
	ErrorTypeFileNotFound
	ErrorTypeFileRead
	ErrorTypeDeviceNotFound
	ErrorTypeMIDIFailed
	ErrorTypeMemoryAllocation
	ErrorTypeInvalidArgs
	ErrorTypeSysExEncoding
	ErrorTypeInvalidConfig
)

// Error implements the error interface
func (e *DroidError) Error() string {
	if e.Cause != nil {
		return fmt.Sprintf("%s: %v", e.Message, e.Cause)
	}
	return e.Message
}

// Unwrap returns the underlying error
func (e *DroidError) Unwrap() error {
	return e.Cause
}

// Is checks if the error is of a specific type
func (e *DroidError) Is(target error) bool {
	if t, ok := target.(*DroidError); ok {
		return e.Type == t.Type
	}
	return false
}

// NewDroidError creates a new DroidError
func NewDroidError(errorType ErrorType, message string, cause error) *DroidError {
	return &DroidError{
		Type:    errorType,
		Message: message,
		Cause:   cause,
	}
}

// Error type constructors for common errors
func NewFileNotFoundError(filename string) *DroidError {
	return NewDroidError(ErrorTypeFileNotFound, fmt.Sprintf("file not found: %s", filename), nil)
}

func NewDeviceNotFoundError(deviceName string) *DroidError {
	return NewDroidError(ErrorTypeDeviceNotFound, fmt.Sprintf("MIDI device not found: %s", deviceName), nil)
}

func NewMIDIError(message string, cause error) *DroidError {
	return NewDroidError(ErrorTypeMIDIFailed, fmt.Sprintf("MIDI error: %s", message), cause)
}

func NewValidationError(message string, cause error) *DroidError {
	return NewDroidError(ErrorTypeInvalidConfig, fmt.Sprintf("validation error: %s", message), cause)
}

// IsFileNotFound checks if an error is a file not found error
func IsFileNotFound(err error) bool {
	if err == nil {
		return false
	}
	var droidErr *DroidError
	if errors.As(err, &droidErr) {
		return droidErr.Type == ErrorTypeFileNotFound
	}
	return false
}

// IsDeviceNotFound checks if an error is a device not found error
func IsDeviceNotFound(err error) bool {
	if err == nil {
		return false
	}
	var droidErr *DroidError
	if errors.As(err, &droidErr) {
		return droidErr.Type == ErrorTypeDeviceNotFound
	}
	return false
}

// IsMIDIError checks if an error is a MIDI-related error
func IsMIDIError(err error) bool {
	if err == nil {
		return false
	}
	var droidErr *DroidError
	if errors.As(err, &droidErr) {
		return droidErr.Type == ErrorTypeMIDIFailed
	}
	return false
}
