package droid

import (
	"errors"
	"testing"
)

func TestDroidError(t *testing.T) {
	// Test basic error creation
	err := NewDroidError(ErrorTypeFileNotFound, "test message", nil)
	if err.Type != ErrorTypeFileNotFound {
		t.<PERSON><PERSON>("Expected error type %v, got %v", ErrorTypeFileNotFound, err.Type)
	}
	if err.Message != "test message" {
		t.<PERSON><PERSON>("Expected message 'test message', got '%s'", err.Message)
	}
	if err.Cause != nil {
		t.<PERSON><PERSON>rf("Expected nil cause, got %v", err.Cause)
	}
}

func TestDroidErrorWithCause(t *testing.T) {
	cause := errors.New("underlying error")
	err := NewDroidError(ErrorTypeMIDIFailed, "MIDI error", cause)

	if err.Cause != cause {
		t.Errorf("Expected cause %v, got %v", cause, err.Cause)
	}

	// Test Unwrap
	if err.Unwrap() != cause {
		t.<PERSON><PERSON><PERSON>("Expected Unwrap to return %v, got %v", cause, err.Unwrap())
	}
}

func TestDroidErrorString(t *testing.T) {
	// Test error without cause
	err := NewDroidError(ErrorTypeFileNotFound, "file not found", nil)
	expected := "file not found"
	if err.Error() != expected {
		t.Errorf("Expected error string '%s', got '%s'", expected, err.Error())
	}

	// Test error with cause
	cause := errors.New("underlying error")
	err = NewDroidError(ErrorTypeMIDIFailed, "MIDI failed", cause)
	expected = "MIDI failed: underlying error"
	if err.Error() != expected {
		t.Errorf("Expected error string '%s', got '%s'", expected, err.Error())
	}
}

func TestDroidErrorIs(t *testing.T) {
	err1 := NewDroidError(ErrorTypeFileNotFound, "test", nil)
	err2 := NewDroidError(ErrorTypeFileNotFound, "different message", nil)
	err3 := NewDroidError(ErrorTypeMIDIFailed, "test", nil)

	// Same type should match
	if !err1.Is(err2) {
		t.Error("Errors of same type should match with Is()")
	}

	// Different type should not match
	if err1.Is(err3) {
		t.Error("Errors of different types should not match with Is()")
	}

	// Non-DroidError should not match
	if err1.Is(errors.New("regular error")) {
		t.Error("DroidError should not match regular error")
	}
}

func TestErrorConstructors(t *testing.T) {
	// Test NewFileNotFoundError
	err := NewFileNotFoundError("test.ini")
	if err.Type != ErrorTypeFileNotFound {
		t.Errorf("NewFileNotFoundError should create ErrorTypeFileNotFound, got %v", err.Type)
	}
	if err.Message != "file not found: test.ini" {
		t.Errorf("Unexpected message: %s", err.Message)
	}

	// Test NewDeviceNotFoundError
	err = NewDeviceNotFoundError("DROID")
	if err.Type != ErrorTypeDeviceNotFound {
		t.Errorf("NewDeviceNotFoundError should create ErrorTypeDeviceNotFound, got %v", err.Type)
	}
	if err.Message != "MIDI device not found: DROID" {
		t.Errorf("Unexpected message: %s", err.Message)
	}

	// Test NewMIDIError
	cause := errors.New("connection failed")
	err = NewMIDIError("send failed", cause)
	if err.Type != ErrorTypeMIDIFailed {
		t.Errorf("NewMIDIError should create ErrorTypeMIDIFailed, got %v", err.Type)
	}
	if err.Cause != cause {
		t.Errorf("NewMIDIError should preserve cause")
	}

	// Test NewValidationError
	err = NewValidationError("invalid syntax", nil)
	if err.Type != ErrorTypeInvalidConfig {
		t.Errorf("NewValidationError should create ErrorTypeInvalidConfig, got %v", err.Type)
	}
}

func TestErrorTypeCheckers(t *testing.T) {
	fileErr := NewFileNotFoundError("test.ini")
	deviceErr := NewDeviceNotFoundError("DROID")
	midiErr := NewMIDIError("failed", nil)
	regularErr := errors.New("regular error")

	// Test IsFileNotFound
	if !IsFileNotFound(fileErr) {
		t.Error("IsFileNotFound should return true for file not found error")
	}
	if IsFileNotFound(deviceErr) {
		t.Error("IsFileNotFound should return false for device error")
	}
	if IsFileNotFound(regularErr) {
		t.Error("IsFileNotFound should return false for regular error")
	}
	if IsFileNotFound(nil) {
		t.Error("IsFileNotFound should return false for nil error")
	}

	// Test IsDeviceNotFound
	if !IsDeviceNotFound(deviceErr) {
		t.Error("IsDeviceNotFound should return true for device not found error")
	}
	if IsDeviceNotFound(fileErr) {
		t.Error("IsDeviceNotFound should return false for file error")
	}

	// Test IsMIDIError
	if !IsMIDIError(midiErr) {
		t.Error("IsMIDIError should return true for MIDI error")
	}
	if IsMIDIError(fileErr) {
		t.Error("IsMIDIError should return false for file error")
	}
}
