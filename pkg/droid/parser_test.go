package droid

import (
	"strings"
	"testing"
)

func TestParseConfig(t *testing.T) {
	tests := []struct {
		name             string
		input            string
		expectedErrors   int
		expectedCircuits int
	}{
		{
			name: "valid simple config",
			input: `# Simple LFO configuration
[lfo]
hz = 2
sine = O1

[sequencer]
clock = I1
cv = O2`,
			expectedErrors:   0,
			expectedCircuits: 2,
		},
		{
			name: "invalid circuit type",
			input: `[unknown_circuit]
param = value`,
			expectedErrors:   0, // Parser doesn't validate circuit types
			expectedCircuits: 1,
		},
		{
			name: "parameter outside circuit",
			input: `param = value
[lfo]
hz = 1`,
			expectedErrors:   1,
			expectedCircuits: 1,
		},
		{
			name: "duplicate parameter",
			input: `[lfo]
hz = 1
hz = 2`,
			expectedErrors:   1,
			expectedCircuits: 1,
		},
		{
			name: "empty circuit name",
			input: `[]
param = value`,
			expectedErrors:   2, // Empty circuit name + parameter outside circuit
			expectedCircuits: 0,
		},
		{
			name: "invalid parameter syntax",
			input: `[lfo]
invalid syntax line`,
			expectedErrors:   1,
			expectedCircuits: 1,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			config, errors := ParseConfig(tt.input)

			if len(errors) != tt.expectedErrors {
				t.Errorf("Expected %d errors, got %d: %v", tt.expectedErrors, len(errors), errors)
			}

			if len(config.Circuits) != tt.expectedCircuits {
				t.Errorf("Expected %d circuits, got %d", tt.expectedCircuits, len(config.Circuits))
			}
		})
	}
}

func TestValidateCircuitParameters(t *testing.T) {
	tests := []struct {
		name           string
		input          string
		expectedErrors int
	}{
		{
			name: "valid lfo parameters",
			input: `[lfo]
hz = 2
sine = O1`,
			expectedErrors: 0,
		},
		{
			name: "invalid lfo parameter",
			input: `[lfo]
invalid_param = value`,
			expectedErrors: 1,
		},
		{
			name: "unknown circuit type",
			input: `[unknown_circuit]
param = value`,
			expectedErrors: 1,
		},
		{
			name: "valid sequencer parameters",
			input: `[sequencer]
clock = I1
cv1 = O1`,
			expectedErrors: 0,
		},
		{
			name: "mixed valid and invalid",
			input: `[lfo]
hz = 2
invalid_param = value
sine = O1

[sequencer]
clock = I1
another_invalid = value`,
			expectedErrors: 2,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			config, parseErrors := ParseConfig(tt.input)
			if len(parseErrors) > 0 {
				t.Fatalf("Parse errors: %v", parseErrors)
			}

			errors := ValidateCircuitParameters(config)

			if len(errors) != tt.expectedErrors {
				t.Errorf("Expected %d validation errors, got %d: %v", tt.expectedErrors, len(errors), errors)
			}
		})
	}
}

func TestParseParameter(t *testing.T) {
	tests := []struct {
		name        string
		line        string
		lineNumber  int
		expectError bool
	}{
		{
			name:        "valid parameter",
			line:        "hz = 2",
			lineNumber:  1,
			expectError: false,
		},
		{
			name:        "parameter with spaces",
			line:        "  level  =  0.5  ",
			lineNumber:  2,
			expectError: false,
		},
		{
			name:        "parameter with inline comment",
			line:        "phase = 0  # phase setting",
			lineNumber:  3,
			expectError: false,
		},
		{
			name:        "invalid syntax - no equals",
			line:        "hz 2",
			lineNumber:  4,
			expectError: true,
		},
		{
			name:        "empty parameter name",
			line:        " = 2",
			lineNumber:  5,
			expectError: true,
		},
		{
			name:        "comment only line",
			line:        "# this is a comment",
			lineNumber:  6,
			expectError: false,
		},
		{
			name:        "empty line after comment removal",
			line:        "   # comment only",
			lineNumber:  7,
			expectError: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Create a fresh circuit for each test case
			circuit := &Circuit{
				Type:       "lfo",
				Parameters: make(map[string]Parameter),
			}

			err := parseParameter(tt.line, tt.lineNumber, circuit)

			if tt.expectError && err == nil {
				t.Error("Expected error but got none")
			}

			if !tt.expectError && err != nil {
				t.Errorf("Unexpected error: %v", err)
			}
		})
	}
}

func TestGetCircuitDefinition(t *testing.T) {
	tests := []struct {
		name         string
		circuitType  string
		expectExists bool
	}{
		{
			name:         "valid lfo circuit",
			circuitType:  "lfo",
			expectExists: true,
		},
		{
			name:         "valid sequencer circuit",
			circuitType:  "sequencer",
			expectExists: true,
		},
		{
			name:         "unknown circuit",
			circuitType:  "unknown_circuit",
			expectExists: false,
		},
		{
			name:         "empty circuit name",
			circuitType:  "",
			expectExists: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			def, exists := GetCircuitDefinition(tt.circuitType)

			if exists != tt.expectExists {
				t.Errorf("Expected exists=%v, got %v", tt.expectExists, exists)
			}

			if exists && def.Name != tt.circuitType {
				t.Errorf("Expected circuit name %s, got %s", tt.circuitType, def.Name)
			}
		})
	}
}

func TestComplexConfiguration(t *testing.T) {
	config := `# Complex DROID configuration
[p2b8]

# Clock generation
[lfo]
hz = 2
output = _BPM

# Euclidean rhythm
[euklid]
clock = _BPM
length = 16
beats = 4
output = _TRIGGER

# Envelope
[contour]
gate = _TRIGGER
attack = 0.1
release = 0.5
output = O1

# Random source
[random]
clock = _TRIGGER
minimum = 0
maximum = 1
output = O2`

	parsedConfig, parseErrors := ParseConfig(config)
	if len(parseErrors) > 0 {
		t.Fatalf("Parse errors: %v", parseErrors)
	}

	if len(parsedConfig.Circuits) != 5 {
		t.Errorf("Expected 5 circuits, got %d", len(parsedConfig.Circuits))
	}

	// Validate parameters
	validationErrors := ValidateCircuitParameters(parsedConfig)

	// Check if p2b8 is treated as a controller
	registry, err := getCircuitRegistry()
	if err != nil {
		t.Fatalf("Failed to get circuit registry: %v", err)
	}
	isController := registry.IsController("p2b8")

	if isController {
		// p2b8 is a valid controller, so we expect 0 validation errors
		if len(validationErrors) != 0 {
			t.Errorf("Expected 0 validation errors (p2b8 is a controller), got %d: %v", len(validationErrors), validationErrors)
		}
	} else {
		// p2b8 is not a controller, so we expect 1 error for the unknown circuit
		if len(validationErrors) != 1 {
			t.Errorf("Expected 1 validation error (p2b8), got %d: %v", len(validationErrors), validationErrors)
		}

		// Check that the error is about p2b8
		if len(validationErrors) > 0 && !strings.Contains(validationErrors[0].Error(), "p2b8") {
			t.Errorf("Expected error about p2b8, got: %v", validationErrors[0])
		}
	}
}

func TestValidateExpression(t *testing.T) {
	tests := []struct {
		name        string
		expression  string
		expectError bool
		description string
	}{
		// Valid simple expressions
		{
			name:        "simple number",
			expression:  "0.5",
			expectError: false,
			description: "Simple numeric value",
		},
		{
			name:        "simple variable",
			expression:  "_VARIABLE",
			expectError: false,
			description: "Simple variable reference",
		},
		{
			name:        "input reference",
			expression:  "I1",
			expectError: false,
			description: "Input reference",
		},
		{
			name:        "output reference",
			expression:  "O1",
			expectError: false,
			description: "Output reference",
		},
		{
			name:        "voltage reference",
			expression:  "5V",
			expectError: false,
			description: "Voltage reference",
		},

		// Valid A * B expressions
		{
			name:        "multiplication basic",
			expression:  "I5 * _ENV1_ATTENUVERTER_POT",
			expectError: false,
			description: "Basic multiplication from config",
		},
		{
			name:        "multiplication with negative",
			expression:  "_SAMPLE2_LEVEL * -5V",
			expectError: false,
			description: "Multiplication with negative value",
		},
		{
			name:        "multiplication with decimal",
			expression:  "_ENV1_ATTACK * _ATTACK_MAX",
			expectError: false,
			description: "Variable multiplication",
		},

		// Valid A + C expressions
		{
			name:        "addition basic",
			expression:  "0.03 + _DECAY_MAX",
			expectError: false,
			description: "Basic addition from config",
		},
		{
			name:        "addition variables",
			expression:  "_ENV1_ATTACK_POT + _ENV1_ATTENUVERTER",
			expectError: false,
			description: "Variable addition",
		},

		// Valid A * B + C expressions
		{
			name:        "full expression positive",
			expression:  "_ENV1_SWITCH_LED * -0.5 + _LED1",
			expectError: false,
			description: "Full A * B + C expression from config",
		},
		{
			name:        "full expression with decimals",
			expression:  "_ENV1_LIN_EXP * 0.25 + 0.5",
			expectError: false,
			description: "Full expression with decimal values",
		},
		{
			name:        "complex expression",
			expression:  "_ENV2_DECAY_POT_ABSBIPOLAR * -1 + _DECAY_MIN",
			expectError: false,
			description: "Complex expression from config",
		},

		// Valid expressions with spaces
		{
			name:        "spaced multiplication",
			expression:  "I5  *  _ENV1_ATTENUVERTER_POT",
			expectError: false,
			description: "Multiplication with extra spaces",
		},
		{
			name:        "spaced addition",
			expression:  "0.03  +  _DECAY_MAX",
			expectError: false,
			description: "Addition with extra spaces",
		},

		// Invalid expressions
		{
			name:        "empty expression",
			expression:  "",
			expectError: true,
			description: "Empty value should be invalid",
		},
		{
			name:        "division_basic",
			expression:  "A / B",
			expectError: false,
			description: "Division is supported",
		},
		{
			name:        "gate_register",
			expression:  "G1.1",
			expectError: false,
			description: "Gate registers are supported",
		},
		{
			name:        "too many terms",
			expression:  "A * B + C * D",
			expectError: true,
			description: "Too complex expression",
		},
		{
			name:        "invalid variable name",
			expression:  "123invalid",
			expectError: true,
			description: "Variable names can't start with numbers",
		},
		{
			name:        "missing operand",
			expression:  "A * + C",
			expectError: true,
			description: "Missing operand in multiplication",
		},
		{
			name:        "double operators",
			expression:  "A ** B",
			expectError: true,
			description: "Double operators not allowed",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := validateExpression(tt.expression, 1)

			if tt.expectError && err == nil {
				t.Errorf("Expected error for expression '%s' (%s), but got none", tt.expression, tt.description)
			}

			if !tt.expectError && err != nil {
				t.Errorf("Unexpected error for expression '%s' (%s): %v", tt.expression, tt.description, err)
			}
		})
	}
}

func TestParseConfigWithInvalidExpressions(t *testing.T) {
	input := `[lfo]
hz = A ** B`

	config, errors := ParseConfig(input)

	// Debug output
	t.Logf("Number of errors: %d", len(errors))
	for i, err := range errors {
		t.Logf("Error %d: %s", i, err.Error())
	}

	// Should have parsing errors due to invalid expression
	if len(errors) == 0 {
		t.Error("Expected parsing errors for invalid expression 'A ** B', but got none")
	} else {
		// Check that the error is about invalid expression format
		found := false
		for _, err := range errors {
			if strings.Contains(err.Error(), "invalid expression format") {
				found = true
				break
			}
		}
		if !found {
			t.Error("Expected error about invalid expression format, but didn't find one")
		}
	}

	// Should still parse the structure
	if len(config.Circuits) != 1 {
		t.Errorf("Expected 1 circuit, got %d", len(config.Circuits))
	}
}

func TestValidateCircuitCountLimits(t *testing.T) {
	tests := []struct {
		name        string
		config      string
		expectError bool
		description string
	}{
		{
			name: "valid_circuit_count",
			config: `[lfo]
hz = 1.0

[copy]
input = 0.5`,
			expectError: false,
			description: "Small number of circuits should be valid",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			config, _ := ParseConfig(tt.config)
			errors := ValidateCircuitCountLimits(config)

			if tt.expectError && len(errors) == 0 {
				t.Errorf("Expected circuit count validation errors for %s, but got none", tt.description)
			}

			if !tt.expectError && len(errors) > 0 {
				t.Errorf("Expected no circuit count validation errors for %s, but got %d: %v",
					tt.description, len(errors), errors)
			}
		})
	}
}

func TestValidateRegisterNumberRanges(t *testing.T) {
	tests := []struct {
		name        string
		config      string
		expectError bool
		description string
	}{
		{
			name: "valid_register_numbers",
			config: `[lfo]
output = O1

[copy]
output = O2`,
			expectError: false,
			description: "Valid register numbers should pass",
		},
		{
			name: "invalid_register_zero",
			config: `[lfo]
output = O0`,
			expectError: true,
			description: "Register number 0 should be invalid",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			config, _ := ParseConfig(tt.config)
			errors := ValidateRegisterNumberRanges(config)

			if tt.expectError && len(errors) == 0 {
				t.Errorf("Expected register range validation errors for %s, but got none", tt.description)
			}

			if !tt.expectError && len(errors) > 0 {
				t.Errorf("Expected no register range validation errors for %s, but got %d: %v",
					tt.description, len(errors), errors)
			}
		})
	}
}

func TestValidateRequiredInputs(t *testing.T) {
	tests := []struct {
		name        string
		config      string
		expectError bool
		description string
	}{
		{
			name: "valid_configuration_with_all_inputs",
			config: `[lfo]
hz = 1.0
output = O1

[copy]
input = 0.5
output = O2`,
			expectError: false,
			description: "Valid configuration should pass",
		},
		{
			name: "configuration_with_minimal_inputs",
			config: `[lfo]
output = O1`,
			expectError: false,
			description: "Configuration with minimal inputs should pass (conservative validation)",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			config, _ := ParseConfig(tt.config)
			errors := ValidateRequiredInputs(config)

			if tt.expectError && len(errors) == 0 {
				t.Errorf("Expected required input validation errors for %s, but got none", tt.description)
			}

			if !tt.expectError && len(errors) > 0 {
				t.Errorf("Expected no required input validation errors for %s, but got %d: %v",
					tt.description, len(errors), errors)
			}
		})
	}
}

func TestExtractCables(t *testing.T) {
	tests := []struct {
		name     string
		value    string
		expected []string
	}{
		{
			name:     "single_cable",
			value:    "_DECAY_MAX",
			expected: []string{"_DECAY_MAX"},
		},
		{
			name:     "cable_in_expression",
			value:    "_ENV1_ATTACK * _ATTACK_MAX",
			expected: []string{"_ENV1_ATTACK", "_ATTACK_MAX"},
		},
		{
			name:     "cable_with_arithmetic",
			value:    "_ENV1_SWITCH_LED * -0.5 + _LED1",
			expected: []string{"_ENV1_SWITCH_LED", "_LED1"},
		},
		{
			name:     "no_cables",
			value:    "I1 + O2 * 0.5",
			expected: []string{},
		},
		{
			name:     "mixed_identifiers",
			value:    "I1 * _CABLE1 + B1.1 * _CABLE2",
			expected: []string{"_CABLE1", "_CABLE2"},
		},
		{
			name:     "duplicate_cables",
			value:    "_CABLE1 + _CABLE1 * 2",
			expected: []string{"_CABLE1"},
		},
		{
			name:     "complex_expression",
			value:    "_ENV1_DECAY_POT_ABSBIPOLAR * -1 + _DECAY_MIN",
			expected: []string{"_ENV1_DECAY_POT_ABSBIPOLAR", "_DECAY_MIN"},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := extractCables(tt.value)

			if len(result) != len(tt.expected) {
				t.Errorf("Expected %d cables, got %d: %v", len(tt.expected), len(result), result)
				return
			}

			// Convert to maps for easier comparison (order doesn't matter)
			expectedMap := make(map[string]bool)
			for _, cable := range tt.expected {
				expectedMap[cable] = true
			}

			resultMap := make(map[string]bool)
			for _, cable := range result {
				resultMap[cable] = true
			}

			for cable := range expectedMap {
				if !resultMap[cable] {
					t.Errorf("Expected cable '%s' not found in result: %v", cable, result)
				}
			}

			for cable := range resultMap {
				if !expectedMap[cable] {
					t.Errorf("Unexpected cable '%s' found in result: %v", cable, result)
				}
			}
		})
	}
}

func TestValidateCableConnections(t *testing.T) {
	tests := []struct {
		name           string
		config         string
		expectedErrors int
		description    string
	}{
		{
			name: "valid_cable_connections",
			config: `[copy]
input = 1.0
output = _CABLE1

[copy]
input = _CABLE1
output = O1`,
			expectedErrors: 0,
			description:    "Valid cable with one output and one input should pass",
		},
		{
			name: "cable_multiple_inputs",
			config: `[copy]
input = 1.0
output = _CABLE1

[copy]
input = _CABLE1
output = O1

[copy]
input = _CABLE1 * 0.5
output = O2`,
			expectedErrors: 0,
			description:    "Cable with one output and multiple inputs should pass",
		},
		{
			name: "cable_no_inputs",
			config: `[copy]
input = 1.0
output = _UNUSED_CABLE`,
			expectedErrors: 1,
			description:    "Cable with output but no inputs should fail",
		},
		{
			name: "cable_no_output",
			config: `[copy]
input = _UNDEFINED_CABLE
output = O1`,
			expectedErrors: 1,
			description:    "Cable used as input but never assigned should fail",
		},
		{
			name: "cable_multiple_outputs",
			config: `[copy]
input = 1.0
output = _CABLE1

[copy]
input = 2.0
output = _CABLE1

[copy]
input = _CABLE1
output = O1`,
			expectedErrors: 1,
			description:    "Cable with multiple outputs should fail",
		},
		{
			name: "complex_valid_scenario",
			config: `[copy]
input = 0.75
output = _DECAY_MAX

[copy]
input = 0.03 + _DECAY_MAX
output = _DECAY_MIN

[pot]
outputscale = _DECAY_MAX
absbipolar = _ENV1_DECAY_POT_ABSBIPOLAR

[copy]
input = _ENV1_DECAY_POT_ABSBIPOLAR * -1 + _DECAY_MIN
output = _ENV1_DECAY_POT

[contour]
release = _ENV1_DECAY_POT
output = O1`,
			expectedErrors: 0,
			description:    "Complex valid cable scenario should pass",
		},
		{
			name: "mixed_errors",
			config: `[copy]
input = 1.0
output = _CABLE1

[copy]
input = 2.0
output = _CABLE1

[copy]
input = _UNDEFINED_CABLE
output = O1

[copy]
input = 3.0
output = _UNUSED_CABLE`,
			expectedErrors: 4,
			description:    "Multiple cable errors should be detected: duplicate output, unused cable, undefined cable, and unused cable from duplicate",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			config, parseErrors := ParseConfig(tt.config)
			if len(parseErrors) > 0 {
				t.Fatalf("Parse errors: %v", parseErrors)
			}

			errors := ValidateCableConnections(config)

			if len(errors) != tt.expectedErrors {
				t.Errorf("Expected %d cable validation errors for %s, got %d: %v",
					tt.expectedErrors, tt.description, len(errors), errors)
			}
		})
	}
}

func TestValidateOutputDestinations(t *testing.T) {
	tests := []struct {
		name        string
		config      string
		expectError bool
		description string
	}{
		{
			name: "valid_outputs_with_destinations",
			config: `[lfo]
hz = 1.0
output = O1

[copy]
input = 0.5
output = _SIGNAL`,
			expectError: false,
			description: "Valid outputs with destinations should pass",
		},
		{
			name: "valid_outputs_with_register_destinations",
			config: `[lfo]
output = R1

[copy]
output = B1.1`,
			expectError: false,
			description: "Valid outputs with register destinations should pass",
		},
		{
			name: "circuit_without_outputs",
			config: `[lfo]
hz = 1.0`,
			expectError: false,
			description: "Circuits without output assignments should pass (outputs are optional)",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			config, _ := ParseConfig(tt.config)
			errors := ValidateOutputDestinations(config)

			if tt.expectError && len(errors) == 0 {
				t.Errorf("Expected output destination validation errors for %s, but got none", tt.description)
			}

			if !tt.expectError && len(errors) > 0 {
				t.Errorf("Expected no output destination validation errors for %s, but got %d: %v",
					tt.description, len(errors), errors)
			}
		})
	}
}
