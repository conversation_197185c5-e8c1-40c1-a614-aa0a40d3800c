package droid

import (
	"testing"
)

func TestGetVersion(t *testing.T) {
	version := GetVersion()
	if version == "" {
		t.<PERSON><PERSON><PERSON>("GetVersion() returned empty string")
	}
	// Should contain version number
	if len(version) < 3 {
		t.<PERSON><PERSON><PERSON>("GetVersion() returned suspiciously short version: %s", version)
	}
}

func TestListDevices(t *testing.T) {
	devices, err := ListDevices()
	if err != nil {
		t.Fatalf("ListDevices() failed: %v", err)
	}

	// Should return a list (may be empty if no devices)
	if devices == nil {
		t.Error("ListDevices() returned nil slice")
	}

	// If devices exist, they should have valid properties
	for i, device := range devices {
		if device.Name == "" {
			t.<PERSON><PERSON>rf("Device %d has empty name", i)
		}
		if device.ID < 0 {
			t.Errorf("Device %d has negative ID: %d", i, device.ID)
		}
	}
}

func TestListDevicesWithOptions(t *testing.T) {
	// Test active only (default behavior)
	activeDevices, err := ListDevicesWithOptions(DeviceListOptions{
		ActiveOnly: true,
		ShowAll:    false,
	})
	if err != nil {
		t.Fatalf("ListDevicesWithOptions(active only) failed: %v", err)
	}

	// Test show all devices
	allDevices, err := ListDevicesWithOptions(DeviceListOptions{
		ActiveOnly: false,
		ShowAll:    true,
	})
	if err != nil {
		t.Fatalf("ListDevicesWithOptions(show all) failed: %v", err)
	}

	// All devices should be >= active devices
	if len(allDevices) < len(activeDevices) {
		t.Errorf("All devices (%d) should be >= active devices (%d)",
			len(allDevices), len(activeDevices))
	}

	// Active devices should all be marked as active
	for i, device := range activeDevices {
		if !device.IsActive {
			t.Errorf("Active device %d should be marked as active: %s", i, device.Name)
		}
	}
}

func TestDeviceListOptions(t *testing.T) {
	// Test default behavior (active only)
	defaultDevices, err := ListDevicesWithOptions(DeviceListOptions{
		ActiveOnly: false,
		ShowAll:    false, // This should show active only
	})
	if err != nil {
		t.Fatalf("ListDevicesWithOptions(default) failed: %v", err)
	}

	// Test explicit active only
	activeDevices, err := ListDevicesWithOptions(DeviceListOptions{
		ActiveOnly: true,
		ShowAll:    false,
	})
	if err != nil {
		t.Fatalf("ListDevicesWithOptions(active only) failed: %v", err)
	}

	// Should return same results
	if len(defaultDevices) != len(activeDevices) {
		t.Errorf("Default behavior should match active only: default=%d, active=%d",
			len(defaultDevices), len(activeDevices))
	}
}

func TestValidateBuffer(t *testing.T) {
	// Test empty buffer (should be valid)
	err := ValidateBuffer(nil)
	if err != nil {
		t.Errorf("ValidateBuffer(nil) should be valid, got: %v", err)
	}

	err = ValidateBuffer([]byte{})
	if err != nil {
		t.Errorf("ValidateBuffer(empty) should be valid, got: %v", err)
	}

	// Test simple valid config
	validConfig := []byte("[lfo]\nhz = 2\noutput = O1\n")
	err = ValidateBuffer(validConfig)
	if err != nil {
		t.Errorf("ValidateBuffer(valid config) should be valid, got: %v", err)
	}
}

func TestUploadFileWithOptions(t *testing.T) {
	// Test dry run with non-existent file (should fail gracefully)
	err := UploadFileWithOptions("nonexistent.ini", UploadOptions{
		DeviceName: "test",
		Verbose:    false,
		DryRun:     true,
	})
	if err == nil {
		t.Error("UploadFileWithOptions with non-existent file should fail")
	}
}

func TestUploadFile(t *testing.T) {
	// Test that UploadFile delegates to UploadFileWithOptions
	// We can't test actual upload without a device, but we can test the interface
	err := UploadFile("nonexistent.ini", "test", false, true)
	if err == nil {
		t.Error("UploadFile with non-existent file should fail")
	}
}

func TestValidateFileWithOptions(t *testing.T) {
	// Test with non-existent file
	result, err := ValidateFileWithOptions("nonexistent.ini", ValidationOptions{})
	if err != nil {
		t.Errorf("ValidateFileWithOptions should not return error: %v", err)
	}
	if result == nil {
		t.Error("ValidateFileWithOptions should return result even on validation failure")
	} else if result.IsValid {
		t.Error("ValidateFileWithOptions with non-existent file should return invalid result")
	} else if len(result.Errors) == 0 {
		t.Error("ValidateFileWithOptions with non-existent file should have errors")
	}
}

func TestGetConfigTemplate(t *testing.T) {
	// Test known templates
	knownTemplates := []string{"basic", "drums", "sequencer", "midi"}

	for _, templateName := range knownTemplates {
		template, err := GetConfigTemplate(templateName)
		if err != nil {
			t.Errorf("GetConfigTemplate(%s) failed: %v", templateName, err)
		}
		if template == "" {
			t.Errorf("GetConfigTemplate(%s) returned empty template", templateName)
		}
		// Should contain DROID configuration syntax
		if len(template) < 10 {
			t.Errorf("GetConfigTemplate(%s) returned suspiciously short template", templateName)
		}
	}

	// Test unknown template
	_, err := GetConfigTemplate("unknown")
	if err == nil {
		t.Error("GetConfigTemplate(unknown) should return error")
	}
}
