package circuits

import (
	"testing"
)

func TestNewRegistry(t *testing.T) {
	registry := NewRegistry()
	if registry == nil {
		t.<PERSON><PERSON>("NewRegistry() returned nil")
	}
	if registry.circuits == nil {
		t.<PERSON>al("NewRegistry() did not initialize circuits map")
	}
}

func TestGetDefaultRegistry(t *testing.T) {
	registry, err := GetDefaultRegistry()
	if err != nil {
		t.Fatalf("GetDefaultRegistry() failed: %v", err)
	}
	if registry == nil {
		t.Fatal("GetDefaultRegistry() returned nil registry")
	}

	// Should have loaded some circuits
	circuits := registry.ListCircuits()
	if len(circuits) == 0 {
		t.<PERSON><PERSON>("GetDefaultRegistry() loaded no circuits")
	}

	// Should have common circuits
	expectedCircuits := []string{"lfo", "button", "sequencer", "mixer"}
	for _, expected := range expectedCircuits {
		if _, exists := registry.GetCircuit(expected); !exists {
			t.<PERSON><PERSON>rf("Expected circuit '%s' not found", expected)
		}
	}
}

func TestGetCircuit(t *testing.T) {
	registry, err := GetDefaultRegistry()
	if err != nil {
		t.Fatalf("Failed to get registry: %v", err)
	}

	// Test existing circuit
	circuit, exists := registry.GetCircuit("lfo")
	if !exists {
		t.Fatal("Circuit 'lfo' should exist")
	}
	if circuit == nil {
		t.Fatal("GetCircuit returned nil circuit")
	}
	if circuit.Name != "lfo" {
		t.Errorf("Expected circuit name 'lfo', got '%s'", circuit.Name)
	}

	// Test non-existing circuit
	_, exists = registry.GetCircuit("nonexistent")
	if exists {
		t.Error("Non-existent circuit should not exist")
	}
}

func TestListCircuits(t *testing.T) {
	registry, err := GetDefaultRegistry()
	if err != nil {
		t.Fatalf("Failed to get registry: %v", err)
	}

	circuits := registry.ListCircuits()
	if len(circuits) == 0 {
		t.Fatal("ListCircuits() returned empty list")
	}

	// Should not contain duplicates
	seen := make(map[string]bool)
	for _, name := range circuits {
		if seen[name] {
			t.Errorf("Duplicate circuit name found: %s", name)
		}
		seen[name] = true
	}
}

func TestListCircuitsByCategory(t *testing.T) {
	registry, err := GetDefaultRegistry()
	if err != nil {
		t.Fatalf("Failed to get registry: %v", err)
	}

	categories := registry.ListCircuitsByCategory()
	if len(categories) == 0 {
		t.Fatal("ListCircuitsByCategory() returned empty map")
	}

	// Should have some expected categories (as they actually appear in firmware)
	expectedCategories := []string{"logic", "clock", "midi"}
	for _, expected := range expectedCategories {
		if circuits, exists := categories[expected]; !exists || len(circuits) == 0 {
			t.Errorf("Expected category '%s' not found or empty", expected)
		}
	}
}

func TestSearchCircuits(t *testing.T) {
	registry, err := GetDefaultRegistry()
	if err != nil {
		t.Fatalf("Failed to get registry: %v", err)
	}

	// Test search by name
	results := registry.SearchCircuits("lfo")
	if len(results) == 0 {
		t.Error("Search for 'lfo' should return results")
	}

	// Test search by description (case insensitive)
	results = registry.SearchCircuits("MIDI")
	if len(results) == 0 {
		t.Error("Search for 'MIDI' should return results")
	}

	// Test search with no results
	results = registry.SearchCircuits("nonexistentcircuit")
	if len(results) != 0 {
		t.Error("Search for non-existent circuit should return no results")
	}
}

func TestCircuitAliases(t *testing.T) {
	registry, err := GetDefaultRegistry()
	if err != nil {
		t.Fatalf("Failed to get registry: %v", err)
	}

	// Test that aliases work (if any circuits have aliases)
	// This is a basic test - specific aliases would need to be checked
	// based on the actual circuit definitions
	allCircuits := registry.ListCircuits()
	for _, name := range allCircuits {
		circuit, exists := registry.GetCircuit(name)
		if !exists {
			t.Errorf("Circuit '%s' from list should be gettable", name)
		}
		if circuit.Name != name {
			t.Errorf("Circuit name mismatch: expected '%s', got '%s'", name, circuit.Name)
		}
	}
}
