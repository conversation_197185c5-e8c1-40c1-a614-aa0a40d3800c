package circuits

import (
	"fmt"
	"sort"
	"strings"

	"github.com/jedib0t/go-pretty/v6/table"
	"github.com/jedib0t/go-pretty/v6/text"
)

// ParameterType represents the type of a circuit parameter
type ParameterType string

const (
	TypeCV      ParameterType = "CV"      // Continuous voltage
	TypeGate    ParameterType = "Gate"    // Gate signal
	TypeTrigger ParameterType = "Trigger" // Trigger signal
	TypePitch   ParameterType = "Pitch"   // 1V/Oct pitch
	TypeBipolar ParameterType = "Bipolar" // Bipolar CV (-5V to +5V)
	TypeInteger ParameterType = "Integer" // Integer value
	TypeFloat   ParameterType = "Float"   // Floating point value
	TypeButton  ParameterType = "Button"  // Button input
	TypeLED     ParameterType = "LED"     // LED output
	TypePot     ParameterType = "Pot"     // Potentiometer
	TypeSwitch  ParameterType = "Switch"  // Switch input
)

// Range represents the valid range for a parameter
type Range struct {
	Min     float64 `json:"min"`
	Max     float64 `json:"max"`
	Default float64 `json:"default"`
}

// Parameter represents a circuit input or output parameter
type Parameter struct {
	Name        string        `json:"name"`
	Type        ParameterType `json:"type"`
	Description string        `json:"description"`
	Default     string        `json:"default,omitempty"`
	Range       *Range        `json:"range,omitempty"`
	Required    bool          `json:"required"`
	Aliases     []string      `json:"aliases,omitempty"`
}

// Display interface implementations for Parameter
func (p *Parameter) GetName() string        { return p.Name }
func (p *Parameter) GetType() string        { return string(p.Type) }
func (p *Parameter) GetDescription() string { return p.Description }
func (p *Parameter) GetDefault() string     { return p.Default }

// DroidFirmwareParameter represents a parameter from the official Droid Forge firmware
type DroidFirmwareParameter struct {
	Name        string `json:"name"`
	Short       string `json:"short"`
	Type        string `json:"type"`
	Default     string `json:"default"`
	Description string `json:"description"`
	Essential   int    `json:"essential"`
	RamHint     string `json:"ramhint"`
	Prefix      string `json:"prefix,omitempty"`
	Count       int    `json:"count,omitempty"`
	StartAt     int    `json:"start_at,omitempty"`
}

// DroidFirmwareCircuit represents a circuit from the official Droid Forge firmware
type DroidFirmwareCircuit struct {
	Category    string                   `json:"category"`
	Title       string                   `json:"title"`
	Description string                   `json:"description"`
	Inputs      []DroidFirmwareParameter `json:"inputs"`
	Outputs     []DroidFirmwareParameter `json:"outputs"`
	Presets     int                      `json:"presets"`
	Manual      int                      `json:"manual"`
	RamSize     int                      `json:"ramsize"`
}

// DroidFirmwareController represents a controller from the official Droid Forge firmware
type DroidFirmwareController struct {
	RamSize int `json:"ramsize"`
}

// DroidFirmware represents the complete official Droid Forge firmware structure
type DroidFirmware struct {
	FirmwareVersion string                             `json:"firmware_version"`
	Circuits        map[string]DroidFirmwareCircuit    `json:"circuits"`
	Controllers     map[string]DroidFirmwareController `json:"controllers"`
}

// Example represents a usage example for a circuit
type Example struct {
	Title       string `json:"title"`
	Description string `json:"description"`
	Code        string `json:"code"`
}

// Display interface implementations for Example
func (e *Example) GetTitle() string       { return e.Title }
func (e *Example) GetDescription() string { return e.Description }
func (e *Example) GetCode() string        { return e.Code }

// Circuit represents a DROID circuit definition
type Circuit struct {
	Name        string               `json:"name"`
	Description string               `json:"description"`
	Category    string               `json:"category"`
	Inputs      map[string]Parameter `json:"inputs"`
	Outputs     map[string]Parameter `json:"outputs"`
	Examples    []Example            `json:"examples"`
	Aliases     []string             `json:"aliases,omitempty"`
	Page        int                  `json:"page,omitempty"` // Manual page reference
}

// GetParameter returns a parameter by name from inputs or outputs
func (c *Circuit) GetParameter(name string) (*Parameter, bool) {
	if param, exists := c.Inputs[name]; exists {
		return &param, true
	}
	if param, exists := c.Outputs[name]; exists {
		return &param, true
	}

	// Check aliases
	for _, param := range c.Inputs {
		for _, alias := range param.Aliases {
			if alias == name {
				return &param, true
			}
		}
	}
	for _, param := range c.Outputs {
		for _, alias := range param.Aliases {
			if alias == name {
				return &param, true
			}
		}
	}

	return nil, false
}

// ValidateParameter checks if a parameter value is valid
func (p *Parameter) ValidateParameter(value string) error {
	if p.Required && value == "" {
		return fmt.Errorf("parameter '%s' is required", p.Name)
	}

	// Add more validation logic here based on type and range
	return nil
}

// FormatDocumentation returns formatted documentation for the circuit
func (c *Circuit) FormatDocumentation() string {
	return c.FormatDocumentationWithOptions(DocumentationOptions{
		UseColor:     false,
		ShowDefaults: true,
		Style:        StylePlain,
	})
}

// colorizeText adds ANSI color codes to text
func colorizeText(text, color string) string {
	colors := map[string]string{
		"red":     "\x1b[31m",
		"green":   "\x1b[32m",
		"yellow":  "\x1b[33m",
		"blue":    "\x1b[34m",
		"magenta": "\x1b[35m",
		"cyan":    "\x1b[36m",
		"white":   "\x1b[37m",
		"bold":    "\x1b[1m",
	}

	reset := "\x1b[0m"
	if colorCode, exists := colors[color]; exists {
		return colorCode + text + reset
	}
	return text
}

// ElementType represents different types of elements that can be colorized
type ElementType int

const (
	ElementParameterName ElementType = iota
	ElementParameterType
	ElementSectionHeader
	ElementCircuitTitle
	ElementCategory
)

// DisplayStyle represents different documentation display styles
type DisplayStyle int

const (
	StylePlain DisplayStyle = iota
	StyleColorized
)

// DocumentationOptions configures how circuit documentation is formatted
type DocumentationOptions struct {
	UseColor     bool
	ShowDefaults bool
	Style        DisplayStyle
}

// colorizeElement provides unified colorization for different element types
func colorizeElement(text string, elementType ElementType) string {
	switch elementType {
	case ElementParameterName:
		return colorizeText(text, "blue")
	case ElementParameterType:
		return colorizeText(text, "green")
	case ElementSectionHeader:
		return colorizeText(text, "cyan")
	case ElementCircuitTitle:
		return colorizeText(text, "bold")
	case ElementCategory:
		return colorizeText(text, "yellow")
	default:
		return text
	}
}

// getCleanTableHeaders returns clean table headers without emojis
func getCleanTableHeaders(showDefaults bool) []string {
	if showDefaults {
		return []string{"Parameter", "Type", "Default", "Description"}
	}
	return []string{"Parameter", "Type", "Description"}
}

// getSortedParameterNames returns parameter names sorted alphabetically
func getSortedParameterNames(params map[string]Parameter) []string {
	names := make([]string, 0, len(params))
	for name := range params {
		names = append(names, name)
	}
	sort.Strings(names)
	return names
}

// FormatSummary returns a concise summary of the circuit
func (c *Circuit) FormatSummary() string {
	inputCount := len(c.Inputs)
	outputCount := len(c.Outputs)

	return fmt.Sprintf("%s - %s (%d inputs, %d outputs)",
		c.Name, c.Description, inputCount, outputCount)
}

// FormatColorizedDocumentation returns beautifully formatted documentation with colors but no emojis
func (c *Circuit) FormatColorizedDocumentation() string {
	return c.FormatDocumentationWithOptions(DocumentationOptions{
		UseColor:     true,
		ShowDefaults: true,
		Style:        StyleColorized,
	})
}

// formatParameterTableWithStyle is a unified table formatter that eliminates duplication
func (c *Circuit) formatParameterTableWithStyle(params map[string]Parameter, showDefaults bool, style string) string {
	if len(params) == 0 {
		return ""
	}

	// Create table writer
	t := table.NewWriter()

	// Configure table style
	t.SetStyle(table.Style{
		Name: "DroidParameters",
		Box: table.BoxStyle{
			Left:           "  ",
			Right:          "",
			MiddleVertical: " ",
			PaddingLeft:    "",
			PaddingRight:   " ",
		},
		Format: table.FormatOptions{
			Header: text.FormatUpper,
			Row:    text.FormatDefault,
		},
		Options: table.Options{
			DrawBorder:      false,
			SeparateColumns: true,
			SeparateHeader:  true,
			SeparateRows:    false,
		},
	})

	// Set up headers based on style
	headers := getCleanTableHeaders(showDefaults)

	// Convert headers to table.Row
	headerRow := make(table.Row, len(headers))
	for i, header := range headers {
		headerRow[i] = header
	}
	t.AppendHeader(headerRow)

	// Set column configurations
	if showDefaults {
		t.SetColumnConfigs([]table.ColumnConfig{
			{Number: 1, WidthMax: 15, Align: text.AlignLeft},
			{Number: 2, WidthMax: 10, Align: text.AlignLeft},
			{Number: 3, WidthMax: 10, Align: text.AlignLeft},
			{Number: 4, WidthMax: 55, Align: text.AlignLeft, WidthMaxEnforcer: text.WrapSoft},
		})
	} else {
		t.SetColumnConfigs([]table.ColumnConfig{
			{Number: 1, WidthMax: 15, Align: text.AlignLeft},
			{Number: 2, WidthMax: 10, Align: text.AlignLeft},
			{Number: 3, WidthMax: 65, Align: text.AlignLeft, WidthMaxEnforcer: text.WrapSoft},
		})
	}

	// Add rows with appropriate formatting in alphabetical order
	sortedNames := getSortedParameterNames(params)
	for _, name := range sortedNames {
		param := params[name]
		var paramName, paramType string

		if style == "colorized" {
			paramName = colorizeElement(name, ElementParameterName)
			paramType = colorizeElement(string(param.Type), ElementParameterType)
		} else {
			// Default/plain style
			paramName = name
			paramType = string(param.Type)
		}

		if showDefaults {
			defaultValue := param.Default
			if defaultValue == "" {
				defaultValue = "—"
			}
			t.AppendRow(table.Row{paramName, paramType, defaultValue, param.Description})
		} else {
			t.AppendRow(table.Row{paramName, paramType, param.Description})
		}
	}

	return t.Render()
}

// FormatDocumentationWithOptions returns formatted documentation using the specified options
func (c *Circuit) FormatDocumentationWithOptions(options DocumentationOptions) string {
	useColor := options.UseColor || options.Style == StyleColorized

	// Create header
	var title string
	if useColor {
		title = fmt.Sprintf("%s – %s", colorizeElement(strings.ToUpper(c.Name), ElementCircuitTitle), c.Description)
	} else {
		title = fmt.Sprintf("%s – %s", strings.ToLower(c.Name), c.Description)
	}

	doc := title + "\n\n"

	// Add category
	if useColor {
		doc += fmt.Sprintf("Category: %s\n\n", colorizeElement(c.Category, ElementCategory))
	} else {
		doc += fmt.Sprintf("Category: %s\n\n", c.Category)
	}

	// Add inputs
	if len(c.Inputs) > 0 {
		if useColor {
			doc += colorizeElement("INPUTS", ElementSectionHeader) + "\n"
		} else {
			doc += "INPUTS:\n"
		}
		doc += c.formatParameterTableWithStyle(c.Inputs, options.ShowDefaults, getStyleString(useColor))
		doc += "\n"
	}

	// Add outputs
	if len(c.Outputs) > 0 {
		if useColor {
			doc += colorizeElement("OUTPUTS", ElementSectionHeader) + "\n"
		} else {
			doc += "OUTPUTS:\n"
		}
		doc += c.formatParameterTableWithStyle(c.Outputs, false, getStyleString(useColor))
		doc += "\n"
	}

	// Add examples
	if len(c.Examples) > 0 {
		if useColor {
			doc += colorizeElement("EXAMPLES", ElementSectionHeader) + "\n"
		} else {
			doc += "EXAMPLES:\n"
		}
		for _, example := range c.Examples {
			if useColor {
				doc += fmt.Sprintf("  %s\n", colorizeText(example.Title, "yellow"))
			} else {
				doc += fmt.Sprintf("  %s:\n\n", example.Title)
			}
			if example.Description != "" {
				doc += fmt.Sprintf("     %s\n", example.Description)
			}
			doc += "\n"
			// Indent code
			lines := strings.Split(example.Code, "\n")
			for _, line := range lines {
				if line != "" {
					if useColor {
						doc += fmt.Sprintf("     %s\n", line)
					} else {
						doc += fmt.Sprintf("    %s\n", line)
					}
				}
			}
			doc += "\n"
		}
	}

	return doc
}

// getStyleString returns the appropriate style string for the table formatter
func getStyleString(useColor bool) string {
	if useColor {
		return "colorized"
	}
	return "plain"
}
