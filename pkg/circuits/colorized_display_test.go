package circuits

import (
	"strings"
	"testing"
)

func TestColorizedCircuitDisplay(t *testing.T) {
	tests := []struct {
		name     string
		circuit  *Circuit
		expected []string // Expected elements in output
	}{
		{
			name: "circuit with colorized output",
			circuit: &Circuit{
				Name:        "lfo",
				Description: "Low frequency oscillator with amazing features",
				Category:    "modulation",
				Inputs: map[string]Parameter{
					"hz": {
						Name:        "hz",
						Type:        "Float",
						Description: "Frequency in Hertz",
						Default:     "1",
					},
				},
				Outputs: map[string]Parameter{
					"sine": {
						Name:        "sine",
						Type:        "CV",
						Description: "Sine wave output",
					},
				},
			},
			expected: []string{
				"LFO",        // Circuit name in title
				"modulation", // Category
				"INPUTS",     // Section header
				"OUTPUTS",    // Section header
				"hz",         // Parameter name
				"sine",       // Output name
				"Float",      // Type without icon
				"CV",         // CV type without icon
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := tt.circuit.FormatColorizedDocumentation()

			for _, expected := range tt.expected {
				if !strings.Contains(result, expected) {
					t.Errorf("Colorized documentation missing expected element: %q", expected)
				}
			}
		})
	}
}

func TestParameterNameColorization(t *testing.T) {
	tests := []struct {
		name     string
		input    string
		expected string
	}{
		{
			name:     "parameter name in blue",
			input:    "hz",
			expected: "\x1b[34mhz\x1b[0m", // ANSI blue
		},
		{
			name:     "parameter name in blue",
			input:    "output",
			expected: "\x1b[34moutput\x1b[0m", // ANSI blue
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := colorizeElement(tt.input, ElementParameterName)
			if result != tt.expected {
				t.Errorf("colorizeElement(%q, ElementParameterName) = %q, want %q", tt.input, result, tt.expected)
			}
		})
	}
}

func TestParameterTypeColorization(t *testing.T) {
	tests := []struct {
		name     string
		input    string
		expected string
	}{
		{
			name:     "CV type in green",
			input:    "CV",
			expected: "\x1b[32mCV\x1b[0m", // ANSI green
		},
		{
			name:     "Float type in green",
			input:    "Float",
			expected: "\x1b[32mFloat\x1b[0m", // ANSI green
		},
		{
			name:     "Gate type in green",
			input:    "Gate",
			expected: "\x1b[32mGate\x1b[0m", // ANSI green
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := colorizeElement(tt.input, ElementParameterType)
			if result != tt.expected {
				t.Errorf("colorizeElement(%q, ElementParameterType) = %q, want %q", tt.input, result, tt.expected)
			}
		})
	}
}

func TestSectionHeaderColorization(t *testing.T) {
	tests := []struct {
		name     string
		input    string
		expected string
	}{
		{
			name:     "inputs header in cyan",
			input:    "INPUTS",
			expected: "\x1b[36mINPUTS\x1b[0m", // ANSI cyan
		},
		{
			name:     "outputs header in cyan",
			input:    "OUTPUTS",
			expected: "\x1b[36mOUTPUTS\x1b[0m", // ANSI cyan
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := colorizeElement(tt.input, ElementSectionHeader)
			if result != tt.expected {
				t.Errorf("colorizeElement(%q, ElementSectionHeader) = %q, want %q", tt.input, result, tt.expected)
			}
		})
	}
}

func TestCircuitTitleColorization(t *testing.T) {
	tests := []struct {
		name     string
		input    string
		expected string
	}{
		{
			name:     "circuit title in bold",
			input:    "LFO",
			expected: "\x1b[1mLFO\x1b[0m", // ANSI bold
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := colorizeElement(tt.input, ElementCircuitTitle)
			if result != tt.expected {
				t.Errorf("colorizeElement(%q, ElementCircuitTitle) = %q, want %q", tt.input, result, tt.expected)
			}
		})
	}
}

func TestCategoryColorization(t *testing.T) {
	tests := []struct {
		name     string
		input    string
		expected string
	}{
		{
			name:     "category in yellow",
			input:    "modulation",
			expected: "\x1b[33mmodulation\x1b[0m", // ANSI yellow
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := colorizeElement(tt.input, ElementCategory)
			if result != tt.expected {
				t.Errorf("colorizeElement(%q, ElementCategory) = %q, want %q", tt.input, result, tt.expected)
			}
		})
	}
}

func TestCleanTableHeaders(t *testing.T) {
	tests := []struct {
		name         string
		showDefaults bool
		expected     []string
	}{
		{
			name:         "headers with defaults",
			showDefaults: true,
			expected:     []string{"Parameter", "Type", "Default", "Description"},
		},
		{
			name:         "headers without defaults",
			showDefaults: false,
			expected:     []string{"Parameter", "Type", "Description"},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			headers := getCleanTableHeaders(tt.showDefaults)
			for i, expected := range tt.expected {
				if i >= len(headers) || headers[i] != expected {
					t.Errorf("getCleanTableHeaders(%v) header %d = %q, want %q",
						tt.showDefaults, i, headers[i], expected)
				}
			}
		})
	}
}

func TestParameterSorting(t *testing.T) {
	tests := []struct {
		name     string
		circuit  *Circuit
		expected []string // Expected parameter order
	}{
		{
			name: "inputs sorted alphabetically",
			circuit: &Circuit{
				Name: "test",
				Inputs: map[string]Parameter{
					"zebra": {Name: "zebra", Type: "CV", Description: "Last input"},
					"alpha": {Name: "alpha", Type: "Gate", Description: "First input"},
					"beta":  {Name: "beta", Type: "Float", Description: "Second input"},
				},
			},
			expected: []string{"alpha", "beta", "zebra"}, // Alphabetical order
		},
		{
			name: "outputs sorted alphabetically",
			circuit: &Circuit{
				Name: "test",
				Outputs: map[string]Parameter{
					"output3": {Name: "output3", Type: "CV", Description: "Third output"},
					"output1": {Name: "output1", Type: "Gate", Description: "First output"},
					"output2": {Name: "output2", Type: "Float", Description: "Second output"},
				},
			},
			expected: []string{"output1", "output2", "output3"}, // Alphabetical order
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			var result string
			if len(tt.circuit.Inputs) > 0 {
				result = tt.circuit.FormatColorizedDocumentation()
			} else {
				result = tt.circuit.FormatColorizedDocumentation()
			}

			// Check that parameters appear in alphabetical order
			lastIndex := -1
			for _, expectedParam := range tt.expected {
				currentIndex := strings.Index(result, expectedParam)
				if currentIndex == -1 {
					t.Errorf("Parameter %q not found in output", expectedParam)
					continue
				}
				if currentIndex <= lastIndex {
					t.Errorf("Parameter %q appears before previous parameter (not in alphabetical order)", expectedParam)
				}
				lastIndex = currentIndex
			}
		})
	}
}

func TestSortedParameterNames(t *testing.T) {
	tests := []struct {
		name     string
		params   map[string]Parameter
		expected []string
	}{
		{
			name: "basic sorting",
			params: map[string]Parameter{
				"zebra": {Name: "zebra"},
				"alpha": {Name: "alpha"},
				"beta":  {Name: "beta"},
			},
			expected: []string{"alpha", "beta", "zebra"},
		},
		{
			name: "ASCII sorting (uppercase before lowercase)",
			params: map[string]Parameter{
				"Zebra": {Name: "Zebra"},
				"alpha": {Name: "alpha"},
				"Beta":  {Name: "Beta"},
			},
			expected: []string{"Beta", "Zebra", "alpha"}, // ASCII order: uppercase before lowercase
		},
		{
			name:     "empty map",
			params:   map[string]Parameter{},
			expected: []string{},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := getSortedParameterNames(tt.params)
			if len(result) != len(tt.expected) {
				t.Errorf("getSortedParameterNames() length = %d, want %d", len(result), len(tt.expected))
				return
			}
			for i, expected := range tt.expected {
				if i >= len(result) || result[i] != expected {
					t.Errorf("getSortedParameterNames() [%d] = %q, want %q", i, result[i], expected)
				}
			}
		})
	}
}
