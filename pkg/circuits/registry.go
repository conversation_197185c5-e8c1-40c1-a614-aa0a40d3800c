package circuits

import (
	"embed"
	"encoding/json"
	"fmt"
	"strings"
	"sync"
)

//go:embed embedded/droid-firmware.json
var embeddedFirmware embed.FS

// Registry holds all circuit definitions
type Registry struct {
	circuits    map[string]*Circuit
	controllers map[string]*DroidFirmwareController
	firmware    *DroidFirmware
}

// NewRegistry creates a new circuit registry
func NewRegistry() *Registry {
	return &Registry{
		circuits:    make(map[string]*Circuit),
		controllers: make(map[string]*DroidFirmwareController),
	}
}

// LoadEmbeddedFirmware loads the official Droid Forge firmware definitions
func (r *Registry) LoadEmbeddedFirmware() error {
	data, err := embeddedFirmware.ReadFile("embedded/droid-firmware.json")
	if err != nil {
		return fmt.Errorf("failed to read firmware file: %w", err)
	}

	var firmware DroidFirmware
	if err := json.Unmarshal(data, &firmware); err != nil {
		return fmt.Errorf("failed to parse firmware file: %w", err)
	}

	r.firmware = &firmware

	// Convert firmware circuits to our Circuit format
	for name, firmwareCircuit := range firmware.Circuits {
		circuit := &Circuit{
			Name:        name,
			Description: SanitizeLatexDescription(firmwareCircuit.Title + "\n\n" + firmwareCircuit.Description),
			Category:    firmwareCircuit.Category,
			Inputs:      make(map[string]Parameter),
			Outputs:     make(map[string]Parameter),
			Page:        firmwareCircuit.Manual,
		}

		// Convert inputs
		for _, input := range firmwareCircuit.Inputs {
			r.addParametersFromFirmware(circuit.Inputs, input, false)
		}

		// Convert outputs
		for _, output := range firmwareCircuit.Outputs {
			r.addParametersFromFirmware(circuit.Outputs, output, true)
		}

		r.circuits[name] = circuit
	}

	// Load controllers
	for name, controller := range firmware.Controllers {
		r.controllers[name] = &controller
	}

	return nil
}

// addParametersFromFirmware converts firmware parameters to our Parameter format
func (r *Registry) addParametersFromFirmware(params map[string]Parameter, firmwareParam DroidFirmwareParameter, isOutput bool) {
	// Handle parameters with prefix/count (like bit1...bit12)
	if firmwareParam.Prefix != "" && firmwareParam.Count > 0 {
		startAt := firmwareParam.StartAt
		if startAt == 0 {
			startAt = 1
		}
		for i := 0; i < firmwareParam.Count; i++ {
			paramName := fmt.Sprintf("%s%d", firmwareParam.Prefix, startAt+i)
			params[paramName] = Parameter{
				Name:        paramName,
				Type:        r.convertFirmwareType(firmwareParam.Type),
				Description: SanitizeParameterDescription(firmwareParam.Description),
				Default:     firmwareParam.Default,
				Required:    !isOutput && firmwareParam.Essential > 1,
			}
		}
	} else {
		// Regular parameter
		params[firmwareParam.Name] = Parameter{
			Name:        firmwareParam.Name,
			Type:        r.convertFirmwareType(firmwareParam.Type),
			Description: SanitizeParameterDescription(firmwareParam.Description),
			Default:     firmwareParam.Default,
			Required:    !isOutput && firmwareParam.Essential > 1,
		}
	}
}

// convertFirmwareType converts firmware parameter types to our ParameterType
func (r *Registry) convertFirmwareType(firmwareType string) ParameterType {
	switch strings.ToLower(firmwareType) {
	case "cv":
		return TypeCV
	case "gate":
		return TypeGate
	case "trigger":
		return TypeTrigger
	case "bipolar":
		return TypeBipolar
	case "integer":
		return TypeInteger
	case "fraction":
		return TypeFloat
	default:
		return TypeCV // Default fallback
	}
}

// GetCircuit returns a circuit by name
func (r *Registry) GetCircuit(name string) (*Circuit, bool) {
	circuit, exists := r.circuits[name]
	return circuit, exists
}

// ListCircuits returns all circuit names
func (r *Registry) ListCircuits() []string {
	var names []string
	seen := make(map[string]bool)

	for name, circuit := range r.circuits {
		// Only include the primary name, not aliases
		if name == circuit.Name && !seen[name] {
			names = append(names, name)
			seen[name] = true
		}
	}

	return names
}

// ListCircuitsByCategory returns circuits grouped by category
func (r *Registry) ListCircuitsByCategory() map[string][]string {
	categories := make(map[string][]string)
	seen := make(map[string]bool)

	for name, circuit := range r.circuits {
		// Only include the primary name, not aliases
		if name == circuit.Name && !seen[name] {
			categories[circuit.Category] = append(categories[circuit.Category], name)
			seen[name] = true
		}
	}

	return categories
}

// SearchCircuits searches for circuits by name or description
func (r *Registry) SearchCircuits(query string) []*Circuit {
	var results []*Circuit
	seen := make(map[string]bool)
	query = strings.ToLower(query)

	for name, circuit := range r.circuits {
		// Only include the primary name, not aliases
		if name != circuit.Name || seen[circuit.Name] {
			continue
		}

		if strings.Contains(strings.ToLower(circuit.Name), query) ||
			strings.Contains(strings.ToLower(circuit.Description), query) {
			results = append(results, circuit)
			seen[circuit.Name] = true
		}
	}

	return results
}

// IsController checks if a circuit type is actually a controller
func (r *Registry) IsController(name string) bool {
	_, exists := r.controllers[name]
	return exists
}

var (
	defaultRegistry *Registry
	registryOnce    sync.Once
	registryError   error
)

// GetDefaultRegistry returns a cached registry with all embedded circuits loaded
func GetDefaultRegistry() (*Registry, error) {
	registryOnce.Do(func() {
		registry := NewRegistry()
		if err := registry.LoadEmbeddedFirmware(); err != nil {
			registryError = err
			return
		}
		defaultRegistry = registry
	})

	if registryError != nil {
		return nil, registryError
	}

	return defaultRegistry, nil
}

// SanitizeLatexDescription removes LaTeX formatting from descriptions
func SanitizeLatexDescription(description string) string {
	// Convert patch examples to clean format (keep them inline)
	description = convertPatchExamples(description)

	// Remove LaTeX commands and formatting
	description = removeLatexCommands(description)

	// Clean up whitespace
	description = cleanupWhitespace(description)

	return description
}

// SanitizeParameterDescription sanitizes parameter descriptions for table display
func SanitizeParameterDescription(description string) string {
	// Remove LaTeX commands and formatting
	description = removeLatexCommands(description)

	// Normalize line breaks - remove arbitrary breaks and rejoin sentences
	// This is crucial for go-pretty to wrap text properly at word boundaries
	description = normalizeParameterText(description)

	// Clean up whitespace
	description = strings.TrimSpace(description)

	// Replace multiple spaces with single spaces
	for strings.Contains(description, "  ") {
		description = strings.ReplaceAll(description, "  ", " ")
	}

	return description
}

// normalizeParameterText removes arbitrary line breaks and creates clean continuous text
func normalizeParameterText(text string) string {
	lines := strings.Split(text, "\n")
	var result strings.Builder

	for _, line := range lines {
		trimmed := strings.TrimSpace(line)

		// Skip empty lines
		if trimmed == "" {
			continue
		}

		// Add to result with space separation
		if result.Len() > 0 {
			result.WriteString(" ")
		}
		result.WriteString(trimmed)
	}

	return result.String()
}

// convertPatchExamples converts LaTeX patch examples to clean inline format
func convertPatchExamples(description string) string {
	lines := strings.Split(description, "\n")
	var result strings.Builder
	inExample := false

	for _, line := range lines {
		if strings.Contains(line, "\\begin{droidini}") {
			inExample = true
			result.WriteString("\n```\n")
			continue
		}

		if strings.Contains(line, "\\end{droidini}") {
			if inExample {
				result.WriteString("```\n\n")
			}
			inExample = false
			continue
		}

		if inExample {
			// Remove the \immediate\write\patchexamples{...} prefix if present
			cleanLine := line
			if strings.Contains(line, "\\immediate\\write\\patchexamples{") {
				// Extract the actual config line after the }
				parts := strings.Split(line, "}")
				if len(parts) > 1 {
					cleanLine = strings.TrimSpace(parts[len(parts)-1])
				}
			}

			if cleanLine != "" {
				result.WriteString(cleanLine)
				result.WriteString("\n")
			}
		} else {
			result.WriteString(line)
			result.WriteString("\n")
		}
	}

	return result.String()
}

// removeLatexCommands removes LaTeX commands and formatting
func removeLatexCommands(text string) string {
	// Remove common LaTeX commands
	replacements := map[string]string{
		// Text formatting
		`{\t `:                 "",
		`}`:                    "",
		`{\it `:                "",
		`{\bf `:                "",
		`{\footnotesize`:       "",
		`\textcolor{red}{`:     "",
		`\textcolor{red}{\bf `: "",

		// Math mode
		`$`:      "",
		`\infty`: "∞",
		`^\circ`: "°",
		`\times`: "×",
		`\pm`:    "±",
		`\sharp`: "♯",
		`\flat`:  "♭",

		// Special characters
		`\&`:           "&",
		`\%`:           "%",
		`\#`:           "#",
		`\_`:           "_",
		`\~`:           "~",
		`\\`:           "\n",
		`\clearpage`:   "",
		`\columnbreak`: "",
		`\medskip`:     "",
		`\hrule`:       "",

		// Circuit references
		`\circuit{`:  "",
		`\droid`:     "DROID",
		`\meighteen`: "M18",
		`\msixteen`:  "M16",
		`\nth{`:      "",

		// Subsections
		`\subsubsection*{`: "## ",
		`\subsection*{`:    "# ",
		`\section*{`:       "# ",

		// Remove tildes used for non-breaking spaces
		`~`: " ",
	}

	result := text
	for old, new := range replacements {
		result = strings.ReplaceAll(result, old, new)
	}

	// Remove LaTeX environments
	envPatterns := []string{
		`\begin{itemize}`,
		`\end{itemize}`,
		`\begin{tabular}{.*?}`,
		`\end{tabular}`,
		`\begin{center}`,
		`\end{center}`,
		`\begin{lilypond}.*?\end{lilypond}`,
		`\item `,
		`\hline`,
		`\jacktablerow{.*?}`,
	}

	for _, pattern := range envPatterns {
		result = strings.ReplaceAll(result, pattern, "")
	}

	// Remove remaining curly braces and backslashes from LaTeX commands
	result = removeBracedCommands(result)

	return result
}

// removeBracedCommands removes LaTeX commands with braces like \command{content}
func removeBracedCommands(text string) string {
	// Simple state machine to remove \command{...} patterns
	var result strings.Builder
	i := 0

	for i < len(text) {
		if i < len(text)-1 && text[i] == '\\' {
			// Found a backslash, look for command
			j := i + 1
			// Skip command name (letters)
			for j < len(text) && ((text[j] >= 'a' && text[j] <= 'z') || (text[j] >= 'A' && text[j] <= 'Z')) {
				j++
			}

			// If followed by {, skip the entire braced content
			if j < len(text) && text[j] == '{' {
				braceCount := 1
				j++
				for j < len(text) && braceCount > 0 {
					if text[j] == '{' {
						braceCount++
					} else if text[j] == '}' {
						braceCount--
					}
					j++
				}
				i = j // Skip the entire command
			} else {
				// Not a braced command, just skip the backslash and command
				i = j
			}
		} else {
			result.WriteByte(text[i])
			i++
		}
	}

	return result.String()
}

// cleanupWhitespace cleans up excessive whitespace and formatting
func cleanupWhitespace(text string) string {
	// First, normalize line breaks and remove arbitrary breaks within paragraphs
	text = normalizeLineBreaks(text)

	// Replace multiple newlines with double newlines (paragraph breaks)
	lines := strings.Split(text, "\n")
	var result []string
	var emptyLineCount int

	for _, line := range lines {
		trimmed := strings.TrimSpace(line)

		if trimmed == "" {
			emptyLineCount++
			if emptyLineCount <= 2 { // Allow max 2 consecutive empty lines
				result = append(result, "")
			}
		} else {
			emptyLineCount = 0
			result = append(result, trimmed)
		}
	}

	// Join and clean up final result
	final := strings.Join(result, "\n")
	final = strings.TrimSpace(final)

	// Replace multiple spaces with single spaces
	for strings.Contains(final, "  ") {
		final = strings.ReplaceAll(final, "  ", " ")
	}

	return final
}

// normalizeLineBreaks removes arbitrary line breaks and intelligently wraps text
func normalizeLineBreaks(text string) string {
	return normalizeLineBreaksWithWidth(text, 80)
}

// normalizeLineBreaksWithWidth removes arbitrary line breaks and wraps text at specified width
func normalizeLineBreaksWithWidth(text string, width int) string {
	lines := strings.Split(text, "\n")
	var paragraphs []string
	var currentParagraph strings.Builder
	inCodeBlock := false

	for _, line := range lines {
		trimmed := strings.TrimSpace(line)

		// Check for code block markers
		if trimmed == "```" {
			// Finish current paragraph before code block
			if currentParagraph.Len() > 0 {
				paragraphs = append(paragraphs, wrapText(currentParagraph.String(), width))
				currentParagraph.Reset()
			}
			paragraphs = append(paragraphs, line)
			inCodeBlock = !inCodeBlock
			continue
		}

		// If we're in a code block, preserve the line as-is
		if inCodeBlock {
			paragraphs = append(paragraphs, line)
			continue
		}

		// Empty line indicates paragraph break
		if trimmed == "" {
			if currentParagraph.Len() > 0 {
				paragraphs = append(paragraphs, wrapText(currentParagraph.String(), width))
				currentParagraph.Reset()
			}
			paragraphs = append(paragraphs, "")
		} else {
			// Add to current paragraph with space separation
			if currentParagraph.Len() > 0 {
				currentParagraph.WriteString(" ")
			}
			currentParagraph.WriteString(trimmed)
		}
	}

	// Don't forget the last paragraph
	if currentParagraph.Len() > 0 {
		paragraphs = append(paragraphs, wrapText(currentParagraph.String(), width))
	}

	return strings.Join(paragraphs, "\n")
}

// wrapText wraps text at the specified width, preserving word boundaries
func wrapText(text string, width int) string {
	if len(text) <= width {
		return text
	}

	words := strings.Fields(text)
	if len(words) == 0 {
		return text
	}

	var lines []string
	var currentLine strings.Builder

	for _, word := range words {
		// If adding this word would exceed the width, start a new line
		if currentLine.Len() > 0 && currentLine.Len()+1+len(word) > width {
			lines = append(lines, currentLine.String())
			currentLine.Reset()
		}

		// Add word to current line
		if currentLine.Len() > 0 {
			currentLine.WriteString(" ")
		}
		currentLine.WriteString(word)
	}

	// Don't forget the last line
	if currentLine.Len() > 0 {
		lines = append(lines, currentLine.String())
	}

	return strings.Join(lines, "\n")
}
