package circuits

import (
	"testing"
)

func TestSanitizeLatexDescription(t *testing.T) {
	tests := []struct {
		name     string
		input    string
		expected string
	}{
		{
			name:     "basic LaTeX commands",
			input:    `This is {\bf bold} and {\it italic} text`,
			expected: "This is bold and italic text",
		},
		{
			name:     "math symbols",
			input:    `Temperature: $25^\circ$C, infinity: $\infty$`,
			expected: "Temperature: 25°C, infinity: ∞",
		},
		{
			name:     "special characters",
			input:    `Use \& for and, \% for percent, \# for hash`,
			expected: "Use & for and, % for percent, # for hash",
		},
		{
			name:     "circuit references",
			input:    `The \droid \circuit{lfo} is powerful`,
			expected: "The DROID lfo is powerful",
		},
		{
			name: "patch examples conversion",
			input: `Here is an example:
\begin{droidini}
[lfo]
hz = 2
output = O1
\end{droidini}
That was the example.`,
			expected: `Here is an example:

` + "```" + `
[lfo]
hz = 2
output = O1
` + "```" + `

That was the example.`,
		},
		{
			name:     "complex LaTeX with multiple elements",
			input:    `{\bf Important:} Use $\pm 5V$ range. See \circuit{mixer} for details.`,
			expected: "Important: Use ± 5V range. See mixer for details.",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := SanitizeLatexDescription(tt.input)
			if result != tt.expected {
				t.Errorf("SanitizeLatexDescription() = %q, want %q", result, tt.expected)
			}
		})
	}
}

func TestSanitizeParameterDescription(t *testing.T) {
	tests := []struct {
		name     string
		input    string
		expected string
	}{
		{
			name: "remove arbitrary line breaks",
			input: `This is a long description that has
arbitrary line breaks in the middle of
sentences that should be joined together.`,
			expected: "This is a long description that has arbitrary line breaks in the middle of sentences that should be joined together.",
		},
		{
			name: "preserve paragraph breaks",
			input: `First paragraph with some text.

Second paragraph after empty line.`,
			expected: "First paragraph with some text. Second paragraph after empty line.",
		},
		{
			name:     "LaTeX commands removal",
			input:    `{\bf Bold} text with $math$ symbols`,
			expected: "Bold text with math symbols",
		},
		{
			name:     "multiple spaces cleanup",
			input:    "Text  with   multiple    spaces",
			expected: "Text with multiple spaces",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := SanitizeParameterDescription(tt.input)
			if result != tt.expected {
				t.Errorf("SanitizeParameterDescription() = %q, want %q", result, tt.expected)
			}
		})
	}
}

func TestNormalizeParameterText(t *testing.T) {
	tests := []struct {
		name     string
		input    string
		expected string
	}{
		{
			name: "join broken sentences",
			input: `This sentence is broken
across multiple lines
for no good reason.`,
			expected: "This sentence is broken across multiple lines for no good reason.",
		},
		{
			name:     "handle empty lines",
			input:    "Line one\n\n\nLine two",
			expected: "Line one Line two",
		},
		{
			name:     "preserve single words",
			input:    "word",
			expected: "word",
		},
		{
			name:     "empty input",
			input:    "",
			expected: "",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := normalizeParameterText(tt.input)
			if result != tt.expected {
				t.Errorf("normalizeParameterText() = %q, want %q", result, tt.expected)
			}
		})
	}
}

func TestConvertPatchExamples(t *testing.T) {
	tests := []struct {
		name     string
		input    string
		expected string
	}{
		{
			name: "basic patch example",
			input: `Text before
\begin{droidini}
[lfo]
hz = 2
\end{droidini}
Text after`,
			expected: `Text before

` + "```" + `
[lfo]
hz = 2
` + "```" + `

Text after
`,
		},
		{
			name: "patch example with immediate write",
			input: `Example:
\begin{droidini}
\immediate\write\patchexamples{[mixer]}
input1 = I1
\end{droidini}`,
			expected: `Example:

` + "```" + `
input1 = I1
` + "```" + `

`,
		},
		{
			name:     "no patch examples",
			input:    "Just regular text without examples",
			expected: "Just regular text without examples\n",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := convertPatchExamples(tt.input)
			if result != tt.expected {
				t.Errorf("convertPatchExamples() = %q, want %q", result, tt.expected)
			}
		})
	}
}

func TestRemoveLatexCommands(t *testing.T) {
	tests := []struct {
		name     string
		input    string
		expected string
	}{
		{
			name:     "text formatting",
			input:    `{\bf bold} {\it italic} {\footnotesize small}`,
			expected: "bold italic  small",
		},
		{
			name:     "math symbols",
			input:    `$\infty$ $^\circ$ $\times$ $\pm$`,
			expected: "∞ ° × ±",
		},
		{
			name:     "special characters",
			input:    `\& \% \# \_ \~`,
			expected: "& % # _  ",
		},
		{
			name:     "line breaks",
			input:    `Line one\\Line two`,
			expected: "Line one\nLine two",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := removeLatexCommands(tt.input)
			if result != tt.expected {
				t.Errorf("removeLatexCommands() = %q, want %q", result, tt.expected)
			}
		})
	}
}

func TestCleanupWhitespace(t *testing.T) {
	tests := []struct {
		name     string
		input    string
		expected string
	}{
		{
			name:     "multiple spaces",
			input:    "Text  with   multiple    spaces",
			expected: "Text with multiple spaces",
		},
		{
			name:     "excessive newlines",
			input:    "Line one\n\n\n\nLine two",
			expected: "Line one\n\n\nLine two",
		},
		{
			name:     "leading and trailing whitespace",
			input:    "   Text with spaces   ",
			expected: "Text with spaces",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := cleanupWhitespace(tt.input)
			if result != tt.expected {
				t.Errorf("cleanupWhitespace() = %q, want %q", result, tt.expected)
			}
		})
	}
}

func TestWrapText(t *testing.T) {
	tests := []struct {
		name     string
		input    string
		width    int
		expected string
	}{
		{
			name:     "basic wrapping",
			input:    "This is a long sentence that should be wrapped at word boundaries",
			width:    20,
			expected: "This is a long\nsentence that should\nbe wrapped at word\nboundaries",
		},
		{
			name:     "short text no wrapping",
			input:    "Short text",
			width:    20,
			expected: "Short text",
		},
		{
			name:     "single word longer than width",
			input:    "Supercalifragilisticexpialidocious",
			width:    10,
			expected: "Supercalifragilisticexpialidocious",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := wrapText(tt.input, tt.width)
			if result != tt.expected {
				t.Errorf("wrapText() = %q, want %q", result, tt.expected)
			}
		})
	}
}
