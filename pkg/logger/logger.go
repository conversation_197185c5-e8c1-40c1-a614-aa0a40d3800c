package logger

import (
	"io"
	"os"
	"strings"

	"github.com/sirupsen/logrus"
)

var (
	// Logger is the global logger instance
	Logger *logrus.Logger
)

// LogLevel represents available log levels
type LogLevel string

const (
	TraceLevel LogLevel = "trace"
	DebugLevel LogLevel = "debug"
	InfoLevel  LogLevel = "info"
	WarnLevel  LogLevel = "warn"
	ErrorLevel LogLevel = "error"
	FatalLevel LogLevel = "fatal"
	PanicLevel LogLevel = "panic"
)

// ValidLogLevels returns a slice of all valid log levels
func ValidLogLevels() []string {
	return []string{
		string(TraceLevel),
		string(DebugLevel),
		string(InfoLevel),
		string(WarnLevel),
		string(ErrorLevel),
		string(FatalLevel),
		string(PanicLevel),
	}
}

// ParseLogLevel converts a string to a logrus.Level
func ParseLogLevel(level string) (logrus.Level, error) {
	switch strings.ToLower(level) {
	case string(TraceLevel):
		return logrus.TraceLevel, nil
	case string(DebugLevel):
		return logrus.DebugLevel, nil
	case string(InfoLevel):
		return logrus.InfoLevel, nil
	case string(WarnLevel):
		return logrus.WarnLevel, nil
	case string(ErrorLevel):
		return logrus.ErrorLevel, nil
	case string(FatalLevel):
		return logrus.FatalLevel, nil
	case string(PanicLevel):
		return logrus.PanicLevel, nil
	default:
		// Try logrus's own parser as fallback
		return logrus.ParseLevel(level)
	}
}

// InitLogger initializes the global logger with the specified configuration
func InitLogger(level string, quiet bool) error {
	Logger = logrus.New()

	// Parse log level
	logLevel, err := ParseLogLevel(level)
	if err != nil {
		return err
	}
	Logger.SetLevel(logLevel)

	// Set output destination
	if quiet {
		// In quiet mode, only show errors and above
		if logLevel < logrus.ErrorLevel {
			Logger.SetLevel(logrus.ErrorLevel)
		}
	}

	// Set formatter based on log level and terminal
	if logLevel <= logrus.DebugLevel {
		// Use detailed formatter for debug/trace
		Logger.SetFormatter(&logrus.TextFormatter{
			FullTimestamp:   true,
			TimestampFormat: "15:04:05",
			ForceColors:     true,
			PadLevelText:    true,
		})
	} else {
		// Use simpler formatter for info and above
		Logger.SetFormatter(&CustomFormatter{
			ShowTimestamp: false,
			ShowLevel:     logLevel <= logrus.WarnLevel,
		})
	}

	return nil
}

// CustomFormatter provides clean output for user-facing messages
type CustomFormatter struct {
	ShowTimestamp bool
	ShowLevel     bool
}

// Format implements the logrus.Formatter interface
func (f *CustomFormatter) Format(entry *logrus.Entry) ([]byte, error) {
	var message strings.Builder

	// Add timestamp if requested
	if f.ShowTimestamp {
		message.WriteString("[")
		message.WriteString(entry.Time.Format("15:04:05"))
		message.WriteString("] ")
	}

	// Add level if requested or if it's warning/error
	if f.ShowLevel || entry.Level <= logrus.WarnLevel {
		switch entry.Level {
		case logrus.ErrorLevel:
			message.WriteString("ERROR: ")
		case logrus.WarnLevel:
			message.WriteString("WARN: ")
		case logrus.FatalLevel:
			message.WriteString("FATAL: ")
		case logrus.PanicLevel:
			message.WriteString("PANIC: ")
		case logrus.DebugLevel:
			message.WriteString("DEBUG: ")
		case logrus.TraceLevel:
			message.WriteString("TRACE: ")
		}
	}

	// Add the message
	message.WriteString(entry.Message)
	message.WriteString("\n")

	return []byte(message.String()), nil
}

// SetOutput sets the logger output destination
func SetOutput(w io.Writer) {
	if Logger != nil {
		Logger.SetOutput(w)
	}
}

// GetLogger returns the global logger instance
func GetLogger() *logrus.Logger {
	if Logger == nil {
		// Initialize with default settings if not already initialized
		InitLogger("info", false)
	}
	return Logger
}

// Convenience functions for common logging operations

// Info logs an info message
func Info(args ...interface{}) {
	GetLogger().Info(args...)
}

// Infof logs a formatted info message
func Infof(format string, args ...interface{}) {
	GetLogger().Infof(format, args...)
}

// Debug logs a debug message
func Debug(args ...interface{}) {
	GetLogger().Debug(args...)
}

// Debugf logs a formatted debug message
func Debugf(format string, args ...interface{}) {
	GetLogger().Debugf(format, args...)
}

// Warn logs a warning message
func Warn(args ...interface{}) {
	GetLogger().Warn(args...)
}

// Warnf logs a formatted warning message
func Warnf(format string, args ...interface{}) {
	GetLogger().Warnf(format, args...)
}

// Error logs an error message
func Error(args ...interface{}) {
	GetLogger().Error(args...)
}

// Errorf logs a formatted error message
func Errorf(format string, args ...interface{}) {
	GetLogger().Errorf(format, args...)
}

// Fatal logs a fatal message and exits
func Fatal(args ...interface{}) {
	GetLogger().Fatal(args...)
}

// Fatalf logs a formatted fatal message and exits
func Fatalf(format string, args ...interface{}) {
	GetLogger().Fatalf(format, args...)
}

// WithField creates a logger entry with a single field
func WithField(key string, value interface{}) *logrus.Entry {
	return GetLogger().WithField(key, value)
}

// WithFields creates a logger entry with multiple fields
func WithFields(fields logrus.Fields) *logrus.Entry {
	return GetLogger().WithFields(fields)
}

// WithError creates a logger entry with an error field
func WithError(err error) *logrus.Entry {
	return GetLogger().WithError(err)
}

// SetQuietMode adjusts the logger for quiet operation
func SetQuietMode(quiet bool) {
	logger := GetLogger()
	if quiet {
		logger.SetOutput(io.Discard)
	} else {
		logger.SetOutput(os.Stderr)
	}
}
